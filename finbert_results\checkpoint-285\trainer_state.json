{"best_global_step": 285, "best_metric": 0.8240544807963432, "best_model_checkpoint": "./finbert_results\\checkpoint-285", "epoch": 3.0, "eval_steps": 500, "global_step": 285, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.5263157894736842, "grad_norm": 9.962910652160645, "learning_rate": 9.800000000000001e-06, "loss": 1.5783, "step": 50}, {"epoch": 1.0, "eval_accuracy": 0.7556390977443609, "eval_f1": 0.7356553071990409, "eval_f1_negative": 0.0, "eval_f1_neutral": 0.8072289156626506, "eval_f1_positive": 0.7127659574468085, "eval_loss": 0.5782700777053833, "eval_model_preparation_time": 0.0022, "eval_precision": 0.721326025022564, "eval_precision_negative": 0.0, "eval_precision_neutral": 0.7570621468926554, "eval_precision_positive": 0.7528089887640449, "eval_recall": 0.7556390977443609, "eval_recall_negative": 0.0, "eval_recall_neutral": 0.864516129032258, "eval_recall_positive": 0.6767676767676768, "eval_runtime": 74.3015, "eval_samples_per_second": 3.58, "eval_steps_per_second": 0.229, "step": 95}, {"epoch": 1.0526315789473684, "grad_norm": 5.602410316467285, "learning_rate": 1.98e-05, "loss": 0.6335, "step": 100}, {"epoch": 1.5789473684210527, "grad_norm": 3.330679416656494, "learning_rate": 1.4702702702702705e-05, "loss": 0.4322, "step": 150}, {"epoch": 2.0, "eval_accuracy": 0.8233082706766918, "eval_f1": 0.804538058269949, "eval_f1_negative": 0.0, "eval_f1_neutral": 0.8634920634920635, "eval_f1_positive": 0.8097560975609757, "eval_loss": 0.46892958879470825, "eval_model_preparation_time": 0.0022, "eval_precision": 0.7867250673854448, "eval_precision_negative": 0.0, "eval_precision_neutral": 0.85, "eval_precision_positive": 0.7830188679245284, "eval_recall": 0.8233082706766918, "eval_recall_negative": 0.0, "eval_recall_neutral": 0.8774193548387097, "eval_recall_positive": 0.8383838383838383, "eval_runtime": 83.7014, "eval_samples_per_second": 3.178, "eval_steps_per_second": 0.203, "step": 190}, {"epoch": 2.1052631578947367, "grad_norm": 2.2615716457366943, "learning_rate": 9.297297297297299e-06, "loss": 0.3408, "step": 200}, {"epoch": 2.6315789473684212, "grad_norm": 16.959030151367188, "learning_rate": 3.891891891891892e-06, "loss": 0.2563, "step": 250}, {"epoch": 3.0, "eval_accuracy": 0.8383458646616542, "eval_f1": 0.8240544807963432, "eval_f1_negative": 0.15384615384615385, "eval_f1_neutral": 0.8726114649681529, "eval_f1_positive": 0.8292682926829268, "eval_loss": 0.49527400732040405, "eval_model_preparation_time": 0.0022, "eval_precision": 0.8456400435049889, "eval_precision_negative": 1.0, "eval_precision_neutral": 0.8616352201257862, "eval_precision_positive": 0.8018867924528302, "eval_recall": 0.8383458646616542, "eval_recall_negative": 0.08333333333333333, "eval_recall_neutral": 0.8838709677419355, "eval_recall_positive": 0.8585858585858586, "eval_runtime": 80.4719, "eval_samples_per_second": 3.306, "eval_steps_per_second": 0.211, "step": 285}], "logging_steps": 50, "max_steps": 285, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"EarlyStoppingCallback": {"args": {"early_stopping_patience": 2, "early_stopping_threshold": 0.001}, "attributes": {"early_stopping_patience_counter": 0}}, "TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 1190325101801472.0, "train_batch_size": 16, "trial_name": null, "trial_params": null}