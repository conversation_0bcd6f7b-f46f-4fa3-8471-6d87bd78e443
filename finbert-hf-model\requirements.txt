# FinBERT Financial Sentiment Analysis - Hugging Face Optimized
# Minimal requirements for HF ecosystem

# Core ML libraries
torch>=2.0.0
transformers>=4.30.0
datasets>=2.12.0
tokenizers>=0.13.0

# Data processing
pandas>=1.5.0
numpy>=1.24.0
scikit-learn>=1.3.0

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0

# Web framework (for deployment)
Flask>=2.3.0

# Utilities
tqdm>=4.65.0

# Optional: Jupyter for notebooks
jupyter>=1.0.0
