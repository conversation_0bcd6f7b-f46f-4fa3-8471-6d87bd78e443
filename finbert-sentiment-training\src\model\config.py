"""
FinBERT model configuration and setup.
"""

from transformers import AutoModelForSequenceClassification, AutoTokenizer
import torch
import torch.nn as nn
from typing import Dict, Optional

class FinBERTConfig:
    """Configuration for FinBERT model."""
    
    def __init__(self, 
                 model_name: str = "ProsusAI/finbert",
                 num_labels: int = 3,
                 dropout_rate: float = 0.1):
        self.model_name = model_name
        self.num_labels = num_labels
        self.dropout_rate = dropout_rate
        
    def to_dict(self) -> Dict:
        """Convert config to dictionary."""
        return {
            "model_name": self.model_name,
            "num_labels": self.num_labels,
            "dropout_rate": self.dropout_rate
        }

def get_model_and_tokenizer(config: FinBERTConfig, device: Optional[str] = None):
    """
    Load FinBERT model and tokenizer.
    
    Args:
        config: FinBERT configuration
        device: Device to load model on
        
    Returns:
        Tuple of (model, tokenizer)
    """
    if device is None:
        device = "cuda" if torch.cuda.is_available() else "cpu"
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(config.model_name)
    
    # Load model
    model = AutoModelForSequenceClassification.from_pretrained(
        config.model_name,
        num_labels=config.num_labels,
        problem_type="single_label_classification"
    )
    
    # Move to device
    model = model.to(device)
    
    return model, tokenizer
