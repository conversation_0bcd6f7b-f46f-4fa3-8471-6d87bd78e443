// FinBERT Sentiment Analyzer Frontend JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // DOM elements
    const textInput = document.getElementById('textInput');
    const analysisForm = document.getElementById('analysisForm');
    const analyzeBtn = document.getElementById('analyzeBtn');
    const charCount = document.getElementById('charCount');
    const wordCount = document.getElementById('wordCount');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const resultsContainer = document.getElementById('resultsContainer');
    const errorContainer = document.getElementById('errorContainer');
    const sampleTextButtons = document.querySelectorAll('.sample-text');

    // Initialize
    updateCounts();
    
    // Event listeners
    textInput.addEventListener('input', updateCounts);
    analysisForm.addEventListener('submit', handleSubmit);
    
    // Sample text buttons
    sampleTextButtons.forEach(button => {
        button.addEventListener('click', function() {
            const sampleText = this.getAttribute('data-text');
            textInput.value = sampleText;
            updateCounts();
            textInput.focus();
        });
    });

    function updateCounts() {
        const text = textInput.value;
        const chars = text.length;
        const words = text.trim() ? text.trim().split(/\s+/).length : 0;
        
        charCount.textContent = chars;
        wordCount.textContent = words;
        
        // Update button state
        if (chars < 10) {
            analyzeBtn.disabled = true;
            analyzeBtn.innerHTML = '<i class="fas fa-brain me-2"></i>Enter more text to analyze';
        } else {
            analyzeBtn.disabled = false;
            analyzeBtn.innerHTML = '<i class="fas fa-brain me-2"></i>Analyze Sentiment';
        }
    }

    async function handleSubmit(e) {
        e.preventDefault();
        
        const text = textInput.value.trim();
        if (!text || text.length < 10) {
            showError('Please enter at least 10 characters for analysis.');
            return;
        }

        // Show loading state
        showLoading();
        
        try {
            const response = await fetch('/analyze', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ text: text })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Analysis failed');
            }

            showResults(data);
            
        } catch (error) {
            console.error('Error:', error);
            showError(error.message || 'An error occurred during analysis.');
        }
    }

    function showLoading() {
        hideAllContainers();
        loadingSpinner.classList.remove('d-none');
        analyzeBtn.disabled = true;
        analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing...';
    }

    function showResults(data) {
        hideAllContainers();
        
        // Update sentiment display
        updateSentimentDisplay(data);
        
        // Update probability bars
        updateProbabilityBars(data.probabilities);
        
        // Update summary
        updateSummary(data.summary);
        
        // Update justifications
        updateJustifications(data.justifications);
        
        // Show results container with animation
        resultsContainer.classList.remove('d-none');
        resultsContainer.classList.add('fade-in-up');
        
        // Reset button
        resetButton();
    }

    function showError(message) {
        hideAllContainers();
        document.getElementById('errorMessage').textContent = message;
        errorContainer.classList.remove('d-none');
        resetButton();
    }

    function hideAllContainers() {
        loadingSpinner.classList.add('d-none');
        resultsContainer.classList.add('d-none');
        errorContainer.classList.add('d-none');
    }

    function resetButton() {
        analyzeBtn.disabled = false;
        analyzeBtn.innerHTML = '<i class="fas fa-brain me-2"></i>Analyze Sentiment';
    }

    function updateSentimentDisplay(data) {
        const sentimentAlert = document.getElementById('sentimentAlert');
        const sentimentIcon = document.getElementById('sentimentIcon');
        const sentimentLabel = document.getElementById('sentimentLabel');
        const confidenceScore = document.getElementById('confidenceScore');
        
        // Remove existing classes
        sentimentAlert.className = 'alert';
        
        // Add sentiment-specific styling
        const sentiment = data.sentiment.toLowerCase();
        sentimentAlert.classList.add(`alert-${getSentimentBootstrapClass(sentiment)}`);
        sentimentAlert.classList.add(`sentiment-${sentiment}`);
        
        // Update icon
        sentimentIcon.className = getSentimentIcon(sentiment);
        
        // Update text
        sentimentLabel.textContent = data.sentiment;
        confidenceScore.textContent = `${(data.confidence * 100).toFixed(1)}%`;
    }

    function updateProbabilityBars(probabilities) {
        const container = document.getElementById('probabilityBars');
        container.innerHTML = '';
        
        const sentiments = ['Negative', 'Neutral', 'Positive'];
        const colors = ['negative', 'neutral', 'positive'];
        
        sentiments.forEach((sentiment, index) => {
            const probability = probabilities[sentiment];
            const percentage = (probability * 100).toFixed(1);
            
            const barHtml = `
                <div class="probability-bar">
                    <div class="probability-label">
                        <span><i class="${getSentimentIcon(sentiment.toLowerCase())} me-1"></i>${sentiment}</span>
                        <span>${percentage}%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar progress-bar-${colors[index]}" 
                             role="progressbar" 
                             style="width: ${percentage}%"
                             aria-valuenow="${percentage}" 
                             aria-valuemin="0" 
                             aria-valuemax="100">
                        </div>
                    </div>
                </div>
            `;
            
            container.innerHTML += barHtml;
        });
    }

    function updateSummary(summary) {
        const summaryText = document.getElementById('summaryText');
        summaryText.textContent = summary;
    }

    function updateJustifications(justifications) {
        const container = document.getElementById('justificationText');
        container.innerHTML = '';
        
        if (!justifications || justifications.length === 0) {
            container.innerHTML = '<p class="text-muted">No specific justifications available.</p>';
            return;
        }
        
        justifications.forEach(justification => {
            const justificationHtml = `
                <div class="justification-item ${justification.class}">
                    <i class="${justification.icon} me-2"></i>
                    ${justification.text}
                </div>
            `;
            container.innerHTML += justificationHtml;
        });
    }

    function getSentimentIcon(sentiment) {
        switch(sentiment) {
            case 'positive':
                return 'fas fa-arrow-up text-success';
            case 'negative':
                return 'fas fa-arrow-down text-danger';
            case 'neutral':
                return 'fas fa-minus text-warning';
            default:
                return 'fas fa-question text-secondary';
        }
    }

    function getSentimentBootstrapClass(sentiment) {
        switch(sentiment) {
            case 'positive':
                return 'success';
            case 'negative':
                return 'danger';
            case 'neutral':
                return 'warning';
            default:
                return 'secondary';
        }
    }

    // Add smooth scrolling to results when they appear
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && 
                mutation.attributeName === 'class' && 
                !resultsContainer.classList.contains('d-none')) {
                
                setTimeout(() => {
                    resultsContainer.scrollIntoView({ 
                        behavior: 'smooth', 
                        block: 'start' 
                    });
                }, 100);
            }
        });
    });
    
    observer.observe(resultsContainer, { attributes: true });
});
