"""
DataLoader module for FinBERT sentiment analysis.
Handles dataset loading, tokenization, and batch creation.
"""

import torch
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer
from datasets import Dataset as HFDataset
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
import logging

logger = logging.getLogger(__name__)

class FinancialSentimentDataset(Dataset):
    """
    PyTorch Dataset for financial sentiment analysis.
    """
    
    def __init__(self, 
                 texts: List[str],
                 labels: List[int],
                 tokenizer: AutoTokenizer,
                 max_length: int = 512):
        """
        Initialize the dataset.
        
        Args:
            texts: List of text samples
            labels: List of corresponding labels
            tokenizer: Tokenizer to use for encoding
            max_length: Maximum sequence length
        """
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
        
        assert len(texts) == len(labels), "Texts and labels must have same length"
    
    def __len__(self) -> int:
        return len(self.texts)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        Get a single sample from the dataset.
        
        Args:
            idx: Index of the sample
            
        Returns:
            Dictionary with tokenized inputs and labels
        """
        text = str(self.texts[idx])
        label = int(self.labels[idx])
        
        # Tokenize text
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'token_type_ids': encoding.get('token_type_ids', torch.zeros_like(encoding['input_ids'])).flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

def create_dataloaders(train_df: pd.DataFrame,
                      val_df: Optional[pd.DataFrame],
                      test_df: pd.DataFrame,
                      tokenizer: AutoTokenizer,
                      text_column: str = 'cleaned_text',
                      label_column: str = 'labels',
                      max_length: int = 512,
                      batch_size: int = 16,
                      num_workers: int = 2) -> Tuple[DataLoader, Optional[DataLoader], DataLoader]:
    """
    Create DataLoaders for training, validation, and testing.
    
    Args:
        train_df: Training DataFrame
        val_df: Validation DataFrame (optional)
        test_df: Test DataFrame
        tokenizer: Tokenizer to use
        text_column: Name of text column
        label_column: Name of label column
        max_length: Maximum sequence length
        batch_size: Batch size
        num_workers: Number of worker processes
        
    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    logger.info("Creating datasets...")
    
    # Create train dataset
    train_dataset = FinancialSentimentDataset(
        texts=train_df[text_column].tolist(),
        labels=train_df[label_column].tolist(),
        tokenizer=tokenizer,
        max_length=max_length
    )
    
    # Create validation dataset if provided
    val_dataset = None
    if val_df is not None and len(val_df) > 0:
        val_dataset = FinancialSentimentDataset(
            texts=val_df[text_column].tolist(),
            labels=val_df[label_column].tolist(),
            tokenizer=tokenizer,
            max_length=max_length
        )
    
    # Create test dataset
    test_dataset = FinancialSentimentDataset(
        texts=test_df[text_column].tolist(),
        labels=test_df[label_column].tolist(),
        tokenizer=tokenizer,
        max_length=max_length
    )
    
    logger.info("Creating DataLoaders...")
    
    # Create DataLoaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available()
    )
    
    val_loader = None
    if val_dataset is not None:
        val_loader = DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=torch.cuda.is_available()
        )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available()
    )
    
    logger.info(f"DataLoaders created:")
    logger.info(f"  Train: {len(train_loader)} batches")
    if val_loader:
        logger.info(f"  Validation: {len(val_loader)} batches")
    logger.info(f"  Test: {len(test_loader)} batches")
    
    return train_loader, val_loader, test_loader

def create_hf_datasets(train_df: pd.DataFrame,
                      val_df: Optional[pd.DataFrame],
                      test_df: pd.DataFrame,
                      tokenizer: AutoTokenizer,
                      text_column: str = 'cleaned_text',
                      label_column: str = 'labels',
                      max_length: int = 512) -> Dict[str, HFDataset]:
    """
    Create Hugging Face datasets for use with Trainer.
    
    Args:
        train_df: Training DataFrame
        val_df: Validation DataFrame (optional)
        test_df: Test DataFrame
        tokenizer: Tokenizer to use
        text_column: Name of text column
        label_column: Name of label column
        max_length: Maximum sequence length
        
    Returns:
        Dictionary with datasets
    """
    def tokenize_function(examples):
        """Tokenization function for dataset mapping."""
        return tokenizer(
            examples[text_column],
            truncation=True,
            padding='max_length',
            max_length=max_length,
            return_tensors='pt'
        )
    
    logger.info("Creating Hugging Face datasets...")
    
    # Create datasets
    datasets = {}
    
    # Train dataset
    train_dataset = HFDataset.from_pandas(train_df[[text_column, label_column]])
    train_dataset = train_dataset.map(tokenize_function, batched=True)
    train_dataset = train_dataset.rename_column(label_column, 'labels')
    datasets['train'] = train_dataset
    
    # Validation dataset
    if val_df is not None and len(val_df) > 0:
        val_dataset = HFDataset.from_pandas(val_df[[text_column, label_column]])
        val_dataset = val_dataset.map(tokenize_function, batched=True)
        val_dataset = val_dataset.rename_column(label_column, 'labels')
        datasets['validation'] = val_dataset
    
    # Test dataset
    test_dataset = HFDataset.from_pandas(test_df[[text_column, label_column]])
    test_dataset = test_dataset.map(tokenize_function, batched=True)
    test_dataset = test_dataset.rename_column(label_column, 'labels')
    datasets['test'] = test_dataset
    
    # Set format for PyTorch
    for split_name, dataset in datasets.items():
        dataset.set_format('torch', columns=['input_ids', 'attention_mask', 'labels'])
        logger.info(f"{split_name.capitalize()} dataset: {len(dataset)} samples")
    
    return datasets

def load_data_from_csv(file_path: str,
                      text_column: str,
                      label_column: str,
                      sample_size: Optional[int] = None) -> pd.DataFrame:
    """
    Load data from CSV file.
    
    Args:
        file_path: Path to CSV file
        text_column: Name of text column
        label_column: Name of label column
        sample_size: Optional sample size for testing
        
    Returns:
        Loaded DataFrame
    """
    logger.info(f"Loading data from {file_path}")
    
    df = pd.read_csv(file_path)
    
    # Check if required columns exist
    if text_column not in df.columns:
        raise ValueError(f"Text column '{text_column}' not found in CSV")
    if label_column not in df.columns:
        raise ValueError(f"Label column '{label_column}' not found in CSV")
    
    # Sample data if requested
    if sample_size and sample_size < len(df):
        df = df.sample(n=sample_size, random_state=42)
        logger.info(f"Sampled {sample_size} rows from dataset")
    
    logger.info(f"Loaded dataset with {len(df)} rows")
    
    return df
