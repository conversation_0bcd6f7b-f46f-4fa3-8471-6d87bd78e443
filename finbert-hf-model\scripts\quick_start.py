"""
Quick Start Script for FinBERT HF Model

This script provides a simple way to get started with the FinBERT model
using Hugging Face pipelines.

Usage:
    python scripts/quick_start.py
"""

import sys
from pathlib import Path

def check_requirements():
    """Check if required packages are installed"""
    try:
        import transformers
        import torch
        print(f"✅ transformers version: {transformers.__version__}")
        print(f"✅ torch version: {torch.__version__}")
        return True
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        print("Install with: pip install -r requirements.txt")
        return False

def test_model_loading():
    """Test loading the model from local files"""
    model_path = Path("./model_files")
    
    if not model_path.exists():
        print(f"❌ Model directory not found: {model_path}")
        print("💡 Run the conversion script first: python scripts/convert_model.py --auto_find")
        return False
    
    try:
        from transformers import pipeline
        
        print("📤 Loading model from local files...")
        classifier = pipeline("text-classification", model=str(model_path))
        
        print("✅ Model loaded successfully!")
        return classifier
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None

def test_online_model():
    """Test loading model from Hugging Face Hub"""
    try:
        from transformers import pipeline
        
        # Use base FinBERT model as fallback
        model_name = "ProsusAI/finbert"
        print(f"📤 Loading model from HF Hub: {model_name}")
        
        classifier = pipeline("text-classification", model=model_name)
        print("✅ Online model loaded successfully!")
        
        return classifier
        
    except Exception as e:
        print(f"❌ Error loading online model: {e}")
        return None

def run_demo(classifier):
    """Run a demo with sample financial texts"""
    print("\n🚀 Running Financial Sentiment Analysis Demo")
    print("=" * 50)
    
    # Sample financial texts
    test_texts = [
        "The company reported record quarterly profits, exceeding analyst expectations by 15%.",
        "Market volatility increased due to concerns about inflation and interest rates.",
        "The quarterly earnings report will be released next Tuesday according to schedule.",
        "Stock prices plummeted following disappointing revenue figures and weak guidance.",
        "The merger is expected to create significant synergies and cost savings.",
        "Trading was suspended due to technical difficulties on the exchange.",
        "The Federal Reserve announced a 0.5% interest rate hike to combat inflation.",
        "Investors remain optimistic about the company's long-term growth prospects."
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n📝 Example {i}:")
        print(f"Text: {text}")
        
        try:
            result = classifier(text)
            label = result[0]['label']
            score = result[0]['score']
            
            # Format output with emoji
            emoji_map = {
                'POSITIVE': '📈',
                'NEGATIVE': '📉', 
                'NEUTRAL': '📊',
                'positive': '📈',
                'negative': '📉',
                'neutral': '📊'
            }
            
            emoji = emoji_map.get(label.upper(), '📊')
            print(f"Sentiment: {emoji} {label} (Confidence: {score:.3f})")
            
        except Exception as e:
            print(f"❌ Error analyzing text: {e}")

def interactive_mode(classifier):
    """Interactive mode for user input"""
    print("\n🎯 Interactive Mode")
    print("=" * 30)
    print("Enter financial texts to analyze (type 'quit' to exit):")
    
    while True:
        try:
            text = input("\n💬 Enter text: ").strip()
            
            if text.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
                
            if not text:
                print("⚠️  Please enter some text")
                continue
            
            result = classifier(text)
            label = result[0]['label']
            score = result[0]['score']
            
            # Format with emoji
            emoji_map = {
                'POSITIVE': '📈', 'NEGATIVE': '📉', 'NEUTRAL': '📊',
                'positive': '📈', 'negative': '📉', 'neutral': '📊'
            }
            emoji = emoji_map.get(label.upper(), '📊')
            
            print(f"   Result: {emoji} {label} (Confidence: {score:.3f})")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    print("🤖 FinBERT Financial Sentiment Analysis - Quick Start")
    print("=" * 55)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Try to load model
    classifier = test_model_loading()
    
    if not classifier:
        print("\n⚠️  Local model not available, trying online model...")
        classifier = test_online_model()
    
    if not classifier:
        print("\n❌ Could not load any model. Please check your setup.")
        sys.exit(1)
    
    # Run demo
    run_demo(classifier)
    
    # Interactive mode
    try:
        interactive_mode(classifier)
    except KeyboardInterrupt:
        print("\n👋 Thanks for trying FinBERT!")

if __name__ == "__main__":
    main()
