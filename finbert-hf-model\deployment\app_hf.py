"""
FinBERT Deployment - Hugging Face Optimized
Simple Flask API for FinBERT sentiment analysis using HF pipeline.
"""

from flask import Flask, render_template, request, jsonify
from transformers import pipeline, AutoModelForSequenceClassification, AutoTokenizer
import torch
import logging
import os
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

class FinBERTSentimentAnalyzer:
    """Simple FinBERT sentiment analyzer using HF pipeline."""
    
    def __init__(self, model_path="../model_files"):
        """Initialize with Hugging Face model."""
        self.model_path = model_path
        self.classifier = None
        self.label_mapping = {
            'LABEL_0': 'Negative',
            'LABEL_1': 'Neutral', 
            'LABEL_2': 'Positive'
        }
        self.load_model()
    
    def load_model(self):
        """Load the FinBERT model using HF pipeline."""
        try:
            logger.info(f"Loading FinBERT model from {self.model_path}")
            
            # Check if model exists
            if not os.path.exists(self.model_path):
                logger.warning(f"Model path {self.model_path} not found. Using base FinBERT.")
                model_path = "ProsusAI/finbert"
            else:
                model_path = self.model_path
            
            # Create pipeline
            self.classifier = pipeline(
                "text-classification",
                model=model_path,
                tokenizer=model_path,
                device=0 if torch.cuda.is_available() else -1,
                return_all_scores=True
            )
            
            logger.info("✅ FinBERT model loaded successfully")
            
        except Exception as e:
            logger.error(f"❌ Error loading model: {e}")
            # Fallback to base FinBERT
            try:
                logger.info("🔄 Falling back to base FinBERT model...")
                self.classifier = pipeline(
                    "text-classification",
                    model="ProsusAI/finbert",
                    device=0 if torch.cuda.is_available() else -1,
                    return_all_scores=True
                )
                logger.info("✅ Base FinBERT model loaded successfully")
            except Exception as e2:
                logger.error(f"❌ Failed to load any model: {e2}")
                raise
    
    def predict(self, text):
        """
        Predict sentiment for given text.
        
        Args:
            text: Input text for sentiment analysis
            
        Returns:
            Dictionary with prediction results
        """
        if not self.classifier:
            raise ValueError("Model not loaded")
        
        try:
            # Get prediction with all scores
            results = self.classifier(text)
            
            # Extract scores for all classes
            scores = {}
            predicted_label = None
            max_score = 0
            
            for result in results:
                label = result['label']
                score = result['score']
                sentiment = self.label_mapping.get(label, label)
                scores[sentiment] = score
                
                if score > max_score:
                    max_score = score
                    predicted_label = sentiment
            
            return {
                'text': text,
                'predicted_sentiment': predicted_label,
                'confidence': max_score,
                'all_scores': scores,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Prediction error: {e}")
            return {
                'error': str(e),
                'text': text,
                'timestamp': datetime.now().isoformat()
            }

# Initialize analyzer
try:
    analyzer = FinBERTSentimentAnalyzer()
except Exception as e:
    logger.error(f"Failed to initialize analyzer: {e}")
    analyzer = None

@app.route('/')
def home():
    """Home page with simple interface."""
    return render_template('index.html')

@app.route('/predict', methods=['POST'])
def predict():
    """API endpoint for sentiment prediction."""
    if not analyzer:
        return jsonify({'error': 'Model not available'}), 500
    
    try:
        data = request.get_json()
        text = data.get('text', '')
        
        if not text or not text.strip():
            return jsonify({'error': 'No text provided'}), 400
        
        # Clean text
        text = text.strip()
        if len(text) > 10000:  # Limit text length
            text = text[:10000]
        
        # Get prediction
        result = analyzer.predict(text)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Prediction endpoint error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/batch_predict', methods=['POST'])
def batch_predict():
    """API endpoint for batch sentiment prediction."""
    if not analyzer:
        return jsonify({'error': 'Model not available'}), 500
    
    try:
        data = request.get_json()
        texts = data.get('texts', [])
        
        if not texts or not isinstance(texts, list):
            return jsonify({'error': 'No texts provided or invalid format'}), 400
        
        if len(texts) > 100:  # Limit batch size
            return jsonify({'error': 'Batch size too large (max 100)'}), 400
        
        results = []
        for text in texts:
            if text and text.strip():
                result = analyzer.predict(text.strip())
                results.append(result)
        
        return jsonify({
            'results': results,
            'count': len(results),
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Batch prediction error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/health')
def health():
    """Health check endpoint."""
    status = {
        'status': 'healthy' if analyzer else 'unhealthy',
        'model_loaded': analyzer is not None,
        'timestamp': datetime.now().isoformat()
    }
    
    if analyzer:
        status['model_path'] = analyzer.model_path
    
    return jsonify(status)

@app.route('/api/info')
def model_info():
    """Get model information."""
    info = {
        'model_name': 'FinBERT Financial Sentiment Analyzer',
        'model_type': 'Hugging Face Transformer',
        'base_model': 'ProsusAI/finbert',
        'labels': list(analyzer.label_mapping.values()) if analyzer else [],
        'device': 'GPU' if torch.cuda.is_available() else 'CPU',
        'timestamp': datetime.now().isoformat()
    }
    return jsonify(info)

if __name__ == '__main__':
    print("🚀 Starting FinBERT Sentiment Analysis API (HF Optimized)")
    print(f"📁 Model path: {analyzer.model_path if analyzer else 'Not loaded'}")
    print(f"🖥️  Device: {'GPU' if torch.cuda.is_available() else 'CPU'}")
    print("🌐 Server starting on http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
