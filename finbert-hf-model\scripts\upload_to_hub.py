"""
Upload FinBERT model to Hugging Face Hub

This script helps you upload your trained FinBERT model to the Hugging Face Hub
for easy sharing and deployment.

Usage:
    python scripts/upload_to_hub.py --model_path ./model_files --repo_name your-username/finbert-financial-sentiment
"""

import argparse
import os
import sys
from pathlib import Path

def check_requirements():
    """Check if required packages are installed"""
    try:
        import huggingface_hub
        from transformers import AutoTokenizer, AutoModelForSequenceClassification
        print("✅ All required packages are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing required package: {e}")
        print("Install with: pip install huggingface_hub transformers")
        return False

def validate_model_files(model_path):
    """Validate that all required model files exist"""
    model_path = Path(model_path)
    required_files = [
        "config.json",
        "tokenizer.json", 
        "tokenizer_config.json",
        "special_tokens_map.json",
        "vocab.txt"
    ]
    
    # Check for model weights (either .safetensors or .bin)
    has_weights = (model_path / "model.safetensors").exists() or (model_path / "pytorch_model.bin").exists()
    
    missing_files = []
    for file in required_files:
        if not (model_path / file).exists():
            missing_files.append(file)
    
    if not has_weights:
        missing_files.append("model.safetensors or pytorch_model.bin")
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    
    print("✅ All required model files found")
    return True

def create_model_card(repo_path, model_name):
    """Create or update model card"""
    model_card_path = repo_path / "README.md"
    
    if model_card_path.exists():
        print("📝 Model card already exists, skipping creation")
        return
    
    # Copy from MODEL_CARD.md if it exists
    source_card = Path("MODEL_CARD.md")
    if source_card.exists():
        import shutil
        shutil.copy2(source_card, model_card_path)
        print("📝 Copied MODEL_CARD.md to README.md")
    else:
        # Create basic model card
        with open(model_card_path, "w") as f:
            f.write(f"""# {model_name}

This model is a fine-tuned version of ProsusAI/finbert for financial sentiment analysis.

## Usage

```python
from transformers import pipeline

classifier = pipeline("text-classification", model="{model_name}")
result = classifier("The company reported strong quarterly earnings.")
print(result)
```

## Training

This model was fine-tuned on financial texts for sentiment classification.
""")
        print("📝 Created basic model card")

def upload_model(model_path, repo_name, token=None, private=False):
    """Upload model to Hugging Face Hub"""
    try:
        from huggingface_hub import HfApi, create_repo
        from transformers import AutoTokenizer, AutoModelForSequenceClassification
        
        # Initialize API
        api = HfApi(token=token)
        
        # Create repository
        try:
            create_repo(repo_name, private=private, token=token)
            print(f"✅ Created repository: {repo_name}")
        except Exception as e:
            if "already exists" in str(e):
                print(f"📁 Repository {repo_name} already exists")
            else:
                print(f"❌ Error creating repository: {e}")
                return False
        
        # Load model and tokenizer
        print("📤 Loading model and tokenizer...")
        model = AutoModelForSequenceClassification.from_pretrained(model_path)
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        
        # Push to hub
        print("📤 Uploading model...")
        model.push_to_hub(repo_name, token=token)
        
        print("📤 Uploading tokenizer...")
        tokenizer.push_to_hub(repo_name, token=token)
        
        # Upload model card if it exists
        model_card_path = Path("MODEL_CARD.md")
        if model_card_path.exists():
            print("📤 Uploading model card...")
            api.upload_file(
                path_or_fileobj=str(model_card_path),
                path_in_repo="README.md",
                repo_id=repo_name,
                token=token
            )
        
        print(f"🎉 Successfully uploaded model to: https://huggingface.co/{repo_name}")
        return True
        
    except Exception as e:
        print(f"❌ Error uploading model: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Upload FinBERT model to Hugging Face Hub")
    parser.add_argument("--model_path", type=str, default="./model_files", 
                       help="Path to model files directory")
    parser.add_argument("--repo_name", type=str, required=True,
                       help="Repository name (format: username/model-name)")
    parser.add_argument("--token", type=str, 
                       help="Hugging Face token (or set HF_TOKEN env var)")
    parser.add_argument("--private", action="store_true",
                       help="Make repository private")
    
    args = parser.parse_args()
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Validate model files
    if not validate_model_files(args.model_path):
        print("\n💡 Tip: Make sure you have trained your model and saved it properly.")
        print("   Expected files: config.json, tokenizer files, and model weights")
        sys.exit(1)
    
    # Get token
    token = args.token or os.getenv("HF_TOKEN")
    if not token:
        print("❌ Hugging Face token required. Set HF_TOKEN env var or use --token")
        print("   Get your token from: https://huggingface.co/settings/tokens")
        sys.exit(1)
    
    # Upload model
    print(f"\n🚀 Starting upload to {args.repo_name}...")
    success = upload_model(args.model_path, args.repo_name, token, args.private)
    
    if success:
        print(f"\n✨ Upload complete! Your model is now available at:")
        print(f"   https://huggingface.co/{args.repo_name}")
        print(f"\n🔗 To use your model:")
        print(f'   from transformers import pipeline')
        print(f'   classifier = pipeline("text-classification", model="{args.repo_name}")')
    else:
        print("\n❌ Upload failed. Please check the error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
