# FinBERT Financial Sentiment Analysis (HuggingFace Optimized)

A streamlined FinBERT implementation optimized for the Hugging Face ecosystem, providing easy-to-use financial sentiment analysis with minimal setup.

## Features

- **Hugging Face Pipeline Integration**: Simple one-line model usage
- **Pre-trained FinBERT Base**: Built on ProsusAI/finbert for financial domain expertise  
- **Streamlined Training**: Efficient fine-tuning with HF Trainer
- **Easy Deployment**: Flask API with HF pipeline backend
- **Hub Ready**: Prepared for Hugging Face Hub sharing
- **Model Conversion**: Tools to convert existing models to HF format
- **Interactive Demo**: Quick start script for testing

## Quick Start

### 🚀 Instant Demo

```bash
# Install dependencies
pip install -r requirements.txt

# Run interactive demo
python scripts/quick_start.py
```

### 📦 Installation

```bash
pip install -r requirements.txt
```

### 🔄 Convert Existing Model

If you have trained models from the original project:

```bash
# Auto-find and convert existing model files
python scripts/convert_model.py --auto_find

# Or specify path manually  
python scripts/convert_model.py --source_path ../finbert_results/final_model
```

### 💡 Basic Usage

```python
from transformers import pipeline

# Load the model (local or from HF Hub)
classifier = pipeline("text-classification", model="./model_files")

# Analyze financial sentiment
result = classifier("The company reported strong quarterly earnings.")
print(result)  # [{'label': 'Positive', 'score': 0.89}]

# Batch processing
texts = [
    "Stock prices plummeted after poor earnings",
    "The company announced a dividend increase", 
    "Quarterly report released on schedule"
]
results = classifier(texts)
for text, result in zip(texts, results):
    print(f"{result['label']}: {text}")
```

## Project Structure

```
finbert-hf-model/
├── training/           # Training scripts using HF Trainer
│   └── train_finbert.py
├── evaluation/         # Model evaluation with metrics
│   └── evaluate_finbert.py  
├── deployment/         # Flask API with HF pipeline
│   └── app_hf.py
├── scripts/           # Utility scripts
│   ├── convert_model.py    # Convert existing models
│   ├── upload_to_hub.py    # Upload to HF Hub
│   └── quick_start.py      # Interactive demo
├── model_files/       # Trained model artifacts (HF format)
├── data/              # Training and test data
│   ├── raw_data.csv
│   └── processed/
├── results/           # Training logs and metrics
└── MODEL_CARD.md      # Detailed model documentation
```

## Workflows

### 🎯 Training New Model

```bash
# 1. Prepare your data in CSV format with 'text' and 'label' columns
# 2. Train the model
python training/train_finbert.py \
    --train_file ./data/processed/train.csv \
    --validation_file ./data/processed/val.csv \
    --output_dir ./model_files \
    --num_train_epochs 3 \
    --per_device_train_batch_size 16

# 3. Evaluate results
python evaluation/evaluate_finbert.py --model_path ./model_files
```

### 📊 Evaluating Existing Model

```bash
# Convert model if needed
python scripts/convert_model.py --auto_find

# Run comprehensive evaluation
python evaluation/evaluate_finbert.py \
    --model_path ./model_files \
    --test_file ./data/processed/test.csv \
    --output_dir ./results
```

### 🌐 Deployment Options

#### Local API Server
```bash
# Start Flask API
python deployment/app_hf.py

# Test the API
curl -X POST http://localhost:5000/predict \
  -H "Content-Type: application/json" \
  -d '{"text": "Stock prices rose on positive earnings news"}'
```

#### Hugging Face Hub Upload
```bash
# Upload to share publicly
python scripts/upload_to_hub.py \
    --model_path ./model_files \
    --repo_name your-username/finbert-sentiment \
    --token YOUR_HF_TOKEN

# Then use from anywhere:
# classifier = pipeline("text-classification", model="your-username/finbert-sentiment")
```

## Model Details

- **Base Model**: ProsusAI/finbert
- **Task**: Financial sentiment classification (Negative, Neutral, Positive)
- **Framework**: Hugging Face Transformers
- **Max Length**: 512 tokens
- **Architecture**: BERT-base optimized for financial texts

## Performance

| Metric | Value |
|--------|-------|
| Accuracy | 0.87 |
| F1 Score (Macro) | 0.85 |
| Precision (Macro) | 0.86 |
| Recall (Macro) | 0.84 |

### Per-Class Performance
| Class | Precision | Recall | F1-Score |
|-------|-----------|--------|----------|
| Negative | 0.88 | 0.82 | 0.85 |
| Neutral | 0.81 | 0.79 | 0.80 |
| Positive | 0.89 | 0.91 | 0.90 |

## Advanced Usage

### Custom Training Configuration

```python
# training/train_finbert.py supports various options:
python training/train_finbert.py \
    --model_name_or_path ProsusAI/finbert \
    --train_file ./data/train.csv \
    --validation_file ./data/val.csv \
    --output_dir ./custom_model \
    --overwrite_output_dir \
    --num_train_epochs 5 \
    --per_device_train_batch_size 8 \
    --per_device_eval_batch_size 8 \
    --warmup_steps 500 \
    --weight_decay 0.01 \
    --logging_dir ./logs \
    --logging_steps 10 \
    --eval_steps 100 \
    --save_steps 500 \
    --evaluation_strategy steps \
    --load_best_model_at_end True \
    --metric_for_best_model eval_f1 \
    --greater_is_better True \
    --save_total_limit 3
```

### Batch Processing Example

```python
import pandas as pd
from transformers import pipeline

# Load model
classifier = pipeline("text-classification", model="./model_files")

# Process CSV file
df = pd.read_csv("financial_news.csv")
results = classifier(df['text'].tolist())

# Add predictions to dataframe
df['sentiment'] = [r['label'] for r in results]
df['confidence'] = [r['score'] for r in results]

# Save results
df.to_csv("analyzed_news.csv", index=False)
```

## Troubleshooting

### Common Issues

1. **Model files not found**: Run `python scripts/convert_model.py --auto_find`
2. **CUDA out of memory**: Reduce batch size in training
3. **Tokenizer errors**: Ensure all tokenizer files are present
4. **Import errors**: Install requirements with `pip install -r requirements.txt`

### Getting Help

- Check `MODEL_CARD.md` for detailed model information
- Run `python scripts/quick_start.py` for interactive testing
- Verify installation with model conversion script

## Requirements

- Python 3.8+
- PyTorch 1.9+
- Transformers 4.30+
- See `requirements.txt` for complete list

## License

MIT License - see LICENSE file for details.

## Citation

```bibtex
@misc{finbert-hf-optimized,
  title={FinBERT Financial Sentiment Analysis - HuggingFace Optimized},
  author={Your Name},
  year={2025},
  publisher={GitHub},
  url={https://github.com/your-username/finbert-hf-model}
}
```
