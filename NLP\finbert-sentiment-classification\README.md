### Step 1: Set Up the Environment

Make sure you have the required libraries installed. You can install them using pip if you haven't already:

```bash
pip install transformers datasets torch
```

### Step 2: Load the Dataset

Assuming you have saved your preprocessed datasets in a directory called `preprocessed_datasets_original_splits`, you can load them as follows:

```python
import os
from datasets import load_from_disk

# Load the preprocessed datasets
train_dataset = load_from_disk(os.path.join("preprocessed_datasets_original_splits", "train"))
test_dataset = load_from_disk(os.path.join("preprocessed_datasets_original_splits", "test"))

print(f"Train dataset size: {len(train_dataset)}")
print(f"Test dataset size: {len(test_dataset)}")
```

### Step 3: Load the FinBERT Model

Load the FinBERT model for sequence classification:

```python
from transformers import AutoModelForSequenceClassification

# Load FinBERT model for sequence classification with 3 classes
model = AutoModelForSequenceClassification.from_pretrained("ProsusAI/finbert", num_labels=3)

print("Model loaded successfully.")
```

### Step 4: Configure Training Parameters

Set up the training arguments using `TrainingArguments`:

```python
from transformers import TrainingArguments

training_args = TrainingArguments(
    output_dir="./results",          # output directory
    evaluation_strategy="epoch",     # evaluation strategy to adopt during training
    learning_rate=2e-5,              # learning rate
    per_device_train_batch_size=16,  # batch size for training
    per_device_eval_batch_size=16,   # batch size for evaluation
    num_train_epochs=3,              # total number of training epochs
    weight_decay=0.01,               # strength of weight decay
    logging_dir='./logs',            # directory for storing logs
    logging_steps=10,
)
```

### Step 5: Create a Trainer

Create a `Trainer` instance to handle the training process:

```python
from transformers import Trainer, DataCollatorWithPadding

# Create a data collator
data_collator = DataCollatorWithPadding(tokenizer=model.config.tokenizer)

# Create the Trainer
trainer = Trainer(
    model=model,                         # the instantiated 🤗 Transformers model to be trained
    args=training_args,                  # training arguments, defined above
    train_dataset=train_dataset,         # training dataset
    eval_dataset=test_dataset,           # evaluation dataset
    data_collator=data_collator,        # data collator
)
```

### Step 6: Train the Model

Now, you can train the model:

```python
# Train the model
trainer.train()
```

### Step 7: Evaluate the Model

After training, evaluate the model on the test dataset:

```python
# Evaluate the model
eval_results = trainer.evaluate()

print(f"Evaluation results: {eval_results}")
```

### Complete Code

Here’s the complete code snippet for your project:

```python
import os
from datasets import load_from_disk
from transformers import AutoModelForSequenceClassification, TrainingArguments, Trainer, DataCollatorWithPadding

# Load the preprocessed datasets
train_dataset = load_from_disk(os.path.join("preprocessed_datasets_original_splits", "train"))
test_dataset = load_from_disk(os.path.join("preprocessed_datasets_original_splits", "test"))

# Load FinBERT model for sequence classification with 3 classes
model = AutoModelForSequenceClassification.from_pretrained("ProsusAI/finbert", num_labels=3)

# Configure training parameters
training_args = TrainingArguments(
    output_dir="./results",
    evaluation_strategy="epoch",
    learning_rate=2e-5,
    per_device_train_batch_size=16,
    per_device_eval_batch_size=16,
    num_train_epochs=3,
    weight_decay=0.01,
    logging_dir='./logs',
    logging_steps=10,
)

# Create a data collator
data_collator = DataCollatorWithPadding(tokenizer=model.config.tokenizer)

# Create the Trainer
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset,
    eval_dataset=test_dataset,
    data_collator=data_collator,
)

# Train the model
trainer.train()

# Evaluate the model
eval_results = trainer.evaluate()
print(f"Evaluation results: {eval_results}")
```

### Notes

- Ensure that your environment has access to a GPU for faster training.
- Adjust the `num_train_epochs`, `learning_rate`, and batch sizes according to your dataset size and available resources.
- You can save the trained model using `trainer.save_model()` if needed.