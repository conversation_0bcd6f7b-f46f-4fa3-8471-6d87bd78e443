# Step 1: Set Up the Environment
# Make sure you have the necessary libraries installed
# !pip install transformers datasets torch

# Step 2: Load the Dataset
from datasets import load_from_disk

# Load the preprocessed datasets
train_dataset = load_from_disk("preprocessed_datasets_original_splits/train")
test_dataset = load_from_disk("preprocessed_datasets_original_splits/test")

# Step 3: Load the FinBERT Model and Tokenizer
from transformers import AutoModelForSequenceClassification, AutoTokenizer

model_name = "ProsusAI/finbert"
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForSequenceClassification.from_pretrained(model_name, num_labels=3)

# Step 4: Configure Training Parameters
from transformers import TrainingArguments

training_args = TrainingArguments(
    output_dir="./finbert_results",          # Output directory
    evaluation_strategy="epoch",              # Evaluation strategy to adopt during training
    learning_rate=2e-5,                       # Learning rate
    per_device_train_batch_size=16,           # Batch size for training
    per_device_eval_batch_size=16,            # Batch size for evaluation
    num_train_epochs=3,                       # Total number of training epochs
    weight_decay=0.01,                        # Strength of weight decay
    logging_dir='./logs',                     # Directory for storing logs
    logging_steps=10,
)

# Step 5: Create a Trainer
from transformers import Trainer, DataCollatorWithPadding

data_collator = DataCollatorWithPadding(tokenizer=tokenizer)

trainer = Trainer(
    model=model,                              # The instantiated 🤗 Transformers model to be trained
    args=training_args,                       # Training arguments, defined above
    train_dataset=train_dataset,              # Training dataset
    eval_dataset=test_dataset,                # Evaluation dataset
    data_collator=data_collator,              # Data collator
)

# Step 6: Train the Model
trainer.train()

# Step 7: Evaluate the Model
eval_results = trainer.evaluate()
print(f"Evaluation results: {eval_results}")