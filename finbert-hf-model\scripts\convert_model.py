"""
Convert existing model files to Hugging Face format

This script helps convert your existing FinBERT model files (from the original project)
to proper Hugging Face format for the optimized structure.

Usage:
    python scripts/convert_model.py --source_path ../finbert_results/final_model --output_path ./model_files
"""

import argparse
import json
import os
import shutil
from pathlib import Path

def find_model_files(search_dirs):
    """Find model files in various locations"""
    model_locations = []
    
    for search_dir in search_dirs:
        search_path = Path(search_dir)
        if not search_path.exists():
            continue
            
        # Look for common model file patterns
        for pattern in ["**/config.json", "**/model.safetensors", "**/pytorch_model.bin"]:
            for file in search_path.glob(pattern):
                model_dir = file.parent
                if model_dir not in model_locations:
                    model_locations.append(model_dir)
    
    return model_locations

def validate_hf_model(model_path):
    """Check if directory contains valid HF model files"""
    model_path = Path(model_path)
    
    # Required files for HF model
    required_files = ["config.json"]
    optional_files = [
        "model.safetensors", "pytorch_model.bin",  # Model weights
        "tokenizer.json", "tokenizer_config.json", "vocab.txt", "special_tokens_map.json"  # Tokenizer
    ]
    
    # Check required files
    for file in required_files:
        if not (model_path / file).exists():
            return False, f"Missing required file: {file}"
    
    # Check for at least one model weight file
    has_weights = any((model_path / f).exists() for f in ["model.safetensors", "pytorch_model.bin"])
    if not has_weights:
        return False, "Missing model weights (model.safetensors or pytorch_model.bin)"
    
    return True, "Valid HuggingFace model"

def copy_model_files(source_path, output_path):
    """Copy model files to output directory"""
    source_path = Path(source_path)
    output_path = Path(output_path)
    
    # Create output directory
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Files to copy
    files_to_copy = [
        "config.json",
        "model.safetensors", "pytorch_model.bin",
        "tokenizer.json", "tokenizer_config.json", 
        "vocab.txt", "special_tokens_map.json",
        "training_args.bin"
    ]
    
    copied_files = []
    for file in files_to_copy:
        source_file = source_path / file
        if source_file.exists():
            shutil.copy2(source_file, output_path / file)
            copied_files.append(file)
            print(f"✅ Copied: {file}")
    
    return copied_files

def create_model_info(output_path, source_info=None):
    """Create model_info.json with metadata"""
    model_info = {
        "model_name": "finbert-financial-sentiment",
        "base_model": "ProsusAI/finbert",
        "task": "text-classification",
        "num_labels": 3,
        "label_names": ["Negative", "Neutral", "Positive"],
        "max_length": 512,
        "framework": "transformers",
        "created_with": "finbert-hf-model conversion script"
    }
    
    # Add source info if available
    if source_info:
        model_info.update(source_info)
    
    # Save model info
    info_path = Path(output_path) / "model_info.json"
    with open(info_path, "w") as f:
        json.dump(model_info, f, indent=2)
    
    print(f"✅ Created: model_info.json")
    return model_info

def verify_conversion(output_path):
    """Verify the converted model can be loaded"""
    try:
        from transformers import AutoTokenizer, AutoModelForSequenceClassification
        
        print("🔍 Verifying converted model...")
        
        # Try to load model
        model = AutoModelForSequenceClassification.from_pretrained(output_path)
        print(f"✅ Model loaded successfully ({model.config.num_labels} labels)")
        
        # Try to load tokenizer
        try:
            tokenizer = AutoTokenizer.from_pretrained(output_path)
            print(f"✅ Tokenizer loaded successfully (vocab size: {tokenizer.vocab_size})")
        except Exception as e:
            print(f"⚠️  Tokenizer loading failed: {e}")
            print("   You may need to copy tokenizer files manually")
        
        return True
        
    except Exception as e:
        print(f"❌ Model verification failed: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Convert model to HuggingFace format")
    parser.add_argument("--source_path", type=str, 
                       help="Path to source model directory")
    parser.add_argument("--output_path", type=str, default="./model_files",
                       help="Output directory for HF model")
    parser.add_argument("--auto_find", action="store_true",
                       help="Automatically find model files in project")
    
    args = parser.parse_args()
    
    # Auto-find model files if requested
    if args.auto_find or not args.source_path:
        print("🔍 Searching for model files in project...")
        
        # Common locations to search
        search_dirs = [
            "../finbert_results/final_model",
            "../finbert_results/checkpoint-285",
            "../finbert_results/checkpoint-190",
            "finbert_results/final_model",
            "./finbert_results/final_model",
            "../preprocessed_datasets/tokenizer"
        ]
        
        model_locations = find_model_files(search_dirs)
        
        if not model_locations:
            print("❌ No model files found in common locations")
            print("   Please specify --source_path manually")
            return
        
        print(f"📁 Found {len(model_locations)} potential model location(s):")
        for i, location in enumerate(model_locations):
            is_valid, status = validate_hf_model(location)
            status_icon = "✅" if is_valid else "⚠️ "
            print(f"   {i+1}. {location} {status_icon} {status}")
        
        # Use the first valid location
        valid_locations = [loc for loc in model_locations if validate_hf_model(loc)[0]]
        if valid_locations:
            args.source_path = str(valid_locations[0])
            print(f"🎯 Using: {args.source_path}")
        else:
            print("❌ No valid HuggingFace model found")
            return
    
    # Validate source path
    source_path = Path(args.source_path)
    if not source_path.exists():
        print(f"❌ Source path does not exist: {args.source_path}")
        return
    
    is_valid, status = validate_hf_model(source_path)
    if not is_valid:
        print(f"❌ Invalid model directory: {status}")
        return
    
    print(f"✅ Valid source model found: {args.source_path}")
    
    # Copy model files
    print(f"\n📋 Copying model files to: {args.output_path}")
    copied_files = copy_model_files(source_path, args.output_path)
    
    if not copied_files:
        print("❌ No files were copied")
        return
    
    # Create model info
    create_model_info(args.output_path)
    
    # Verify conversion
    print(f"\n🔍 Verifying conversion...")
    if verify_conversion(args.output_path):
        print(f"\n🎉 Conversion successful!")
        print(f"   Model files are ready in: {args.output_path}")
        print(f"   Files copied: {', '.join(copied_files)}")
        print(f"\n💡 Next steps:")
        print(f"   1. Test the model: python evaluation/evaluate_finbert.py")
        print(f"   2. Upload to HF Hub: python scripts/upload_to_hub.py --repo_name your-username/model-name")
    else:
        print(f"\n❌ Conversion completed but verification failed")
        print(f"   Check the error messages above")

if __name__ == "__main__":
    main()
