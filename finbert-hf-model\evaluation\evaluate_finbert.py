"""
Simple FinBERT Evaluation Script - Hugging Face Optimized
Evaluate FinBERT model using Hugging Face ecosystem.
"""

import pandas as pd
import numpy as np
import torch
from transformers import AutoModelForSequenceClassification, AutoTokenizer, pipeline
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix, classification_report
import matplotlib.pyplot as plt
import seaborn as sns
import argparse
import json
from datetime import datetime

def evaluate_model(model_path, test_data_path, text_column='summary_detail_with_title', 
                  label_column='labels', output_dir='evaluation'):
    """
    Evaluate a trained FinBERT model.
    
    Args:
        model_path: Path to the saved Hugging Face model
        test_data_path: Path to test data CSV
        text_column: Name of text column
        label_column: Name of label column
        output_dir: Directory to save results
    """
    
    print("🔍 Starting FinBERT Model Evaluation")
    print(f"📁 Model: {model_path}")
    print(f"📊 Test data: {test_data_path}")
    
    # Load model and tokenizer
    print("🤗 Loading model and tokenizer...")
    try:
        model = AutoModelForSequenceClassification.from_pretrained(model_path)
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        
        # Create pipeline for easy inference
        classifier = pipeline(
            "text-classification",
            model=model,
            tokenizer=tokenizer,
            device=0 if torch.cuda.is_available() else -1
        )
        
        print("✅ Model loaded successfully")
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None
    
    # Load test data
    print("📊 Loading test data...")
    df = pd.read_csv(test_data_path)
    
    if text_column not in df.columns:
        print(f"❌ Column '{text_column}' not found. Available columns: {list(df.columns)}")
        return None
    
    if label_column not in df.columns:
        print(f"❌ Column '{label_column}' not found. Available columns: {list(df.columns)}")
        return None
    
    # Clean data
    df = df.dropna(subset=[text_column, label_column])
    print(f"📋 Test samples: {len(df)}")
    
    # Make predictions
    print("🎯 Making predictions...")
    texts = df[text_column].astype(str).tolist()
    
    # Batch prediction for efficiency
    batch_size = 32
    all_predictions = []
    all_scores = []
    
    for i in range(0, len(texts), batch_size):
        batch_texts = texts[i:i+batch_size]
        batch_results = classifier(batch_texts)
        
        for result in batch_results:
            # Map HF labels to our format
            if result['label'] == 'LABEL_0':
                pred = 0  # Negative
            elif result['label'] == 'LABEL_1':
                pred = 1  # Neutral
            elif result['label'] == 'LABEL_2':
                pred = 2  # Positive
            else:
                pred = 1  # Default to neutral
            
            all_predictions.append(pred)
            all_scores.append(result['score'])
        
        if (i // batch_size + 1) % 10 == 0:
            print(f"   Processed {i + len(batch_texts)}/{len(texts)} samples")
    
    # Get true labels
    true_labels = df[label_column].values
    predictions = np.array(all_predictions)
    
    # Calculate metrics
    print("📊 Calculating metrics...")
    accuracy = accuracy_score(true_labels, predictions)
    precision, recall, f1, support = precision_recall_fscore_support(
        true_labels, predictions, average='macro'
    )
    
    # Per-class metrics
    precision_per_class, recall_per_class, f1_per_class, support_per_class = precision_recall_fscore_support(
        true_labels, predictions, average=None
    )
    
    # Confusion matrix
    cm = confusion_matrix(true_labels, predictions)
    
    # Print results
    print("\n🎯 Evaluation Results:")
    print(f"   Accuracy: {accuracy:.4f}")
    print(f"   F1 Score (Macro): {f1:.4f}")
    print(f"   Precision (Macro): {precision:.4f}")
    print(f"   Recall (Macro): {recall:.4f}")
    
    print("\n📊 Per-Class Results:")
    class_names = ['Negative', 'Neutral', 'Positive']
    for i, class_name in enumerate(class_names):
        if i < len(precision_per_class):
            print(f"   {class_name}:")
            print(f"     Precision: {precision_per_class[i]:.4f}")
            print(f"     Recall: {recall_per_class[i]:.4f}")
            print(f"     F1: {f1_per_class[i]:.4f}")
            print(f"     Support: {support_per_class[i]}")
    
    # Save results
    import os
    os.makedirs(output_dir, exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Save detailed results
    results = {
        "model_path": model_path,
        "test_data_path": test_data_path,
        "evaluation_timestamp": timestamp,
        "metrics": {
            "accuracy": float(accuracy),
            "f1_macro": float(f1),
            "precision_macro": float(precision),
            "recall_macro": float(recall),
            "per_class": {
                class_names[i]: {
                    "precision": float(precision_per_class[i]) if i < len(precision_per_class) else 0,
                    "recall": float(recall_per_class[i]) if i < len(recall_per_class) else 0,
                    "f1": float(f1_per_class[i]) if i < len(f1_per_class) else 0,
                    "support": int(support_per_class[i]) if i < len(support_per_class) else 0
                } for i in range(min(3, len(class_names)))
            }
        },
        "confusion_matrix": cm.tolist(),
        "sample_predictions": {
            "texts": texts[:10],
            "true_labels": true_labels[:10].tolist(),
            "predictions": predictions[:10].tolist(),
            "scores": all_scores[:10]
        }
    }
    
    results_file = f"{output_dir}/evaluation_results_{timestamp}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    # Plot confusion matrix
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=class_names, yticklabels=class_names)
    plt.title('Confusion Matrix')
    plt.xlabel('Predicted')
    plt.ylabel('Actual')
    plt.tight_layout()
    
    cm_file = f"{output_dir}/confusion_matrix_{timestamp}.png"
    plt.savefig(cm_file, dpi=300, bbox_inches='tight')
    plt.show()
    
    # Create predictions DataFrame
    results_df = df.copy()
    results_df['predicted_label'] = predictions
    results_df['predicted_class'] = [class_names[pred] for pred in predictions]
    results_df['confidence'] = all_scores
    results_df['correct'] = (predictions == true_labels)
    
    # Save predictions
    predictions_file = f"{output_dir}/predictions_{timestamp}.csv"
    results_df.to_csv(predictions_file, index=False)
    
    print(f"\n💾 Results saved:")
    print(f"   📊 Metrics: {results_file}")
    print(f"   📈 Confusion Matrix: {cm_file}")
    print(f"   📋 Predictions: {predictions_file}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description='Evaluate FinBERT sentiment model')
    parser.add_argument('--model_path', type=str, default='./model_files', 
                       help='Path to saved model')
    parser.add_argument('--test_data', type=str, default='data/raw_data.csv',
                       help='Path to test data CSV')
    parser.add_argument('--text_column', type=str, default='summary_detail_with_title',
                       help='Name of text column')
    parser.add_argument('--label_column', type=str, default='labels',
                       help='Name of label column')
    parser.add_argument('--output_dir', type=str, default='evaluation',
                       help='Output directory for results')
    
    args = parser.parse_args()
    
    # Run evaluation
    results = evaluate_model(
        model_path=args.model_path,
        test_data_path=args.test_data,
        text_column=args.text_column,
        label_column=args.label_column,
        output_dir=args.output_dir
    )
    
    if results:
        print("✅ Evaluation completed successfully!")
    else:
        print("❌ Evaluation failed!")

if __name__ == "__main__":
    main()
