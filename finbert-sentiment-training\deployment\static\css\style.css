/* Custom styles for FinBERT Sentiment Analyzer */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    border-bottom: 1px solid #dee2e6;
}

.btn {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.sample-text {
    text-align: left;
    font-size: 0.85rem;
}

.sample-text:hover {
    background-color: #e9ecef;
}

/* Sentiment-specific styles */
.sentiment-positive {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.sentiment-negative {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.sentiment-neutral {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

/* Progress bars for probabilities */
.probability-bar {
    margin-bottom: 10px;
}

.probability-label {
    font-weight: 600;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
}

.progress {
    height: 25px;
    border-radius: 15px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 15px;
    transition: width 0.6s ease;
    font-weight: 600;
    font-size: 0.85rem;
}

.progress-bar-positive {
    background: linear-gradient(45deg, #28a745, #20c997);
}

.progress-bar-negative {
    background: linear-gradient(45deg, #dc3545, #e74c3c);
}

.progress-bar-neutral {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
}

/* Loading animation */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Text area styling */
#textInput {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

#textInput:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Character/word count */
.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Justification styling */
.justification-item {
    padding: 10px;
    margin: 5px 0;
    border-radius: 8px;
    border-left: 4px solid;
}

.justification-positive {
    background-color: #f8fff9;
    border-left-color: #28a745;
}

.justification-negative {
    background-color: #fff8f8;
    border-left-color: #dc3545;
}

.justification-neutral {
    background-color: #fffef8;
    border-left-color: #ffc107;
}

/* Summary styling */
#summaryText {
    font-style: italic;
    line-height: 1.6;
    color: #495057;
}

/* Header styling */
header {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
}

/* Footer styling */
footer {
    margin-top: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 10px;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    header h1 {
        font-size: 1.5rem;
    }
    
    header p {
        font-size: 0.9rem;
    }
}

/* Animation for results appearing */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

/* Confidence score styling */
#confidenceScore {
    font-size: 1.1rem;
    font-weight: 700;
}

/* Icon styling */
.fas, .far {
    color: inherit;
}

/* Error styling */
.alert-danger {
    border-radius: 8px;
}

/* Badge styling */
.badge {
    border-radius: 20px;
    padding: 8px 12px;
}
