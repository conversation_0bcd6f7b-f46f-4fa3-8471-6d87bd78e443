#!/usr/bin/env python3
"""
Test the problematic examples to see current model predictions
"""

import requests
import json

def test_example(text, expected_sentiment):
    """Test a single example and show results."""
    try:
        response = requests.post('http://127.0.0.1:5000/analyze', 
                                json={'text': text}, 
                                headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            data = response.json()
            print(f"Expected: {expected_sentiment}")
            print(f"Predicted: {data['sentiment']}")
            print(f"Confidence: {data['confidence']:.1%}")
            print("Probabilities:")
            for sentiment, prob in data['probabilities'].items():
                print(f"  {sentiment}: {prob:.1%}")
            print("Justifications:")
            for just in data['justifications']:
                print(f"  • {just['text']}")
            print("-" * 60)
            return data
        else:
            print(f"Error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"Error: {e}")
        return None

# Test cases
print("🧪 Testing Problematic Examples")
print("=" * 60)

# Negative example that was predicted as neutral
negative_text = """Meta Platforms Faces Massive $15 Billion Loss as Metaverse Strategy Crumbles

MENLO PARK, Calif. - Meta Platforms Inc. reported devastating quarterly results yesterday, with the company's Reality Labs division posting a staggering $15.2 billion operating loss for the fiscal year. The social media giant's ambitious metaverse bet continues to hemorrhage cash while failing to attract meaningful user engagement.

The company's stock crashed 18% in pre-market trading after CEO Mark Zuckerberg admitted that metaverse adoption has been "significantly slower than anticipated." Meta's total revenue declined 8% year-over-year to $27.7 billion, marking the company's worst quarterly performance since going public."""

print("📉 NEGATIVE EXAMPLE:")
test_example(negative_text, "Negative")

# Neutral example that was predicted as positive
neutral_text = """Federal Reserve Maintains Interest Rates at 5.25% Following Two-Day Policy Meeting

WASHINGTON, D.C. - The Federal Reserve concluded its two-day Federal Open Market Committee meeting today by keeping the federal funds rate unchanged at 5.25-5.50%, marking the third consecutive meeting without a rate adjustment. The decision was widely anticipated by market participants and aligns with recent economic data trends.

Fed Chair Jerome Powell stated during the post-meeting press conference that the central bank is taking a measured approach to monetary policy. Recent inflation data shows the Consumer Price Index holding steady at 3.2% annually, while core inflation excluding food and energy remains at 3.8%."""

print("⚖️ NEUTRAL EXAMPLE:")
test_example(neutral_text, "Neutral")

# Test a clearly positive example for comparison
positive_text = """Apple Inc. reported record quarterly earnings, beating analyst expectations by 15%. The company's revenue grew 25% year-over-year, driven by strong iPhone sales and expanding services revenue."""

print("📈 POSITIVE EXAMPLE (for comparison):")
test_example(positive_text, "Positive")
