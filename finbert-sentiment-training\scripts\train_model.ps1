# FinBERT Training Script for Windows PowerShell
# Usage: .\scripts\train_model.ps1 [data_file] [text_column] [label_column]

param(
    [string]$DataFile = "data\raw\data.csv",
    [string]$TextColumn = "summary_detail_with_title",
    [string]$LabelColumn = "labels",
    [string]$OutputDir = "models",
    [string]$SampleSize = ""
)

Write-Host "=========================================" -ForegroundColor Green
Write-Host "FinBERT Sentiment Analysis Training" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green
Write-Host "Data file: $DataFile"
Write-Host "Text column: $TextColumn"
Write-Host "Label column: $LabelColumn"
Write-Host "Output directory: $OutputDir"
if ($SampleSize -ne "") {
    Write-Host "Sample size: $SampleSize"
}
Write-Host "=========================================" -ForegroundColor Green

# Check if data file exists
if (-not (Test-Path $DataFile)) {
    Write-Host "Error: Data file '$DataFile' not found!" -ForegroundColor Red
    exit 1
}

# Create necessary directories
$directories = @("logs", "models\checkpoints", "models\final", "results\metrics", "results\plots")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Yellow
    }
}

# Activate virtual environment if it exists
if (Test-Path "venv\Scripts\Activate.ps1") {
    Write-Host "Activating virtual environment..." -ForegroundColor Yellow
    & "venv\Scripts\Activate.ps1"
}

# Check if required packages are installed
try {
    python -c "import torch, transformers, pandas" 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Required packages not found"
    }
} catch {
    Write-Host "Error: Required packages not installed. Run: pip install -r requirements.txt" -ForegroundColor Red
    exit 1
}

# Run training
Write-Host "Starting FinBERT training..." -ForegroundColor Green
Write-Host "Timestamp: $(Get-Date)" -ForegroundColor Cyan

try {
    if ($SampleSize -ne "") {
        python src\train.py --data_file $DataFile --text_column $TextColumn --label_column $LabelColumn --output_dir $OutputDir --sample_size $SampleSize
    } else {
        python src\train.py --data_file $DataFile --text_column $TextColumn --label_column $LabelColumn --output_dir $OutputDir
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "=========================================" -ForegroundColor Green
        Write-Host "Training completed successfully!" -ForegroundColor Green
        Write-Host "Timestamp: $(Get-Date)" -ForegroundColor Cyan
        Write-Host "Check logs\ directory for training logs" -ForegroundColor Yellow
        Write-Host "Check models\ directory for saved models" -ForegroundColor Yellow
        Write-Host "Check results\ directory for metrics and plots" -ForegroundColor Yellow
        Write-Host "=========================================" -ForegroundColor Green
    } else {
        Write-Host "Training failed with exit code: $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
    }
} catch {
    Write-Host "Error during training: $_" -ForegroundColor Red
    exit 1
}
