# FinBERT Sentiment Analyzer - Flask Web Application

A sophisticated web interface for financial sentiment analysis using the fine-tuned FinBERT model. This application provides real-time sentiment analysis, article summarization, and detailed explanations for financial text.

## Features

### 🎯 Core Functionality
- **Real-time Sentiment Analysis**: Analyze financial text with high accuracy using the trained FinBERT model
- **Confidence Scoring**: Get confidence levels for each prediction
- **Probability Breakdown**: See detailed probability scores for Positive, Negative, and Neutral sentiments
- **Article Summarization**: Automatic extractive summarization of input text
- **Intelligent Justifications**: Detailed explanations for why the model made specific predictions

### 🎨 User Interface
- **Modern Bootstrap Design**: Clean, responsive interface that works on all devices
- **Interactive Elements**: Real-time character/word counting, sample text buttons
- **Visual Feedback**: Color-coded results, progress bars, and animated transitions
- **Professional Styling**: Financial-themed design with intuitive navigation

### 🧠 AI-Powered Analysis
- **FinBERT Model**: Uses the specialized ProsusAI/finbert model fine-tuned on financial data
- **Keyword Detection**: Identifies positive, negative, and neutral financial indicators
- **Context Understanding**: Analyzes text context for more accurate sentiment classification
- **Mixed Sentiment Detection**: Handles ambiguous or mixed sentiment scenarios

## Installation

### Prerequisites
- Python 3.8 or higher
- The trained FinBERT model file (`finbert_model_only.pkl`)

### Setup Instructions

1. **Clone or navigate to the project directory**
   ```bash
   cd /path/to/your/project
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify model file location**
   Ensure the `finbert_model_only.pkl` file is located at:
   ```
   finbert_results/model_export/finbert_model_only.pkl
   ```

4. **Run the application**
   ```bash
   python app.py
   ```

5. **Access the web interface**
   Open your browser and navigate to: `http://localhost:5000`

## Usage

### Web Interface

1. **Enter Text**: Paste or type financial text into the input area
2. **Use Examples**: Click on sample text buttons to try pre-loaded examples
3. **Analyze**: Click "Analyze Sentiment" to process the text
4. **Review Results**: View the sentiment classification, confidence scores, and detailed analysis

### API Endpoint

The application also provides a REST API endpoint:

```bash
POST /analyze
Content-Type: application/json

{
    "text": "Your financial text here"
}
```

**Response:**
```json
{
    "sentiment": "Positive",
    "confidence": 0.89,
    "probabilities": {
        "Negative": 0.05,
        "Neutral": 0.06,
        "Positive": 0.89
    },
    "summary": "Extracted summary of the text...",
    "justifications": [
        {
            "type": "confidence",
            "text": "The model is highly confident (89.0%) in this positive classification.",
            "icon": "fas fa-check-circle",
            "class": "justification-positive"
        }
    ],
    "timestamp": "2025-07-21T10:30:00",
    "text_length": 150,
    "word_count": 25
}
```

## File Structure

```
├── app.py                          # Main Flask application
├── requirements.txt                # Python dependencies
├── FLASK_README.md                # This documentation
├── templates/
│   └── index.html                 # Main web interface template
├── static/
│   ├── css/
│   │   └── style.css             # Custom styling
│   └── js/
│       └── app.js                # Frontend JavaScript
└── finbert_results/
    └── model_export/
        └── finbert_model_only.pkl # Trained FinBERT model
```

## Technical Details

### Model Loading
- Uses the `finbert_model_only.pkl` file containing the fine-tuned model weights
- Automatically detects and uses GPU if available, falls back to CPU
- Implements proper error handling and logging

### Text Processing
- Maximum input length: 512 tokens (BERT limitation)
- Automatic text truncation for longer inputs
- Real-time character and word counting

### Summarization Algorithm
- Extractive summarization based on sentence scoring
- Considers sentence position, length, and financial keyword density
- Configurable summary length (default: 3 sentences)

### Justification Engine
- Confidence-based explanations
- Financial keyword detection and analysis
- Probability distribution analysis
- Mixed sentiment detection

## Customization

### Adding New Keywords
Edit the keyword lists in `app.py`:
```python
positive_keywords = ['profit', 'growth', 'increase', ...]
negative_keywords = ['loss', 'decline', 'fall', ...]
neutral_keywords = ['stable', 'maintain', 'steady', ...]
```

### Modifying Summary Length
Change the `max_sentences` parameter in the `generate_summary` method:
```python
summary = analyzer.generate_summary(text, max_sentences=5)
```

### Styling Customization
Modify `static/css/style.css` to change the appearance and colors.

## Troubleshooting

### Common Issues

1. **Model file not found**
   - Ensure `finbert_model_only.pkl` is in the correct location
   - Check file permissions

2. **Memory issues**
   - The model requires significant RAM (2-4GB recommended)
   - Consider using CPU-only mode for limited memory systems

3. **Slow loading**
   - First model load takes time (downloading base FinBERT model)
   - Subsequent requests are much faster

4. **CUDA errors**
   - If GPU issues occur, the app will automatically fall back to CPU
   - Check CUDA installation if GPU acceleration is desired

## Performance

- **Model Loading**: 10-30 seconds (first time only)
- **Analysis Speed**: 100-500ms per request
- **Memory Usage**: 2-4GB RAM
- **Concurrent Users**: Supports multiple simultaneous requests

## Security Considerations

- Input validation prevents malicious text injection
- No user data is stored or logged
- All processing happens locally (no external API calls)

## License

This application uses the FinBERT model which is subject to its original license terms. Please refer to the main project documentation for licensing information.
