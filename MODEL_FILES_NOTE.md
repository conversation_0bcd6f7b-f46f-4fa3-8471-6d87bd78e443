# Model Files Note

## Large Files Excluded from Repository

Due to GitHub's file size limitations (100MB max), the following large model files are **not included** in this repository:

### Missing Model Files:
```
finbert_results/model_export/
├── finbert_model.pkl           # ~450MB - Complete model package
├── finbert_model.joblib        # ~440MB - Joblib optimized version  
└── finbert_model_only.pkl      # ~380MB - Model weights only

finbert_results/checkpoint-190/
├── model.safetensors          # ~400MB - Training checkpoint
└── optimizer.pt               # ~50MB - Optimizer state

finbert_results/checkpoint-285/
├── model.safetensors          # ~400MB - Training checkpoint
└── optimizer.pt               # ~50MB - Optimizer state

finbert_results/final_model/
└── model.safetensors          # ~400MB - Final model weights

preprocessed_datasets/
├── train/data-00000-of-00001.arrow      # ~300MB - Training data
├── test/data-00000-of-00001.arrow       # ~80MB - Test data
└── validation/data-00000-of-00001.arrow # ~80MB - Validation data
```

## How to Regenerate These Files:

1. **Run the training notebooks** to regenerate model files:
   - Execute `NLP.ipynb` for data preprocessing
   - Execute `Step4_Model_Training_FinBERT.ipynb` for model training

2. **Model files will be automatically created** in their respective directories

3. **For deployment**: Use the generated pickle files in `finbert_results/model_export/`

## Alternative Storage Options:

- **Google Drive**: Upload large model files to shared drive
- **Hugging Face Hub**: Push model to Hugging Face model repository
- **Git LFS**: Use Git Large File Storage for version control of large files
- **Cloud Storage**: AWS S3, Azure Blob, etc.

## Quick Start Without Large Files:

The repository contains all the **code, configurations, and analysis files** needed to:
- ✅ Understand the project structure
- ✅ Review the training methodology  
- ✅ Analyze the performance metrics
- ✅ Retrain the model from scratch
- ✅ Deploy using the training notebooks

**Note**: All performance metrics, analysis files, and smaller configuration files are included.
