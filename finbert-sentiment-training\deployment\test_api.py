#!/usr/bin/env python3
"""
Test script for the FinBERT Sentiment Analyzer Flask API
"""

import requests
import json
import time

# API endpoint
API_URL = "http://127.0.0.1:5000/analyze"

# Test cases
test_cases = [
    {
        "name": "Positive Financial News",
        "text": "Apple Inc. reported record quarterly earnings, beating analyst expectations by 15%. The company's revenue grew 25% year-over-year, driven by strong iPhone sales and expanding services revenue. CEO <PERSON> expressed optimism about future growth prospects."
    },
    {
        "name": "Negative Financial News", 
        "text": "Tesla stock plummeted 12% in after-hours trading following disappointing quarterly delivery numbers. The company cited supply chain disruptions and production challenges at its Shanghai facility. Analysts downgraded the stock citing concerns about future profitability."
    },
    {
        "name": "Neutral Financial News",
        "text": "The Federal Reserve announced a 0.75% interest rate hike to combat inflation. Markets remained stable following the announcement, with investors already pricing in the increase. The central bank maintained its cautious stance on future monetary policy."
    },
    {
        "name": "Mixed Sentiment",
        "text": "Microsoft reported strong cloud revenue growth of 30% but warned of potential headwinds in the PC market. While Azure services exceeded expectations, Windows licensing revenue declined 5% due to weak consumer demand."
    }
]

def test_api():
    """Test the Flask API with various financial text samples."""
    print("🧪 Testing FinBERT Sentiment Analyzer API")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📊 Test {i}: {test_case['name']}")
        print("-" * 40)
        print(f"Text: {test_case['text'][:100]}...")
        
        try:
            # Send request
            start_time = time.time()
            response = requests.post(
                API_URL,
                json={"text": test_case['text']},
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"✅ Status: Success")
                print(f"⏱️  Response Time: {(end_time - start_time):.2f}s")
                print(f"🎯 Sentiment: {data['sentiment']}")
                print(f"📈 Confidence: {data['confidence']:.1%}")
                print(f"📊 Probabilities:")
                for sentiment, prob in data['probabilities'].items():
                    print(f"   {sentiment}: {prob:.1%}")
                
                print(f"📝 Summary: {data['summary'][:100]}...")
                print(f"💡 Justifications: {len(data['justifications'])} explanations")
                
                # Show first justification
                if data['justifications']:
                    print(f"   • {data['justifications'][0]['text']}")
                
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"   Message: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 API Testing Complete")

def test_edge_cases():
    """Test edge cases and error handling."""
    print("\n🔍 Testing Edge Cases")
    print("=" * 30)
    
    edge_cases = [
        {"name": "Empty Text", "text": ""},
        {"name": "Very Short Text", "text": "Good"},
        {"name": "Very Long Text", "text": "This is a test. " * 100},
        {"name": "Non-Financial Text", "text": "The weather is nice today. I went for a walk in the park and saw some beautiful flowers."}
    ]
    
    for test_case in edge_cases:
        print(f"\n🧪 {test_case['name']}")
        try:
            response = requests.post(
                API_URL,
                json={"text": test_case['text']},
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success: {data['sentiment']} ({data['confidence']:.1%})")
            else:
                print(f"⚠️  Expected error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    # Wait a moment for the server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    try:
        # Test if server is running
        response = requests.get("http://127.0.0.1:5000", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running!")
            test_api()
            test_edge_cases()
        else:
            print("❌ Server not responding correctly")
    except requests.exceptions.RequestException:
        print("❌ Server is not running. Please start the Flask app first:")
        print("   python app.py")
