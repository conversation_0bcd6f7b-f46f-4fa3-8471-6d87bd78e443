
# HOW TO LOAD THE EXPORTED MODEL:

## Method 1: Load complete model with pickle
import pickle
import torch

# Load the complete model package
with open('finbert_model.pkl', 'rb') as f:
    model_package = pickle.load(f)

model = model_package['model']
tokenizer = model_package['tokenizer']
label_mapping = model_package['label_mapping']

# Use the model
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = model.to(device)

def predict_sentiment(text):
    inputs = tokenizer(text, return_tensors="pt", truncation=True, padding=True, max_length=512)
    inputs = {k: v.to(device) for k, v in inputs.items()}

    model.eval()
    with torch.no_grad():
        outputs = model(**inputs)
        predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)

    probs = predictions.cpu().numpy()[0]
    predicted_class = torch.argmax(predictions, dim=-1).item()

    return label_mapping[predicted_class], probs

## Method 2: Load with joblib (often faster)
import joblib

model_package = joblib.load('finbert_model.joblib')
# Then use same as Method 1

## Method 3: Load only model weights (smallest file)
import pickle
from transformers import AutoModelForSequenceClassification, AutoTokenizer

with open('finbert_model_only.pkl', 'rb') as f:
    model_data = pickle.load(f)

# Recreate model and load weights
model = AutoModelForSequenceClassification.from_pretrained('ProsusAI/finbert', num_labels=3)
model.load_state_dict(model_data['model_state_dict'])
tokenizer = model_data['tokenizer']
    