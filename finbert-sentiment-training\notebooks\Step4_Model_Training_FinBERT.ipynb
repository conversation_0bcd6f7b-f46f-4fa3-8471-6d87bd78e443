{"cells": [{"cell_type": "markdown", "id": "e7dc31fa", "metadata": {}, "source": ["# **Step 4: FinBERT Model Training**\n", "\n", "This notebook implements FinBERT model training for financial sentiment analysis using preprocessed data.\n", "\n", "## **Why FinBERT?**\n", "FinBERT (ProsusAI/finbert) is the optimal choice because:\n", "- **Pre-trained on financial texts**: Understands financial jargon\n", "- **Long text handling**: Up to 512 tokens with global context capture\n", "- **Efficient fine-tuning**: Optimal performance with modest-sized datasets\n", "\n", "## **Training steps:**\n", "1. **FinBERT model loading**: Configuration for 3 sentiment classes\n", "2. **Hyperparameter configuration**: Training parameter optimization\n", "3. **Trainer creation**: Hugging Face Trainer API configuration\n", "4. **Training**: Model fine-tuning on our data\n", "5. **Evaluation**: Performance metrics (accuracy, F1-score, etc.)\n", "6. **Saving**: Trained model and metrics"]}, {"cell_type": "code", "execution_count": 22, "id": "0b680d4e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Device used: cpu\n", "GPU not available\n", "All libraries imported successfully\n"]}], "source": ["# Installation of necessary libraries\n", "# Uncomment if packages are not installed\n", "# !pip install transformers datasets torch scikit-learn accelerate\n", "\n", "# Library imports\n", "import os\n", "import json\n", "import numpy as np\n", "import pandas as pd\n", "import torch\n", "from datetime import datetime\n", "\n", "# Transformers and Datasets\n", "from transformers import (\n", "    AutoModelForSequenceClassification,\n", "    AutoTokenizer,\n", "    TrainingArguments,\n", "    Trainer,\n", "    EarlyStoppingCallback\n", ")\n", "from datasets import load_from_disk\n", "\n", "# Metrics\n", "from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix\n", "\n", "# Visualization\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Environment configuration\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Device used: {device}\")\n", "print(f\"Available GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB\" if torch.cuda.is_available() else \"GPU not available\")\n", "\n", "print(\"All libraries imported successfully\")"]}, {"cell_type": "code", "execution_count": 23, "id": "6b39e135", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== STEP 4.1: LOADING PREPROCESSED DATA ===\n", "Data folder found: preprocessed_datasets_original_splits\n", "\n", "Loading datasets...\n", "Train dataset loaded: 1508 examples\n", "Test dataset loaded: 266 examples\n", "Tokenizer loaded: BertTokenizerFast\n", "\n", "Preprocessing information:\n", "  - Original dataset: test\n", "  - Tokenizer: ProsusAI/finbert\n", "  - Max length: 512\n", "  - Preprocessing date: 2025-07-15 15:35:42\n", "\n", "Data structure:\n", "  Train columns: ['labels', 'input_ids', 'attention_mask']\n", "  Test columns: ['labels', 'input_ids', 'attention_mask']\n", "  Format: {'type': 'torch', 'format_kwargs': {}, 'columns': ['input_ids', 'attention_mask', 'labels'], 'output_all_columns': False}\n", "\n", "Data example:\n", "  Input IDs shape: torch.Size([512])\n", "  Attention mask shape: torch.Size([512])\n", "  Label: 2 (Positive)\n"]}], "source": ["# Step 4.1: Loading preprocessed data\n", "print(\"=== STEP 4.1: LOADING PREPROCESSED DATA ===\")\n", "\n", "# Path to preprocessed data\n", "data_dir = \"preprocessed_datasets_original_splits\"\n", "\n", "# Check that data exists\n", "if not os.path.exists(data_dir):\n", "    print(f\"Error: The folder {data_dir} does not exist.\")\n", "    print(f\"Please run the preprocessing notebook first.\")\n", "else:\n", "    print(f\"Data folder found: {data_dir}\")\n", "\n", "try:\n", "    # Load preprocessed datasets\n", "    print(\"\\nLoading datasets...\")\n", "    train_dataset = load_from_disk(os.path.join(data_dir, \"train\"))\n", "    test_dataset = load_from_disk(os.path.join(data_dir, \"test\"))\n", "    \n", "    print(f\"Train dataset loaded: {len(train_dataset)} examples\")\n", "    print(f\"Test dataset loaded: {len(test_dataset)} examples\")\n", "    \n", "    # Load tokenizer\n", "    tokenizer = AutoTokenizer.from_pretrained(os.path.join(data_dir, \"tokenizer\"))\n", "    print(f\"Tokenizer loaded: {tokenizer.__class__.__name__}\")\n", "    \n", "    # Load metadata\n", "    with open(os.path.join(data_dir, \"metadata.json\"), 'r') as f:\n", "        metadata = json.load(f)\n", "    \n", "    print(f\"\\nPreprocessing information:\")\n", "    print(f\"  - Original dataset: {metadata['dataset_name']}\")\n", "    print(f\"  - Tokenizer: {metadata['tokenizer_name']}\")\n", "    print(f\"  - Max length: {metadata['max_length']}\")\n", "    print(f\"  - Preprocessing date: {metadata['preprocessing_date']}\")\n", "    \n", "    # Check data structure\n", "    print(f\"\\nData structure:\")\n", "    print(f\"  Train columns: {train_dataset.column_names}\")\n", "    print(f\"  Test columns: {test_dataset.column_names}\")\n", "    print(f\"  Format: {train_dataset.format}\")\n", "    \n", "    # Data example\n", "    example = train_dataset[0]\n", "    print(f\"\\nData example:\")\n", "    print(f\"  Input IDs shape: {example['input_ids'].shape}\")\n", "    print(f\"  Attention mask shape: {example['attention_mask'].shape}\")\n", "    print(f\"  Label: {example['labels']} ({['Negative', 'Neutral', 'Positive'][example['labels']]})\")\n", "    \n", "except Exception as e:\n", "    print(f\"Error during loading: {e}\")\n", "    print(f\"Please check that preprocessing was performed correctly.\")"]}, {"cell_type": "code", "execution_count": 24, "id": "2907fe1b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== STEP 4.2: LOADING FINBERT MODEL ===\n", "Loading model ProsusAI/finbert...\n", "FinBERT model loaded successfully\n", "  - Number of labels: 3\n", "  - Number of parameters: 109,484,547\n", "  - Label mapping: {0: 'Negative', 1: 'Neutral', 2: 'Positive'}\n", "  - Model moved to: cpu\n", "\n", "Model architecture:\n", "  - Type: BertForSequenceClassification\n", "  - Classification layer: Linear(in_features=768, out_features=3, bias=True)\n", "FinBERT model loaded successfully\n", "  - Number of labels: 3\n", "  - Number of parameters: 109,484,547\n", "  - Label mapping: {0: 'Negative', 1: 'Neutral', 2: 'Positive'}\n", "  - Model moved to: cpu\n", "\n", "Model architecture:\n", "  - Type: BertForSequenceClassification\n", "  - Classification layer: Linear(in_features=768, out_features=3, bias=True)\n"]}], "source": ["# Step 4.2: Loading FinBERT model\n", "print(\"\\n=== STEP 4.2: LOADING FINBERT MODEL ===\")\n", "\n", "# Model configuration\n", "model_name = \"ProsusAI/finbert\"\n", "num_labels = 3  # Negative, Neutral, Positive\n", "\n", "try:\n", "    # Load FinBERT model for sequence classification\n", "    print(f\"Loading model {model_name}...\")\n", "    model = AutoModelForSequenceClassification.from_pretrained(\n", "        model_name,\n", "        num_labels=num_labels,\n", "        problem_type=\"single_label_classification\"\n", "    )\n", "    \n", "    print(f\"FinBERT model loaded successfully\")\n", "    print(f\"  - Number of labels: {num_labels}\")\n", "    print(f\"  - Number of parameters: {model.num_parameters():,}\")\n", "    \n", "    # Label configuration\n", "    label_names = ['Negative', 'Neutral', 'Positive']\n", "    id2label = {i: label for i, label in enumerate(label_names)}\n", "    label2id = {label: i for i, label in enumerate(label_names)}\n", "    \n", "    model.config.id2label = id2label\n", "    model.config.label2id = label2id\n", "    \n", "    print(f\"  - Label mapping: {id2label}\")\n", "    \n", "    # Move model to appropriate device\n", "    model = model.to(device)\n", "    print(f\"  - Model moved to: {device}\")\n", "    \n", "    # Display model architecture\n", "    print(f\"\\nModel architecture:\")\n", "    print(f\"  - Type: {model.__class__.__name__}\")\n", "    print(f\"  - Classification layer: {model.classifier}\")\n", "    \n", "except Exception as e:\n", "    print(f\"Error loading model: {e}\")"]}, {"cell_type": "code", "execution_count": 27, "id": "ca6cbe72", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== STEP 4.3: METRICS CONFIGURATION ===\n", "Metrics function configured\n", "Metrics to be calculated:\n", "  - Global accuracy\n", "  - F1-score (weighted and per class)\n", "  - Precision (weighted and per class)\n", "  - Recall (weighted and per class)\n"]}], "source": ["# Step 4.3: Evaluation metrics configuration\n", "print(\"\\n=== STEP 4.3: METRICS CONFIGURATION ===\")\n", "\n", "def compute_metrics(eval_pred):\n", "    \"\"\"Calculate evaluation metrics\"\"\"\n", "    predictions, labels = eval_pred\n", "    predictions = np.argmax(predictions, axis=1)\n", "    \n", "    # Calculate basic metrics\n", "    accuracy = accuracy_score(labels, predictions)\n", "    precision, recall, f1, _ = precision_recall_fscore_support(labels, predictions, average='weighted')\n", "    \n", "    # Per-class metrics\n", "    precision_per_class, recall_per_class, f1_per_class, support = precision_recall_fscore_support(\n", "        labels, predictions, average=None\n", "    )\n", "    \n", "    # Create metrics dictionary\n", "    metrics = {\n", "        'accuracy': accuracy,\n", "        'f1': f1,\n", "        'precision': precision,\n", "        'recall': recall,\n", "    }\n", "    \n", "    # Add per-class metrics\n", "    for i, label_name in enumerate(['negative', 'neutral', 'positive']):\n", "        metrics[f'f1_{label_name}'] = f1_per_class[i]\n", "        metrics[f'precision_{label_name}'] = precision_per_class[i]\n", "        metrics[f'recall_{label_name}'] = recall_per_class[i]\n", "    \n", "    return metrics\n", "\n", "print(\"Metrics function configured\")\n", "print(\"Metrics to be calculated:\")\n", "print(\"  - Global accuracy\")\n", "print(\"  - F1-score (weighted and per class)\")\n", "print(\"  - Precision (weighted and per class)\")\n", "print(\"  - Recall (weighted and per class)\")"]}, {"cell_type": "code", "execution_count": 28, "id": "6fb743cd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== STEP 4.4: HYPERPARAMETERS CONFIGURATION ===\n", "Training configuration:\n", "  Output folder: ./finbert_results\n", "  Learning rate: 2e-05\n", "  Batch size (train/eval): 16/16\n", "  Number of epochs: 3\n", "  Weight decay: 0.01\n", "  Warmup steps: 100\n", "  Evaluation: every epoch\n", "  Saving: best model according to F1-score\n", "  FP16: False\n", "\n", "Training estimation:\n", "  - Steps per epoch: 94\n", "  - Total steps: 282\n", "  - Estimated duration: ~9.4 minutes (estimation)\n"]}], "source": ["# Step 4.4: Training hyperparameters configuration\n", "print(\"\\n=== STEP 4.4: HYPERPARAMETERS CONFIGURATION ===\")\n", "\n", "# Create output folder\n", "output_dir = \"./finbert_results\"\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "# Training arguments configuration\n", "training_args = TrainingArguments(\n", "    # Output folders\n", "    output_dir=output_dir,\n", "    logging_dir=f\"{output_dir}/logs\",\n", "    \n", "    # Evaluation strategy (eval_strategy replaces evaluation_strategy)\n", "    eval_strategy=\"epoch\",\n", "    save_strategy=\"epoch\",\n", "    logging_strategy=\"steps\",\n", "    \n", "    # Training hyperparameters\n", "    learning_rate=2e-5,\n", "    per_device_train_batch_size=16,\n", "    per_device_eval_batch_size=16,\n", "    num_train_epochs=3,\n", "    weight_decay=0.01,\n", "    \n", "    # Optimization and stability\n", "    warmup_steps=100,\n", "    logging_steps=50,\n", "    save_total_limit=2,\n", "    \n", "    # Best model selection\n", "    load_best_model_at_end=True,\n", "    metric_for_best_model=\"f1\",\n", "    greater_is_better=True,\n", "    \n", "    # Other configurations\n", "    seed=42,\n", "    fp16=torch.cuda.is_available(),  # Use mixed precision if GPU available\n", "    dataloader_num_workers=0,  # Avoid multiprocessing issues\n", "    remove_unused_columns=False,\n", "    \n", "    # Reporting and saving\n", "    report_to=\"none\",  # No external logging\n", "    push_to_hub=False\n", ")\n", "\n", "print(\"Training configuration:\")\n", "print(f\"  Output folder: {output_dir}\")\n", "print(f\"  Learning rate: {training_args.learning_rate}\")\n", "print(f\"  Batch size (train/eval): {training_args.per_device_train_batch_size}/{training_args.per_device_eval_batch_size}\")\n", "print(f\"  Number of epochs: {training_args.num_train_epochs}\")\n", "print(f\"  Weight decay: {training_args.weight_decay}\")\n", "print(f\"  Warmup steps: {training_args.warmup_steps}\")\n", "print(f\"  Evaluation: every epoch\")\n", "print(f\"  Saving: best model according to F1-score\")\n", "print(f\"  FP16: {training_args.fp16}\")\n", "\n", "# Calculate total number of steps\n", "total_steps = (len(train_dataset) // training_args.per_device_train_batch_size) * training_args.num_train_epochs\n", "print(f\"\\nTraining estimation:\")\n", "print(f\"  - Steps per epoch: {len(train_dataset) // training_args.per_device_train_batch_size}\")\n", "print(f\"  - Total steps: {total_steps}\")\n", "print(f\"  - Estimated duration: ~{total_steps * 2 / 60:.1f} minutes (estimation)\")"]}, {"cell_type": "code", "execution_count": 29, "id": "fe9f25cd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== STEP 4.5: TRAINER CREATION ===\n", "Trainer created successfully\n", "Configuration:\n", "  - Training dataset: 1508 examples\n", "  - Evaluation dataset: 266 examples\n", "  - Tokenizer: BertTokenizerFast\n", "  - Early stopping: enabled (patience=2)\n", "  - Custom metrics: enabled\n", "\n", "Configuration verification:\n", "  - Model on device: cpu\n", "  - Number of trainable parameters: 109,484,547\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23644\\690550671.py:12: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.\n", "  trainer = Trainer(\n"]}], "source": ["# Step 4.5: Trainer creation\n", "print(\"\\n=== STEP 4.5: TRAINER CREATION ===\")\n", "\n", "# Early stopping callback (optional)\n", "early_stopping = EarlyStoppingCallback(\n", "    early_stopping_patience=2,\n", "    early_stopping_threshold=0.001\n", ")\n", "\n", "try:\n", "    # Create trainer\n", "    trainer = Trainer(\n", "        model=model,\n", "        args=training_args,\n", "        train_dataset=train_dataset,\n", "        eval_dataset=test_dataset,\n", "        tokenizer=tokenizer,\n", "        compute_metrics=compute_metrics,\n", "        callbacks=[early_stopping]\n", "    )\n", "    \n", "    print(\"<PERSON><PERSON> created successfully\")\n", "    print(f\"Configuration:\")\n", "    print(f\"  - Training dataset: {len(train_dataset)} examples\")\n", "    print(f\"  - Evaluation dataset: {len(test_dataset)} examples\")\n", "    print(f\"  - Tokenizer: {tokenizer.__class__.__name__}\")\n", "    print(f\"  - Early stopping: enabled (patience=2)\")\n", "    print(f\"  - Custom metrics: enabled\")\n", "    \n", "    # Check configuration\n", "    print(f\"\\nConfiguration verification:\")\n", "    print(f\"  - Model on device: {next(model.parameters()).device}\")\n", "    print(f\"  - Number of trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}\")\n", "    \n", "except Exception as e:\n", "    print(f\"Error creating trainer: {e}\")"]}, {"cell_type": "code", "execution_count": 30, "id": "3c926ff4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== STEP 4.6: INITIAL EVALUATION ===\n", "Evaluating model before fine-tuning...\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='34' max='17' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [17/17 57:20]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Initial metrics (pre-trained model):\n", "  Accuracy: 0.1504 (15.04%)\n", "  F1-score: 0.1293\n", "  Precision: 0.3164\n", "  Recall: 0.1504\n", "\n", "Per-sentiment metrics:\n", "  Negative:\n", "    F1: 0.0000, Precision: 0.0000, Recall: 0.0000\n", "  Neutral:\n", "    F1: 0.0599, Precision: 0.4167, Recall: 0.0323\n", "  Positive:\n", "    F1: 0.2536, Precision: 0.1977, Recall: 0.3535\n", "\n", "Initial metrics saved in: ./finbert_results/initial_evaluation.json\n"]}], "source": ["# Step 4.6: Initial evaluation (before training)\n", "print(\"\\n=== STEP 4.6: INITIAL EVALUATION ===\")\n", "\n", "print(\"Evaluating model before fine-tuning...\")\n", "try:\n", "    # Evaluate pre-trained model\n", "    initial_eval = trainer.evaluate()\n", "    \n", "    print(\"\\nInitial metrics (pre-trained model):\")\n", "    print(f\"  Accuracy: {initial_eval['eval_accuracy']:.4f} ({initial_eval['eval_accuracy']*100:.2f}%)\")\n", "    print(f\"  F1-score: {initial_eval['eval_f1']:.4f}\")\n", "    print(f\"  Precision: {initial_eval['eval_precision']:.4f}\")\n", "    print(f\"  Recall: {initial_eval['eval_recall']:.4f}\")\n", "    \n", "    # Per-class metrics\n", "    print(f\"\\nPer-sentiment metrics:\")\n", "    for sentiment in ['negative', 'neutral', 'positive']:\n", "        f1 = initial_eval.get(f'eval_f1_{sentiment}', 0)\n", "        precision = initial_eval.get(f'eval_precision_{sentiment}', 0)\n", "        recall = initial_eval.get(f'eval_recall_{sentiment}', 0)\n", "        print(f\"  {sentiment.capitalize()}:\")\n", "        print(f\"    F1: {f1:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}\")\n", "    \n", "    # Save initial metrics\n", "    initial_metrics = {\n", "        'evaluation_type': 'initial (pre-trained)',\n", "        'timestamp': datetime.now().isoformat(),\n", "        'metrics': initial_eval\n", "    }\n", "    \n", "    with open(os.path.join(output_dir, 'initial_evaluation.json'), 'w') as f:\n", "        json.dump(initial_metrics, f, indent=2)\n", "    \n", "    print(f\"\\nInitial metrics saved in: {output_dir}/initial_evaluation.json\")\n", "    \n", "except Exception as e:\n", "    print(f\"Error during initial evaluation: {e}\")\n", "    initial_eval = None"]}, {"cell_type": "code", "execution_count": 31, "id": "11b093bc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== STEP 4.7: MODEL TRAINING ===\n", "Starting FinBERT training...\n", "Start time: 11:26:01\n", "\n", "============================================================\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\torch\\utils\\data\\dataloader.py:665: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='285' max='285' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [285/285 2:36:23, Epoch 3/3]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "      <th>Model Preparation Time</th>\n", "      <th>Accuracy</th>\n", "      <th>F1</th>\n", "      <th>Precision</th>\n", "      <th>Recall</th>\n", "      <th>F1 Negative</th>\n", "      <th>Precision Negative</th>\n", "      <th>Recall Negative</th>\n", "      <th>F1 Neutral</th>\n", "      <th>Precision Neutral</th>\n", "      <th>Recall Neutral</th>\n", "      <th>F1 Positive</th>\n", "      <th>Precision Positive</th>\n", "      <th>Recall Positive</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>1.578300</td>\n", "      <td>0.578270</td>\n", "      <td>0.002200</td>\n", "      <td>0.755639</td>\n", "      <td>0.735655</td>\n", "      <td>0.721326</td>\n", "      <td>0.755639</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.807229</td>\n", "      <td>0.757062</td>\n", "      <td>0.864516</td>\n", "      <td>0.712766</td>\n", "      <td>0.752809</td>\n", "      <td>0.676768</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.432200</td>\n", "      <td>0.468930</td>\n", "      <td>0.002200</td>\n", "      <td>0.823308</td>\n", "      <td>0.804538</td>\n", "      <td>0.786725</td>\n", "      <td>0.823308</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.863492</td>\n", "      <td>0.850000</td>\n", "      <td>0.877419</td>\n", "      <td>0.809756</td>\n", "      <td>0.783019</td>\n", "      <td>0.838384</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.256300</td>\n", "      <td>0.495274</td>\n", "      <td>0.002200</td>\n", "      <td>0.838346</td>\n", "      <td>0.824054</td>\n", "      <td>0.845640</td>\n", "      <td>0.838346</td>\n", "      <td>0.153846</td>\n", "      <td>1.000000</td>\n", "      <td>0.083333</td>\n", "      <td>0.872611</td>\n", "      <td>0.861635</td>\n", "      <td>0.883871</td>\n", "      <td>0.829268</td>\n", "      <td>0.801887</td>\n", "      <td>0.858586</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1565: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1565: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\torch\\utils\\data\\dataloader.py:665: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\torch\\utils\\data\\dataloader.py:665: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1565: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1565: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1565: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1565: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\torch\\utils\\data\\dataloader.py:665: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\torch\\utils\\data\\dataloader.py:665: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "TRAINING COMPLETED SUCCESSFULLY!\n", "Total duration: 2:37:00.734304\n", "End time: 14:03:02\n", "\n", "Training results:\n", "  - Completed epochs: 3.0\n", "  - Total steps: 285\n", "  - Final loss: 0.5964\n", "\n", "Training information saved\n"]}], "source": ["# Step 4.7: Model training launch\n", "print(\"\\n=== STEP 4.7: MODEL TRAINING ===\")\n", "\n", "print(\"Starting FinBERT training...\")\n", "print(f\"Start time: {datetime.now().strftime('%H:%M:%S')}\")\n", "print(\"\\n\" + \"=\"*60)\n", "\n", "try:\n", "    # Start training\n", "    start_time = datetime.now()\n", "    \n", "    training_result = trainer.train()\n", "    \n", "    end_time = datetime.now()\n", "    training_duration = end_time - start_time\n", "    \n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"TRAINING COMPLETED SUCCESSFULLY!\")\n", "    print(f\"Total duration: {training_duration}\")\n", "    print(f\"End time: {end_time.strftime('%H:%M:%S')}\")\n", "    \n", "    # Display training results\n", "    print(f\"\\nTraining results:\")\n", "    print(f\"  - Completed epochs: {training_result.global_step / (len(train_dataset) // training_args.per_device_train_batch_size):.1f}\")\n", "    print(f\"  - Total steps: {training_result.global_step}\")\n", "    print(f\"  - Final loss: {training_result.training_loss:.4f}\")\n", "    \n", "    # Save training information\n", "    training_info = {\n", "        'start_time': start_time.isoformat(),\n", "        'end_time': end_time.isoformat(),\n", "        'duration_seconds': training_duration.total_seconds(),\n", "        'duration_formatted': str(training_duration),\n", "        'global_step': training_result.global_step,\n", "        'training_loss': training_result.training_loss,\n", "        'epochs_completed': training_result.global_step / (len(train_dataset) // training_args.per_device_train_batch_size),\n", "        'training_args': training_args.to_dict()\n", "    }\n", "    \n", "    with open(os.path.join(output_dir, 'training_info.json'), 'w') as f:\n", "        json.dump(training_info, f, indent=2, default=str)\n", "    \n", "    print(f\"\\nTraining information saved\")\n", "    \n", "except Exception as e:\n", "    print(f\"\\nError during training: {e}\")\n", "    print(f\"Check logs in {output_dir}/logs for more details\")"]}, {"cell_type": "code", "execution_count": 32, "id": "199ecd57", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== STEP 4.8: FINAL EVALUATION ===\n", "Evaluating model after fine-tuning...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\torch\\utils\\data\\dataloader.py:665: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n"]}, {"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "FINAL METRICS (after fine-tuning):\n", "==================================================\n", "Accuracy: 0.8383 (83.83%)\n", "F1-score: 0.8241\n", "Precision: 0.8456\n", "Recall: 0.8383\n", "\n", "PER-SENTIMENT METRICS:\n", "----------------------------------------\n", "NEGATIVE:\n", "   F1: 0.1538 | Precision: 1.0000 | Recall: 0.0833\n", "NEUTRAL:\n", "   F1: 0.8726 | Precision: 0.8616 | Recall: 0.8839\n", "POSITIVE:\n", "   F1: 0.8293 | Precision: 0.8019 | Recall: 0.8586\n", "\n", "IMPROVEMENT AFTER FINE-TUNING:\n", "---------------------------------------------\n", "Accuracy: +0.6880 (+68.80 percentage points)\n", "F1-score: +0.6948 (+69.48%)\n", "Fine-tuning improved performance!\n", "\n", "Final metrics saved\n"]}], "source": ["# Step 4.8: Final evaluation of trained model\n", "print(\"\\n=== STEP 4.8: FINAL EVALUATION ===\")\n", "\n", "print(\"Evaluating model after fine-tuning...\")\n", "\n", "try:\n", "    # Evaluate final model\n", "    final_eval = trainer.evaluate()\n", "    \n", "    print(\"\\nFINAL METRICS (after fine-tuning):\")\n", "    print(\"=\"*50)\n", "    print(f\"Accuracy: {final_eval['eval_accuracy']:.4f} ({final_eval['eval_accuracy']*100:.2f}%)\")\n", "    print(f\"F1-score: {final_eval['eval_f1']:.4f}\")\n", "    print(f\"Precision: {final_eval['eval_precision']:.4f}\")\n", "    print(f\"Recall: {final_eval['eval_recall']:.4f}\")\n", "    \n", "    # Detailed per-class metrics\n", "    print(f\"\\nPER-SENTIMENT METRICS:\")\n", "    print(\"-\"*40)\n", "    for sentiment in ['negative', 'neutral', 'positive']:\n", "        f1 = final_eval.get(f'eval_f1_{sentiment}', 0)\n", "        precision = final_eval.get(f'eval_precision_{sentiment}', 0)\n", "        recall = final_eval.get(f'eval_recall_{sentiment}', 0)\n", "        print(f\"{sentiment.upper()}:\")\n", "        print(f\"   F1: {f1:.4f} | Precision: {precision:.4f} | Recall: {recall:.4f}\")\n", "    \n", "    # Comparison with initial evaluation\n", "    if 'initial_eval' in locals() and initial_eval is not None:\n", "        print(f\"\\nIMPROVEMENT AFTER FINE-TUNING:\")\n", "        print(\"-\"*45)\n", "        acc_improvement = final_eval['eval_accuracy'] - initial_eval['eval_accuracy']\n", "        f1_improvement = final_eval['eval_f1'] - initial_eval['eval_f1']\n", "        \n", "        print(f\"Accuracy: {acc_improvement:+.4f} ({acc_improvement*100:+.2f} percentage points)\")\n", "        print(f\"F1-score: {f1_improvement:+.4f} ({f1_improvement*100:+.2f}%)\")\n", "        \n", "        if acc_improvement > 0:\n", "            print(f\"Fine-tuning improved performance!\")\n", "        else:\n", "            print(f\"Fine-tuning did not improve performance\")\n", "    \n", "    # Save final metrics\n", "    final_metrics = {\n", "        'evaluation_type': 'final (after fine-tuning)',\n", "        'timestamp': datetime.now().isoformat(),\n", "        'metrics': final_eval,\n", "        'improvement_over_initial': {\n", "            'accuracy': acc_improvement if 'acc_improvement' in locals() else None,\n", "            'f1': f1_improvement if 'f1_improvement' in locals() else None\n", "        } if 'initial_eval' in locals() and initial_eval is not None else None\n", "    }\n", "    \n", "    with open(os.path.join(output_dir, 'final_evaluation.json'), 'w') as f:\n", "        json.dump(final_metrics, f, indent=2)\n", "    \n", "    print(f\"\\nFinal metrics saved\")\n", "    \n", "except Exception as e:\n", "    print(f\"Error during final evaluation: {e}\")"]}, {"cell_type": "code", "execution_count": 37, "id": "b090643e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== STEP 4.9: DETAILED PREDICTION ANALYSIS ===\n", "Generating predictions on test dataset...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\torch\\utils\\data\\dataloader.py:665: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 7 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "DETAILED PERFORMANCE METRICS:\n", "============================================================\n", "Global Accuracy: 0.8383 (83.83%)\n", "\n", "Accuracy per Class:\n", "  Negative: 0.0833 (8.33%) - 12 samples\n", "  Neutral: 0.8839 (88.39%) - 155 samples\n", "  Positive: 0.8586 (85.86%) - 99 samples\n", "\n", "Confidence Metrics:\n", "  Mean confidence: 0.9226\n", "  Median confidence: 0.9712\n", "  Confidence std dev: 0.1076\n", "  High confidence predictions (>0.9): 215 (80.8%)\n", "  Low confidence predictions (<0.6): 13 (4.9%)\n", "\n", "DETAILED ERROR ANALYSIS:\n", "--------------------------------------------------\n", "Total errors: 43 out of 266 (16.2%)\n", "\n", "Confidence in errors vs correct predictions:\n", "  Mean confidence (errors): 0.8304\n", "  Mean confidence (correct): 0.9403\n", "  Difference: 0.1099\n", "\n", "Most frequent confusions:\n", "  Neutral -> Positive: 18 errors (41.9% of errors)\n", "  Positive -> Neutral: 14 errors (32.6% of errors)\n", "  Negative -> Neutral: 8 errors (18.6% of errors)\n", "\n", "DETAILED MODEL INFORMATION:\n", "--------------------------------------------------\n", "  Model name: ProsusAI/finbert\n", "  Architecture: BertForSequenceClassification\n", "  Total parameters: 109,484,547\n", "  Trainable parameters: 109,484,547\n", "  Vocabulary size: 30,522\n", "  Max sequence length: 512\n", "  Device: cpu\n", "Error during analysis: Object of type int64 is not JSON serializable\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Traceback (most recent call last):\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23644\\3970378388.py\", line 176, in <module>\n", "    json.dump(comprehensive_analysis, f, indent=2, ensure_ascii=False)\n", "    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\__init__.py\", line 179, in dump\n", "    for chunk in iterable:\n", "                 ^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\encoder.py\", line 432, in _iterencode\n", "    yield from _iterencode_dict(o, _current_indent_level)\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\encoder.py\", line 406, in _iterencode_dict\n", "    yield from chunks\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\encoder.py\", line 326, in _iterencode_list\n", "    yield from chunks\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\encoder.py\", line 326, in _iterencode_list\n", "    yield from chunks\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\encoder.py\", line 439, in _iterencode\n", "    o = _default(o)\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\encoder.py\", line 180, in default\n", "    raise TypeError(f'Object of type {o.__class__.__name__} '\n", "                    f'is not JSON serializable')\n", "TypeError: Object of type int64 is not JSON serializable\n"]}], "source": ["# Step 4.9: Detailed prediction analysis and advanced metrics\n", "print(\"\\n=== STEP 4.9: DETAILED PREDICTION ANALYSIS ===\")\n", "\n", "try:\n", "    # Get predictions on test dataset\n", "    print(\"Generating predictions on test dataset...\")\n", "    predictions = trainer.predict(test_dataset)\n", "    \n", "    # Extract predictions and true labels\n", "    y_pred = np.argmax(predictions.predictions, axis=1)\n", "    y_true = predictions.label_ids\n", "    prediction_probabilities = torch.nn.functional.softmax(torch.tensor(predictions.predictions), dim=-1).numpy()\n", "    \n", "    # Create confusion matrix\n", "    cm = confusion_matrix(y_true, y_pred)\n", "    \n", "    # === MULTIPLE VISUALIZATIONS ===\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    \n", "    # 1. Normalized confusion matrix\n", "    cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]\n", "    sns.heatmap(cm_normalized, \n", "                annot=True, \n", "                fmt='.3f', \n", "                cmap='Blues',\n", "                xticklabels=['Negative', 'Neutral', 'Positive'],\n", "                yticklabels=['Negative', 'Neutral', 'Positive'],\n", "                ax=axes[0,0])\n", "    axes[0,0].set_title('Normalized Confusion Matrix', fontweight='bold')\n", "    axes[0,0].set_xlabel('Predictions')\n", "    axes[0,0].set_ylabel('True Labels')\n", "    \n", "    # 2. Raw confusion matrix\n", "    sns.heatmap(cm, \n", "                annot=True, \n", "                fmt='d', \n", "                cmap='Oranges',\n", "                xticklabels=['Negative', 'Neutral', 'Positive'],\n", "                yticklabels=['Negative', 'Neutral', 'Positive'],\n", "                ax=axes[0,1])\n", "    axes[0,1].set_title('Confusion Matrix (Absolute Numbers)', fontweight='bold')\n", "    axes[0,1].set_xlabel('Predictions')\n", "    axes[0,1].set_ylabel('True Labels')\n", "    \n", "    # 3. Probability correlation matrix\n", "    correlation_matrix = np.corrcoef(prediction_probabilities.T)\n", "    sns.heatmap(correlation_matrix,\n", "                annot=True,\n", "                fmt='.3f',\n", "                cmap='RdBu_r',\n", "                center=0,\n", "                xticklabels=['Negative', 'Neutral', 'Positive'],\n", "                yticklabels=['Negative', 'Neutral', 'Positive'],\n", "                ax=axes[1,0])\n", "    axes[1,0].set_title('Probability Correlation Matrix', fontweight='bold')\n", "    \n", "    # 4. Prediction confidence distribution\n", "    max_probs = np.max(prediction_probabilities, axis=1)\n", "    axes[1,1].hist(max_probs, bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n", "    axes[1,1].axvline(np.mean(max_probs), color='red', linestyle='--', \n", "                      label=f'Mean: {np.mean(max_probs):.3f}')\n", "    axes[1,1].set_title('Prediction Confidence Distribution', fontweight='bold')\n", "    axes[1,1].set_xlabel('Maximum Probability')\n", "    axes[1,1].set_ylabel('Frequency')\n", "    axes[1,1].legend()\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig(os.path.join(output_dir, 'comprehensive_analysis.png'), dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "    \n", "    # === DETAILED PERFORMANCE METRICS ===\n", "    print(f\"\\nDETAILED PERFORMANCE METRICS:\")\n", "    print(\"=\"*60)\n", "    \n", "    # Global and per-class accuracy\n", "    overall_accuracy = accuracy_score(y_true, y_pred)\n", "    print(f\"Global Accuracy: {overall_accuracy:.4f} ({overall_accuracy*100:.2f}%)\")\n", "    \n", "    # Per-class accuracy\n", "    print(f\"\\nAccuracy per Class:\")\n", "    label_names = ['Negative', 'Neutral', 'Positive']\n", "    for i, label in enumerate(label_names):\n", "        class_mask = (y_true == i)\n", "        if np.sum(class_mask) > 0:\n", "            class_accuracy = accuracy_score(y_true[class_mask], y_pred[class_mask])\n", "            print(f\"  {label}: {class_accuracy:.4f} ({class_accuracy*100:.2f}%) - {np.sum(class_mask)} samples\")\n", "    \n", "    # Confidence metrics\n", "    print(f\"\\nConfidence Metrics:\")\n", "    print(f\"  Mean confidence: {np.mean(max_probs):.4f}\")\n", "    print(f\"  Median confidence: {np.median(max_probs):.4f}\")\n", "    print(f\"  Confidence std dev: {np.std(max_probs):.4f}\")\n", "    print(f\"  High confidence predictions (>0.9): {np.sum(max_probs > 0.9)} ({np.sum(max_probs > 0.9)/len(max_probs)*100:.1f}%)\")\n", "    print(f\"  Low confidence predictions (<0.6): {np.sum(max_probs < 0.6)} ({np.sum(max_probs < 0.6)/len(max_probs)*100:.1f}%)\")\n", "    \n", "    # === ERROR ANALYSIS ===\n", "    print(f\"\\nDETAILED ERROR ANALYSIS:\")\n", "    print(\"-\"*50)\n", "    \n", "    total_errors = len(y_true) - np.trace(cm)\n", "    print(f\"Total errors: {total_errors} out of {len(y_true)} ({total_errors/len(y_true)*100:.1f}%)\")\n", "    \n", "    # Analyze errors by confidence\n", "    error_mask = (y_true != y_pred)\n", "    if np.sum(error_mask) > 0:\n", "        error_confidences = max_probs[error_mask]\n", "        correct_confidences = max_probs[~error_mask]\n", "        \n", "        print(f\"\\nConfidence in errors vs correct predictions:\")\n", "        print(f\"  Mean confidence (errors): {np.mean(error_confidences):.4f}\")\n", "        print(f\"  Mean confidence (correct): {np.mean(correct_confidences):.4f}\")\n", "        print(f\"  Difference: {np.mean(correct_confidences) - np.mean(error_confidences):.4f}\")\n", "    \n", "    # Most frequent confusions\n", "    print(f\"\\nMost frequent confusions:\")\n", "    confusions = [\n", "        (\"Negative -> Neutral\", cm[0,1]),\n", "        (\"Negative -> Positive\", cm[0,2]),\n", "        (\"Neutral -> Negative\", cm[1,0]),\n", "        (\"Neutral -> Positive\", cm[1,2]),\n", "        (\"Positive -> Negative\", cm[2,0]),\n", "        (\"Positive -> Neutral\", cm[2,1])\n", "    ]\n", "    \n", "    confusions.sort(key=lambda x: x[1], reverse=True)\n", "    for confusion_type, count in confusions[:3]:  # Top 3\n", "        if count > 0:\n", "            percentage = count / total_errors * 100 if total_errors > 0 else 0\n", "            print(f\"  {confusion_type}: {count} errors ({percentage:.1f}% of errors)\")\n", "    \n", "    # === DETAILED MODEL INFORMATION ===\n", "    print(f\"\\nDETAILED MODEL INFORMATION:\")\n", "    print(\"-\"*50)\n", "    print(f\"  Model name: {model_name}\")\n", "    print(f\"  Architecture: {model.__class__.__name__}\")\n", "    print(f\"  Total parameters: {model.num_parameters():,}\")\n", "    print(f\"  Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}\")\n", "    print(f\"  Vocabulary size: {tokenizer.vocab_size:,}\")\n", "    print(f\"  Max sequence length: {tokenizer.model_max_length}\")\n", "    print(f\"  Device: {next(model.parameters()).device}\")\n", "    \n", "    # Save all analyses\n", "    comprehensive_analysis = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'model_info': {\n", "            'name': model_name,\n", "            'architecture': model.__class__.__name__,\n", "            'total_parameters': int(model.num_parameters()),\n", "            'trainable_parameters': int(sum(p.numel() for p in model.parameters() if p.requires_grad)),\n", "            'vocab_size': int(tokenizer.vocab_size),\n", "            'max_length': int(tokenizer.model_max_length)\n", "        },\n", "        'performance_metrics': {\n", "            'overall_accuracy': float(overall_accuracy),\n", "            'total_predictions': int(len(y_true)),\n", "            'total_errors': int(total_errors),\n", "            'error_rate': float(total_errors / len(y_true)),\n", "            'confidence_metrics': {\n", "                'mean_confidence': float(np.mean(max_probs)),\n", "                'median_confidence': float(np.median(max_probs)),\n", "                'std_confidence': float(np.std(max_probs)),\n", "                'high_confidence_predictions': int(np.sum(max_probs > 0.9)),\n", "                'low_confidence_predictions': int(np.sum(max_probs < 0.6))\n", "            }\n", "        },\n", "        'confusion_matrix': cm.tolist(),\n", "        'confusion_matrix_normalized': cm_normalized.tolist(),\n", "        'correlation_matrix': correlation_matrix.tolist(),\n", "        'predictions': y_pred.tolist(),\n", "        'true_labels': y_true.tolist(),\n", "        'prediction_probabilities': prediction_probabilities.tolist(),\n", "        'top_confusions': confusions\n", "    }\n", "    \n", "    with open(os.path.join(output_dir, 'comprehensive_analysis.json'), 'w', encoding='utf-8') as f:\n", "        json.dump(comprehensive_analysis, f, indent=2, ensure_ascii=False)\n", "    \n", "    print(f\"\\nComprehensive analysis saved in: {output_dir}/comprehensive_analysis.json\")\n", "    \n", "except Exception as e:\n", "    print(f\"Error during analysis: {e}\")\n", "    import traceback\n", "    traceback.print_exc()"]}, {"cell_type": "code", "execution_count": 38, "id": "2a867bd8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== STEP 4.9.1: ADVANCED CORRELATION ANALYSIS ===\n", "Correlation analysis between features...\n", "\n", "DETAILED STATISTICS PER CLASS:\n", "=================================================================\n", "\n", "NEGATIVE:\n", "  Samples: 12\n", "  Accuracy: 0.0833 (8.33%)\n", "  Mean confidence: 0.8723\n", "  Mean prob (own class): 0.1083 ± 0.2105\n", "\n", "NEUTRAL:\n", "  Samples: 155\n", "  Accuracy: 0.8839 (88.39%)\n", "  Mean confidence: 0.9331\n", "  Mean prob (own class): 0.8602 ± 0.2664\n", "\n", "POSITIVE:\n", "  Samples: 99\n", "  Accuracy: 0.8586 (85.86%)\n", "  Mean confidence: 0.9121\n", "  Mean prob (own class): 0.8115 ± 0.2964\n", "\n", "EXTENDED CORRELATION ANALYSIS:\n", "---------------------------------------------\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "FINAL MODEL PERFORMANCE SUMMARY:\n", "=======================================================\n", "Global Performance Metrics:\n", "  Accuracy: 0.8383 (83.83%)\n", "  Confidence-Accuracy Correlation: 0.3759\n", "  Calibration Error: 0.1843\n", "  Mean Confidence: 0.9226\n", "\n", "Robustness Metrics:\n", "  High Confidence Predictions (>0.9): 215 (80.8%)\n", "  Uncertain Predictions (0.4-0.6): 13\n", "  Confident Errors (>0.8): 31\n", "\n", "OVERALL MODEL EVALUATION: GOOD\n", "   Performance: 83.8% accuracy\n", "   Calibration: Poorly calibrated\n", "   Confidence: Needs adjustment\n", "\n", "Correlation analysis saved in: ./finbert_results/correlation_analysis.json\n"]}], "source": ["# Step 4.9.1: Advanced correlation analysis and performance summary\n", "print(\"\\n=== STEP 4.9.1: ADVANCED CORRELATION ANALYSIS ===\")\n", "\n", "try:\n", "    # === CORRELATION ANALYSIS BETWEEN FEATURES ===\n", "    print(\"Correlation analysis between features...\")\n", "    \n", "    # Calculate statistics per class\n", "    class_stats = {}\n", "    for class_idx, class_name in enumerate(['Negative', 'Neutral', 'Positive']):\n", "        class_mask = (y_true == class_idx)\n", "        class_probs = prediction_probabilities[class_mask]\n", "        class_predictions = y_pred[class_mask]\n", "        \n", "        if len(class_probs) > 0:\n", "            class_stats[class_name] = {\n", "                'count': int(np.sum(class_mask)),\n", "                'mean_confidence': float(np.mean(np.max(class_probs, axis=1))),\n", "                'correct_predictions': int(np.sum(class_predictions == class_idx)),\n", "                'accuracy': float(np.sum(class_predictions == class_idx) / len(class_predictions)),\n", "                'mean_prob_own_class': float(np.mean(class_probs[:, class_idx])),\n", "                'std_prob_own_class': float(np.std(class_probs[:, class_idx]))\n", "            }\n", "    \n", "    # Display per-class statistics\n", "    print(f\"\\nDETAILED STATISTICS PER CLASS:\")\n", "    print(\"=\"*65)\n", "    for class_name, stats in class_stats.items():\n", "        print(f\"\\n{class_name.upper()}:\")\n", "        print(f\"  Samples: {stats['count']}\")\n", "        print(f\"  Accuracy: {stats['accuracy']:.4f} ({stats['accuracy']*100:.2f}%)\")\n", "        print(f\"  Mean confidence: {stats['mean_confidence']:.4f}\")\n", "        print(f\"  Mean prob (own class): {stats['mean_prob_own_class']:.4f} ± {stats['std_prob_own_class']:.4f}\")\n", "    \n", "    # === EXTENDED CORRELATION MATRIX ===\n", "    print(f\"\\nEXTENDED CORRELATION ANALYSIS:\")\n", "    print(\"-\"*45)\n", "    \n", "    # Create figure for extended correlations\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # 1. Probability correlation heatmap by true class\n", "    true_class_probs = []\n", "    for i in range(3):\n", "        class_mask = (y_true == i)\n", "        if np.sum(class_mask) > 0:\n", "            true_class_probs.append(np.mean(prediction_probabilities[class_mask], axis=0))\n", "    \n", "    true_class_corr = np.corrcoef(true_class_probs)\n", "    sns.heatmap(true_class_corr,\n", "                annot=True,\n", "                fmt='.3f',\n", "                cmap='RdBu_r',\n", "                center=0,\n", "                xticklabels=['Negative', 'Neutral', 'Positive'],\n", "                yticklabels=['Negative', 'Neutral', 'Positive'],\n", "                ax=axes[0,0])\n", "    axes[0,0].set_title('Probability Correlation by True Class', fontweight='bold')\n", "    \n", "    # 2. Correlation between confidence and accuracy\n", "    confidence_scores = np.max(prediction_probabilities, axis=1)\n", "    correct_predictions = (y_true == y_pred).astype(int)\n", "    \n", "    # Create confidence bins for analysis\n", "    confidence_bins = np.linspace(0.3, 1.0, 8)\n", "    bin_centers = (confidence_bins[:-1] + confidence_bins[1:]) / 2\n", "    bin_accuracies = []\n", "    \n", "    for i in range(len(confidence_bins)-1):\n", "        mask = (confidence_scores >= confidence_bins[i]) & (confidence_scores < confidence_bins[i+1])\n", "        if np.sum(mask) > 0:\n", "            bin_accuracies.append(np.mean(correct_predictions[mask]))\n", "        else:\n", "            bin_accuracies.append(0)\n", "    \n", "    axes[0,1].bar(bin_centers, bin_accuracies, width=0.08, alpha=0.7, color='lightcoral')\n", "    axes[0,1].plot(bin_centers, bin_accuracies, 'ro-', linewidth=2)\n", "    axes[0,1].set_xlabel('Confidence Level')\n", "    axes[0,1].set_ylabel('Accuracy')\n", "    axes[0,1].set_title('Confidence vs Accuracy Correlation', fontweight='bold')\n", "    axes[0,1].grid(True, alpha=0.3)\n", "    \n", "    # 3. Error distribution by confidence level\n", "    error_mask = (y_true != y_pred)\n", "    error_confidences = confidence_scores[error_mask]\n", "    correct_confidences = confidence_scores[~error_mask]\n", "    \n", "    axes[1,0].hist(correct_confidences, bins=20, alpha=0.7, label='Correct Predictions', \n", "                   color='lightgreen', density=True)\n", "    axes[1,0].hist(error_confidences, bins=20, alpha=0.7, label='Errors', \n", "                   color='lightcoral', density=True)\n", "    axes[1,0].set_xlabel('Confidence Level')\n", "    axes[1,0].set_ylabel('Density')\n", "    axes[1,0].set_title('Confidence Distribution: Correct vs Errors', fontweight='bold')\n", "    axes[1,0].legend()\n", "    \n", "    # 4. Probability transition matrix\n", "    prob_transitions = np.zeros((3, 3))\n", "    for true_idx in range(3):\n", "        for pred_idx in range(3):\n", "            mask = (y_true == true_idx) & (y_pred == pred_idx)\n", "            if np.sum(mask) > 0:\n", "                prob_transitions[true_idx, pred_idx] = np.mean(prediction_probabilities[mask, pred_idx])\n", "    \n", "    sns.heatmap(prob_transitions,\n", "                annot=True,\n", "                fmt='.3f',\n", "                cmap='YlOrRd',\n", "                xticklabels=['Pred: Negative', 'Pred: Neutral', 'Pred: Positive'],\n", "                yticklabels=['True: Negative', 'True: Neutral', 'True: Positive'],\n", "                ax=axes[1,1])\n", "    axes[1,1].set_title('Mean Probabilities by Transition', fontweight='bold')\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig(os.path.join(output_dir, 'correlation_analysis.png'), dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "    \n", "    # === FINAL PERFORMANCE SUMMARY ===\n", "    print(f\"\\nFINAL MODEL PERFORMANCE SUMMARY:\")\n", "    print(\"=\"*55)\n", "    \n", "    # Calculate robustness metrics\n", "    confidence_accuracy_corr = np.corrcoef(confidence_scores, correct_predictions)[0,1]\n", "    calibration_error = np.mean(np.abs(confidence_scores - correct_predictions))\n", "    \n", "    print(f\"Global Performance Metrics:\")\n", "    print(f\"  Accuracy: {overall_accuracy:.4f} ({overall_accuracy*100:.2f}%)\")\n", "    print(f\"  Confidence-Accuracy Correlation: {confidence_accuracy_corr:.4f}\")\n", "    print(f\"  Calibration Error: {calibration_error:.4f}\")\n", "    print(f\"  Mean Confidence: {np.mean(confidence_scores):.4f}\")\n", "    \n", "    print(f\"\\nRobustness Metrics:\")\n", "    print(f\"  High Confidence Predictions (>0.9): {np.sum(confidence_scores > 0.9)} ({np.sum(confidence_scores > 0.9)/len(confidence_scores)*100:.1f}%)\")\n", "    print(f\"  Uncertain Predictions (0.4-0.6): {np.sum((confidence_scores > 0.4) & (confidence_scores < 0.6))}\")\n", "    print(f\"  Confident Errors (>0.8): {np.sum((confidence_scores > 0.8) & error_mask)}\")\n", "    \n", "    # Determine overall model quality\n", "    if overall_accuracy > 0.85:\n", "        quality = \"EXCELLENT\"\n", "    elif overall_accuracy > 0.75:\n", "        quality = \"GOOD\" \n", "    elif overall_accuracy > 0.65:\n", "        quality = \"AVERAGE\"\n", "    else:\n", "        quality = \"POOR\"\n", "    \n", "    print(f\"\\nOVERALL MODEL EVALUATION: {quality}\")\n", "    print(f\"   Performance: {overall_accuracy*100:.1f}% accuracy\")\n", "    print(f\"   Calibration: {'Well calibrated' if calibration_error < 0.1 else 'Poorly calibrated'}\")\n", "    print(f\"   Confidence: {'Appropriate' if 0.7 < np.mean(confidence_scores) < 0.9 else 'Needs adjustment'}\")\n", "    \n", "    # Save correlation analysis\n", "    correlation_analysis = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'class_statistics': class_stats,\n", "        'correlation_metrics': {\n", "            'confidence_accuracy_correlation': float(confidence_accuracy_corr),\n", "            'calibration_error': float(calibration_error),\n", "            'mean_confidence': float(np.mean(confidence_scores))\n", "        },\n", "        'robustness_metrics': {\n", "            'high_confidence_predictions': int(np.sum(confidence_scores > 0.9)),\n", "            'uncertain_predictions': int(np.sum((confidence_scores > 0.4) & (confidence_scores < 0.6))),\n", "            'confident_errors': int(np.sum((confidence_scores > 0.8) & error_mask))\n", "        },\n", "        'correlation_matrices': {\n", "            'true_class_correlation': true_class_corr.tolist(),\n", "            'probability_transitions': prob_transitions.tolist()\n", "        },\n", "        'model_quality_assessment': quality\n", "    }\n", "    \n", "    with open(os.path.join(output_dir, 'correlation_analysis.json'), 'w') as f:\n", "        json.dump(correlation_analysis, f, indent=2)\n", "    \n", "    print(f\"\\nCorrelation analysis saved in: {output_dir}/correlation_analysis.json\")\n", "    \n", "except Exception as e:\n", "    print(f\"Error during correlation analysis: {e}\")\n", "    import traceback\n", "    traceback.print_exc()"]}, {"cell_type": "code", "execution_count": 39, "id": "6a581ac2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== STEP 4.10: FINAL MODEL SAVING ===\n", "Saving trained model...\n", "Model saved in: ./finbert_results\\final_model\n", "\n", "Saved files:\n", "  - config.json (0.0 MB)\n", "  - model.safetensors (417.7 MB)\n", "  - model_info.json (0.0 MB)\n", "  - special_tokens_map.json (0.0 MB)\n", "  - tokenizer.json (0.7 MB)\n", "  - tokenizer_config.json (0.0 MB)\n", "  - training_args.bin (0.0 MB)\n", "  - vocab.txt (0.2 MB)\n", "\n", "Total model size: 418.6 MB\n", "\n", "To load this model later:\n", "```python\n", "from transformers import AutoModelForSequenceClassification, AutoTokenizer\n", "\n", "model = AutoModelForSequenceClassification.from_pretrained('./finbert_results\\final_model')\n", "tokenizer = AutoTokenizer.from_pretrained('./finbert_results\\final_model')\n", "```\n", "Model saved in: ./finbert_results\\final_model\n", "\n", "Saved files:\n", "  - config.json (0.0 MB)\n", "  - model.safetensors (417.7 MB)\n", "  - model_info.json (0.0 MB)\n", "  - special_tokens_map.json (0.0 MB)\n", "  - tokenizer.json (0.7 MB)\n", "  - tokenizer_config.json (0.0 MB)\n", "  - training_args.bin (0.0 MB)\n", "  - vocab.txt (0.2 MB)\n", "\n", "Total model size: 418.6 MB\n", "\n", "To load this model later:\n", "```python\n", "from transformers import AutoModelForSequenceClassification, AutoTokenizer\n", "\n", "model = AutoModelForSequenceClassification.from_pretrained('./finbert_results\\final_model')\n", "tokenizer = AutoTokenizer.from_pretrained('./finbert_results\\final_model')\n", "```\n"]}], "source": ["# Step 4.10: Final model saving\n", "print(\"\\n=== STEP 4.10: FINAL MODEL SAVING ===\")\n", "\n", "try:\n", "    # Create folder for final model\n", "    final_model_dir = os.path.join(output_dir, \"final_model\")\n", "    os.makedirs(final_model_dir, exist_ok=True)\n", "    \n", "    print(f\"Saving trained model...\")\n", "    \n", "    # Save model and tokenizer\n", "    trainer.save_model(final_model_dir)\n", "    tokenizer.save_pretrained(final_model_dir)\n", "    \n", "    print(f\"Model saved in: {final_model_dir}\")\n", "    \n", "    # Create model configuration file\n", "    model_config = {\n", "        'model_name': 'FinBERT-Financial-Sentiment',\n", "        'base_model': model_name,\n", "        'num_labels': num_labels,\n", "        'label_mapping': {\n", "            '0': 'Negative',\n", "            '1': 'Neutral', \n", "            '2': 'Positive'\n", "        },\n", "        'training_dataset': metadata['dataset_name'],\n", "        'training_date': datetime.now().isoformat(),\n", "        'final_metrics': final_eval if 'final_eval' in locals() else None,\n", "        'training_args': training_args.to_dict(),\n", "        'model_size_mb': sum(os.path.getsize(os.path.join(final_model_dir, f)) \n", "                            for f in os.listdir(final_model_dir) \n", "                            if os.path.isfile(os.path.join(final_model_dir, f))) / (1024*1024)\n", "    }\n", "    \n", "    with open(os.path.join(final_model_dir, 'model_info.json'), 'w', encoding='utf-8') as f:\n", "        json.dump(model_config, f, indent=2, default=str, ensure_ascii=False)\n", "    \n", "    # List saved files\n", "    saved_files = os.listdir(final_model_dir)\n", "    print(f\"\\nSaved files:\")\n", "    for file in saved_files:\n", "        file_path = os.path.join(final_model_dir, file)\n", "        if os.path.isfile(file_path):\n", "            size_mb = os.path.getsize(file_path) / (1024*1024)\n", "            print(f\"  - {file} ({size_mb:.1f} MB)\")\n", "    \n", "    print(f\"\\nTotal model size: {model_config['model_size_mb']:.1f} MB\")\n", "    \n", "    # Instructions to load the model later\n", "    print(f\"\\nTo load this model later:\")\n", "    print(f\"```python\")\n", "    print(f\"from transformers import AutoModelForSequenceClassification, AutoTokenizer\")\n", "    print(f\"\")\n", "    print(f\"model = AutoModelForSequenceClassification.from_pretrained('{final_model_dir}')\")\n", "    print(f\"tokenizer = AutoTokenizer.from_pretrained('{final_model_dir}')\")\n", "    print(f\"```\")\n", "    \n", "except Exception as e:\n", "    print(f\"Error during saving: {e}\")"]}, {"cell_type": "code", "execution_count": 40, "id": "396e7d60", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== STEP 4.11: INFERENCE TEST ===\n", "Inference test on examples:\n", "----------------------------------------------------------------------\n", "\n", "Example 1:\n", "Text: \"Apple stock reaches new all-time high after strong quarterly earnings\"\n", "Prediction: Positive (0.922)\n", "Probabilities: Negative=0.071, Neutral=0.007, Positive=0.922\n", "\n", "Example 2:\n", "Text: \"Company faces major losses due to market volatility\"\n", "Prediction: Neutral (0.977)\n", "Probabilities: Negative=0.014, Neutral=0.977, Positive=0.009\n", "\n", "Example 3:\n", "Text: \"Quarterly results meet analyst expectations\"\n", "Prediction: Positive (0.954)\n", "Probabilities: Negative=0.041, Neutral=0.005, Positive=0.954\n", "\n", "Example 4:\n", "Text: \"Stock market shows mixed performance today\"\n", "Prediction: Neutral (0.774)\n", "Probabilities: Negative=0.112, Neutral=0.774, Positive=0.114\n", "\n", "Example 5:\n", "Text: \"Strong revenue growth drives investor confidence\"\n", "Prediction: Positive (0.973)\n", "Probabilities: Negative=0.020, Neutral=0.007, Positive=0.973\n", "\n", "Inference test completed\n"]}], "source": ["# Step 4.11: Quick inference test\n", "print(\"\\n=== STEP 4.11: INFERENCE TEST ===\")\n", "\n", "def predict_sentiment(text, model, tokenizer, device):\n", "    \"\"\"Predict sentiment of a text\"\"\"\n", "    # Tokenize text\n", "    inputs = tokenizer(text, return_tensors=\"pt\", truncation=True, padding=True, max_length=512)\n", "    inputs = {k: v.to(device) for k, v in inputs.items()}\n", "    \n", "    # Prediction\n", "    model.eval()\n", "    with torch.no_grad():\n", "        outputs = model(**inputs)\n", "        predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)\n", "    \n", "    # Convert to probabilities\n", "    probs = predictions.cpu().numpy()[0]\n", "    predicted_class = np.argmax(probs)\n", "    \n", "    return predicted_class, probs\n", "\n", "# Test examples\n", "test_texts = [\n", "    \"Apple stock reaches new all-time high after strong quarterly earnings\",\n", "    \"Company faces major losses due to market volatility\", \n", "    \"Quarterly results meet analyst expectations\",\n", "    \"Stock market shows mixed performance today\",\n", "    \"Strong revenue growth drives investor confidence\"\n", "]\n", "\n", "print(\"Inference test on examples:\")\n", "print(\"-\"*70)\n", "\n", "sentiment_names = ['Negative', 'Neutral', 'Positive']\n", "\n", "for i, text in enumerate(test_texts, 1):\n", "    try:\n", "        predicted_class, probs = predict_sentiment(text, model, tokenizer, device)\n", "        \n", "        print(f\"\\nExample {i}:\")\n", "        print(f\"Text: \\\"{text}\\\"\")\n", "        print(f\"Prediction: {sentiment_names[predicted_class]} ({probs[predicted_class]:.3f})\")\n", "        print(f\"Probabilities: Negative={probs[0]:.3f}, Neutral={probs[1]:.3f}, Positive={probs[2]:.3f}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error for example {i}: {e}\")\n", "\n", "print(f\"\\nInference test completed\")"]}, {"cell_type": "code", "execution_count": 41, "id": "e022e3e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== STEP 4.12: EXPORT MODEL AS PICKLE ===\n", "Preparing model for pickle export...\n", "Saving model with pickle...\n", "Saving model with joblib...\n", "Saving model with joblib...\n", "Saving model weights only...\n", "Saving model weights only...\n", "\n", "Model export completed successfully!\n", "Export files created:\n", "  - Complete model (pickle): ./finbert_results\\model_export\\finbert_model.pkl (418.2 MB)\n", "  - Complete model (joblib): ./finbert_results\\model_export\\finbert_model.joblib (388.3 MB)\n", "  - Model weights only: ./finbert_results\\model_export\\finbert_model_only.pkl (418.2 MB)\n", "  - Usage instructions: ./finbert_results\\model_export\\usage_instructions.txt\n", "\n", "Export directory: ./finbert_results\\model_export\n", "Your model is now ready for deployment!\n", "\n", "Model export completed successfully!\n", "Export files created:\n", "  - Complete model (pickle): ./finbert_results\\model_export\\finbert_model.pkl (418.2 MB)\n", "  - Complete model (joblib): ./finbert_results\\model_export\\finbert_model.joblib (388.3 MB)\n", "  - Model weights only: ./finbert_results\\model_export\\finbert_model_only.pkl (418.2 MB)\n", "  - Usage instructions: ./finbert_results\\model_export\\usage_instructions.txt\n", "\n", "Export directory: ./finbert_results\\model_export\n", "Your model is now ready for deployment!\n"]}], "source": ["# Step 4.12: Export model as pickle file\n", "print(\"\\n=== STEP 4.12: EXPORT MODEL AS PICKLE ===\")\n", "\n", "import pickle\n", "import joblib\n", "\n", "try:\n", "    # Create export directory\n", "    export_dir = os.path.join(output_dir, \"model_export\")\n", "    os.makedirs(export_dir, exist_ok=True)\n", "    \n", "    print(\"Preparing model for pickle export...\")\n", "    \n", "    # Move model to CPU for serialization (pickle works better with CPU models)\n", "    model_cpu = model.cpu()\n", "    tokenizer_cpu = tokenizer  # Tokenizer doesn't need device movement\n", "    \n", "    # Create a complete model package\n", "    model_package = {\n", "        'model': model_cpu,\n", "        'tokenizer': tokenizer_cpu,\n", "        'label_mapping': {\n", "            0: 'Negative',\n", "            1: 'Neutral', \n", "            2: 'Positive'\n", "        },\n", "        'model_info': {\n", "            'model_name': model_name,\n", "            'num_labels': num_labels,\n", "            'training_date': datetime.now().isoformat(),\n", "            'final_metrics': final_eval if 'final_eval' in locals() else None\n", "        },\n", "        'predict_function': predict_sentiment\n", "    }\n", "    \n", "    # Export using pickle\n", "    pickle_path = os.path.join(export_dir, 'finbert_model.pkl')\n", "    print(f\"Saving model with pickle...\")\n", "    with open(pickle_path, 'wb') as f:\n", "        pickle.dump(model_package, f, protocol=pickle.HIGHEST_PROTOCOL)\n", "    \n", "    # Also export using joblib (often more efficient for large models)\n", "    joblib_path = os.path.join(export_dir, 'finbert_model.joblib')\n", "    print(f\"Saving model with joblib...\")\n", "    joblib.dump(model_package, joblib_path, compress=3)\n", "    \n", "    # Export just the model weights (smaller file)\n", "    model_only_path = os.path.join(export_dir, 'finbert_model_only.pkl')\n", "    print(f\"Saving model weights only...\")\n", "    with open(model_only_path, 'wb') as f:\n", "        pickle.dump({\n", "            'model_state_dict': model_cpu.state_dict(),\n", "            'model_config': model_cpu.config,\n", "            'tokenizer': tokenizer_cpu,\n", "            'label_mapping': model_package['label_mapping']\n", "        }, f)\n", "    \n", "    # Get file sizes\n", "    pickle_size = os.path.getsize(pickle_path) / (1024*1024)\n", "    joblib_size = os.path.getsize(joblib_path) / (1024*1024)\n", "    model_only_size = os.path.getsize(model_only_path) / (1024*1024)\n", "    \n", "    print(f\"\\nModel export completed successfully!\")\n", "    print(f\"Export files created:\")\n", "    print(f\"  - Complete model (pickle): {pickle_path} ({pickle_size:.1f} MB)\")\n", "    print(f\"  - Complete model (joblib): {joblib_path} ({joblib_size:.1f} MB)\")\n", "    print(f\"  - Model weights only: {model_only_path} ({model_only_size:.1f} MB)\")\n", "    \n", "    # Create usage instructions\n", "    usage_instructions = \"\"\"\n", "# HOW TO LOAD THE EXPORTED MODEL:\n", "\n", "## Method 1: Load complete model with pickle\n", "import pickle\n", "import torch\n", "\n", "# Load the complete model package\n", "with open('finbert_model.pkl', 'rb') as f:\n", "    model_package = pickle.load(f)\n", "\n", "model = model_package['model']\n", "tokenizer = model_package['tokenizer']\n", "label_mapping = model_package['label_mapping']\n", "\n", "# Use the model\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "model = model.to(device)\n", "\n", "def predict_sentiment(text):\n", "    inputs = tokenizer(text, return_tensors=\"pt\", truncation=True, padding=True, max_length=512)\n", "    inputs = {k: v.to(device) for k, v in inputs.items()}\n", "    \n", "    model.eval()\n", "    with torch.no_grad():\n", "        outputs = model(**inputs)\n", "        predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)\n", "    \n", "    probs = predictions.cpu().numpy()[0]\n", "    predicted_class = torch.argmax(predictions, dim=-1).item()\n", "    \n", "    return label_mapping[predicted_class], probs\n", "\n", "## Method 2: Load with joblib (often faster)\n", "import joblib\n", "\n", "model_package = joblib.load('finbert_model.joblib')\n", "# Then use same as Method 1\n", "\n", "## Method 3: Load only model weights (smallest file)\n", "import pickle\n", "from transformers import AutoModelForSequenceClassification, AutoTokenizer\n", "\n", "with open('finbert_model_only.pkl', 'rb') as f:\n", "    model_data = pickle.load(f)\n", "\n", "# Recreate model and load weights\n", "model = AutoModelForSequenceClassification.from_pretrained('ProsusAI/finbert', num_labels=3)\n", "model.load_state_dict(model_data['model_state_dict'])\n", "tokenizer = model_data['tokenizer']\n", "    \"\"\"\n", "    \n", "    # Save usage instructions\n", "    instructions_path = os.path.join(export_dir, 'usage_instructions.txt')\n", "    with open(instructions_path, 'w', encoding='utf-8') as f:\n", "        f.write(usage_instructions)\n", "    \n", "    print(f\"  - Usage instructions: {instructions_path}\")\n", "    \n", "    # Move model back to original device if it was on GPU\n", "    if device.type == 'cuda':\n", "        model = model.to(device)\n", "        print(f\"\\nModel moved back to {device}\")\n", "    \n", "    print(f\"\\nExport directory: {export_dir}\")\n", "    print(f\"Your model is now ready for deployment!\")\n", "    \n", "except Exception as e:\n", "    print(f\"Error during model export: {e}\")\n", "    import traceback\n", "    traceback.print_exc()\n", "    \n", "    # Make sure model is back on original device in case of error\n", "    if device.type == 'cuda':\n", "        model = model.to(device)"]}, {"cell_type": "markdown", "id": "3e2168d9", "metadata": {}, "source": ["## **FinBERT Training Summary**\n", "\n", "### **Completed Steps:**\n", "\n", "1. **Data Loading**: Preprocessed datasets loaded successfully\n", "2. **Model Configuration**: FinBERT configured for 3 sentiment classes\n", "3. **Evaluation Metrics**: F1-score, accuracy, precision, recall (global and per class)\n", "4. **Hyperparameters**: Optimized configuration for fine-tuning\n", "5. **Training**: Fine-tuning the model on financial data\n", "6. **Evaluation**: Complete performance analysis with confusion matrix\n", "7. **Saving**: Final model saved for future use\n", "8. **Inference Test**: Validation on concrete examples\n", "\n", "### **Final Configuration:**\n", "- **Base Model**: FinBERT (ProsusAI/finbert)\n", "- **Classes**: 3 (Negative, Neutral, Positive)\n", "- **Learning Rate**: 2e-5\n", "- **<PERSON><PERSON>**: 16\n", "- **Epochs**: 3\n", "- **Optimization**: AdamW with weight decay\n", "\n", "### **Results:**\n", "- **Trained Model**: Ready for financial sentiment classification\n", "- **Detailed Metrics**: Available in JSON files\n", "- **Error Analysis**: Confusion matrix and confusion analysis\n", "- **Saved Model**: Reusable for new predictions\n", "\n", "**The FinBERT model is now fine-tuned and ready for sentiment analysis on financial texts!**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}