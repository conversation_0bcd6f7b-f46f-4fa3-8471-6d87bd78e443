"""
Project Summary - FinBERT HuggingFace Optimized Structure

This script provides an overview of the complete HF-optimized project structure
and helps verify everything is set up correctly.
"""

import os
from pathlib import Path

def check_file_exists(filepath, description=""):
    """Check if a file exists and print status"""
    if Path(filepath).exists():
        size = Path(filepath).stat().st_size
        print(f"✅ {filepath:<30} ({size:,} bytes) {description}")
        return True
    else:
        print(f"❌ {filepath:<30} (Missing) {description}")
        return False

def analyze_project_structure():
    """Analyze the complete project structure"""
    print("🔍 FinBERT HuggingFace Optimized Project Analysis")
    print("=" * 60)
    
    # Check main directories
    directories = [
        ("training/", "Training scripts using HF Trainer"),
        ("evaluation/", "Model evaluation and metrics"),
        ("deployment/", "Flask API with HF pipeline"),
        ("scripts/", "Utility and helper scripts"),
        ("model_files/", "Trained model artifacts (HF format)"),
        ("data/", "Training and test data"),
        ("results/", "Training logs and metrics")
    ]
    
    print("\n📁 Directory Structure:")
    for dir_path, description in directories:
        if Path(dir_path).exists():
            files_count = len(list(Path(dir_path).rglob("*")))
            print(f"✅ {dir_path:<20} ({files_count} files) - {description}")
        else:
            print(f"❌ {dir_path:<20} (Missing) - {description}")
    
    # Check core files
    print("\n📄 Core Files:")
    core_files = [
        ("training/train_finbert.py", "HF Trainer-based training"),
        ("evaluation/evaluate_finbert.py", "HF pipeline evaluation"),
        ("deployment/app_hf.py", "Flask API with HF pipeline"),
        ("scripts/convert_model.py", "Convert existing models to HF"),
        ("scripts/upload_to_hub.py", "Upload to HuggingFace Hub"),
        ("scripts/quick_start.py", "Interactive demo and testing"),
        ("requirements.txt", "Minimal HF dependencies"),
        ("README.md", "Complete documentation"),
        ("MODEL_CARD.md", "HF Hub model card")
    ]
    
    existing_files = 0
    for filepath, description in core_files:
        if check_file_exists(filepath, description):
            existing_files += 1
    
    print(f"\n📊 Summary: {existing_files}/{len(core_files)} core files present")
    
    # Check for model files
    print("\n🤖 Model Files:")
    model_files = [
        "model_files/config.json",
        "model_files/model.safetensors", 
        "model_files/tokenizer.json",
        "model_files/tokenizer_config.json",
        "model_files/special_tokens_map.json",
        "model_files/vocab.txt"
    ]
    
    model_files_exist = 0
    for filepath in model_files:
        if check_file_exists(filepath):
            model_files_exist += 1
    
    if model_files_exist == 0:
        print("\n💡 No model files found. Run conversion script:")
        print("   python scripts/convert_model.py --auto_find")
    
    return existing_files, model_files_exist

def show_usage_examples():
    """Show key usage examples"""
    print("\n🚀 Quick Start Commands:")
    print("-" * 30)
    
    examples = [
        ("Install dependencies:", "pip install -r requirements.txt"),
        ("Interactive demo:", "python scripts/quick_start.py"),
        ("Convert existing model:", "python scripts/convert_model.py --auto_find"),
        ("Train new model:", "python training/train_finbert.py --train_file data.csv"),
        ("Evaluate model:", "python evaluation/evaluate_finbert.py"),
        ("Start API server:", "python deployment/app_hf.py"),
        ("Upload to HF Hub:", "python scripts/upload_to_hub.py --repo_name user/model")
    ]
    
    for description, command in examples:
        print(f"  {description:<25} {command}")

def show_next_steps():
    """Show recommended next steps"""
    print("\n🎯 Recommended Next Steps:")
    print("-" * 30)
    
    steps = [
        "1. Convert existing model files (if available)",
        "2. Test the model with quick_start.py",
        "3. Evaluate model performance on your data", 
        "4. Deploy API for production use",
        "5. Upload to HuggingFace Hub for sharing"
    ]
    
    for step in steps:
        print(f"  {step}")

def main():
    """Main analysis function"""
    # Change to project directory
    project_dir = Path(__file__).parent
    os.chdir(project_dir)
    
    # Analyze structure
    core_files, model_files = analyze_project_structure()
    
    # Show usage
    show_usage_examples()
    
    # Show next steps
    show_next_steps()
    
    # Final status
    print("\n" + "=" * 60)
    if core_files >= 7:
        print("🎉 Project setup is COMPLETE!")
        print("   All core components are ready for use.")
        
        if model_files > 0:
            print("   ✅ Model files detected - ready for inference!")
        else:
            print("   ⚠️  Run model conversion to enable inference")
    else:
        print("⚠️  Project setup is INCOMPLETE")
        print("   Some core files are missing.")
    
    print(f"\n📈 Status: {core_files}/9 core files, {model_files}/6 model files")
    print("📖 See README.md for complete documentation")

if __name__ == "__main__":
    main()
