"""
Main training script for FinBERT sentiment analysis.
"""

import os
import sys
import argparse
import torch
from transformers import Auto<PERSON>oken<PERSON>, AutoModelForSequenceClassification, Trainer, TrainingArguments
from transformers import EarlyStoppingCallback
import pandas as pd
from datetime import datetime
import logging

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config.training_config import TrainingConfig
from src.data.preprocessing import preprocess_dataframe, split_data, analyze_label_distribution
from src.data.dataloader import create_hf_datasets
from src.model.finbert_classifier import FinBERTClassifier
from src.utils.metrics import compute_metrics_for_trainer, save_metrics
from src.utils.logger import setup_logging, TrainingLogger, log_system_info, log_model_info, log_data_info
from src.utils.helpers import set_random_seed, save_model_package, plot_training_curves

def main():
    """Main training function."""
    
    # Parse arguments
    parser = argparse.ArgumentParser(description='Train FinBERT for sentiment analysis')
    parser.add_argument('--data_file', type=str, required=True, help='Path to training data CSV')
    parser.add_argument('--text_column', type=str, default='summary_detail_with_title', help='Name of text column')
    parser.add_argument('--label_column', type=str, default='labels', help='Name of label column')
    parser.add_argument('--output_dir', type=str, default='models', help='Output directory for models')
    parser.add_argument('--config_file', type=str, help='Path to custom config file (optional)')
    parser.add_argument('--sample_size', type=int, help='Sample size for testing (optional)')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(log_dir="logs", log_level="INFO")
    logger = logging.getLogger(__name__)
    training_logger = TrainingLogger(log_dir="logs")
    
    logger.info("Starting FinBERT sentiment analysis training")
    logger.info(f"Arguments: {args}")
    
    # Log system information
    log_system_info()
    
    # Load configuration
    if args.config_file and os.path.exists(args.config_file):
        # Load custom config if provided
        import json
        with open(args.config_file, 'r') as f:
            config_dict = json.load(f)
        config = TrainingConfig(**config_dict)
    else:
        config = TrainingConfig()
    
    # Set random seed for reproducibility
    set_random_seed(config.random_seed)
    
    # Load and preprocess data
    logger.info(f"Loading data from {args.data_file}")
    df = pd.read_csv(args.data_file)
    
    # Sample data if requested (for testing)
    if args.sample_size and args.sample_size < len(df):
        df = df.sample(n=args.sample_size, random_state=config.random_seed)
        logger.info(f"Using sample of {args.sample_size} rows for testing")
    
    logger.info(f"Loaded dataset with {len(df)} rows")
    
    # Preprocess data
    logger.info("Preprocessing data...")
    df_clean = preprocess_dataframe(df, args.text_column, args.label_column)
    
    # Analyze label distribution
    label_dist = analyze_label_distribution(df_clean, args.label_column)
    logger.info(f"Label distribution: {label_dist}")
    
    # Split data
    logger.info("Splitting data...")
    train_df, val_df, test_df = split_data(
        df_clean, 
        train_ratio=config.train_test_split,
        val_ratio=config.validation_split,
        random_state=config.random_seed
    )
    
    # Log data information
    log_data_info(
        train_size=len(train_df),
        val_size=len(val_df) if val_df is not None else 0,
        test_size=len(test_df),
        label_distribution=label_dist['label_counts']
    )
    
    # Load model and tokenizer
    logger.info(f"Loading FinBERT model: {config.model_name}")
    tokenizer = AutoTokenizer.from_pretrained(config.model_name)
    model = AutoModelForSequenceClassification.from_pretrained(
        config.model_name,
        num_labels=config.num_labels,
        problem_type="single_label_classification"
    )
    
    # Log model information
    log_model_info(model, tokenizer)
    
    # Create datasets
    logger.info("Creating datasets...")
    datasets = create_hf_datasets(
        train_df=train_df,
        val_df=val_df if len(val_df) > 0 else None,
        test_df=test_df,
        tokenizer=tokenizer,
        text_column='cleaned_text',
        label_column=args.label_column,
        max_length=config.max_length
    )
    
    # Setup training arguments
    training_args = TrainingArguments(
        **config.get_training_args(),
        logging_dir=os.path.join("logs", f"tensorboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    )
    
    # Setup trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=datasets['train'],
        eval_dataset=datasets.get('validation', datasets['test']),
        tokenizer=tokenizer,
        compute_metrics=compute_metrics_for_trainer,
        callbacks=[EarlyStoppingCallback(early_stopping_patience=config.early_stopping_patience)]
    )
    
    # Start training
    logger.info("Starting training...")
    start_time = datetime.now()
    
    try:
        # Train the model
        train_result = trainer.train()
        
        # Calculate training time
        end_time = datetime.now()
        training_time = (end_time - start_time).total_seconds()
        
        logger.info(f"Training completed in {training_time:.2f} seconds")
        
        # Evaluate on test set
        logger.info("Evaluating on test set...")
        test_results = trainer.evaluate(eval_dataset=datasets['test'])
        
        # Save final model
        model_save_path = os.path.join(args.output_dir, "final", f"finbert_sentiment_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        os.makedirs(model_save_path, exist_ok=True)
        
        # Save with Hugging Face format
        trainer.save_model(model_save_path)
        tokenizer.save_pretrained(model_save_path)
        
        # Save comprehensive model package
        save_model_package(
            model=model,
            tokenizer=tokenizer,
            config=config.__dict__,
            metrics=test_results,
            save_dir=args.output_dir,
            model_name=f"finbert_sentiment_final_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        
        # Save training metrics
        all_metrics = {
            "training_results": train_result.metrics,
            "test_results": test_results,
            "training_time_seconds": training_time,
            "config": config.__dict__,
            "data_info": {
                "train_size": len(train_df),
                "val_size": len(val_df) if val_df is not None else 0,
                "test_size": len(test_df),
                "label_distribution": label_dist
            }
        }
        
        metrics_file = os.path.join("results", "metrics", f"training_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        os.makedirs(os.path.dirname(metrics_file), exist_ok=True)
        save_metrics(all_metrics, metrics_file)
        
        # Log completion
        training_logger.log_training_complete(training_time, test_results)
        training_logger.log_model_save(model_save_path, test_results)
        
        logger.info("Training pipeline completed successfully!")
        logger.info(f"Final test accuracy: {test_results.get('eval_accuracy', 'N/A'):.4f}")
        logger.info(f"Final test F1 score: {test_results.get('eval_f1', 'N/A'):.4f}")
        logger.info(f"Model saved to: {model_save_path}")
        
    except Exception as e:
        logger.error(f"Training failed with error: {str(e)}")
        raise
    
    return model, tokenizer, test_results

if __name__ == "__main__":
    main()
