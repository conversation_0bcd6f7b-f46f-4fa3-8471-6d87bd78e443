{"export_date": "2025-07-15 15:18:54", "tokenizer_used": "ProsusAI/finbert", "max_length": 512, "files_exported": {"train": "train_processed.csv (1199 lignes)", "validation": "validation_processed.csv (309 lignes)", "test": "test_processed.csv (266 lignes)", "summary": "summary_statistics.csv (9 lignes)"}, "columns_description": {"index": "Index de l'exemple dans le dataset", "text": "Texte décodé (sans tokens spéciaux)", "label": "Label numérique (0=Négatif, 1=Neutre, 2=Positif)", "sentiment": "Nom du sentiment", "token_length": "Longueur réelle en tokens (sans padding)", "input_ids": "IDs des tokens (avec padding)", "attention_mask": "Masque d'attention"}}