{"cells": [{"cell_type": "markdown", "id": "2038521b", "metadata": {}, "source": ["# **Prétraitement des Données pour l'Analyse de Sentiment Financier**\n", "\n", "Ce notebook implémente les étapes détaillées de prétraitement des données pour préparer le dataset à l'entraînement du modèle FinBERT.\n", "\n", "## **Étapes du prétraitement :**\n", "1. **Chargement du Dataset** : Utilisation de la bibliothèque datasets de Hugging Face\n", "2. **Vérification des Caractéristiques** : Examen des colonnes et analyse des données\n", "3. **Division du Dataset** : Création des ensembles train/validation/test\n", "4. **Tokenisation** : Utilisation du tokenizer FinBERT avec longueur maximale de 512 tokens\n", "5. **Formatage pour PyTorch** : Configuration des datasets pour l'entraînement\n", "6. **Nettoyage des Données** : Suppression des caractères spéciaux et gestion des valeurs nulles"]}, {"cell_type": "code", "execution_count": 16, "id": "bc0a4dff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Toutes les bibliothèques ont été importées avec succès\n"]}], "source": ["# Installation des bibliothèques nécessaires\n", "# Décommentez la ligne suivante si les packages ne sont pas installés\n", "# !pip install datasets transformers pandas numpy scikit-learn torch\n", "\n", "# Imports des bibliothèques\n", "from datasets import load_dataset, Dataset\n", "from transformers import AutoTokenizer\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "import re\n", "from html import unescape\n", "import os\n", "import json\n", "\n", "print(\"✅ Toutes les bibliothèques ont été importées avec succès\")"]}, {"cell_type": "code", "execution_count": 17, "id": "346df899", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ÉTAPE 3.1: CHARGEMENT DU DATASET ===\n", "✅ Dataset '<PERSON><PERSON><PERSON>/financial_news_sentiment_mixte_with_phrasebank_75' chargé avec succès\n", "Structure du dataset: DatasetDict({\n", "    train: Dataset({\n", "        features: ['summary_detail', 'title', 'summary_detail_with_title', 'topic', 'labels', '__index_level_0__', 'score_topic'],\n", "        num_rows: 4446\n", "    })\n", "    test: Dataset({\n", "        features: ['summary_detail', 'title', 'summary_detail_with_title', 'topic', 'labels', '__index_level_0__', 'score_topic'],\n", "        num_rows: 785\n", "    })\n", "})\n", "\n", "📊 Split 'train': 4446 exemples\n", "\n", "📊 Split 'test': 785 exemples\n", "✅ Dataset '<PERSON><PERSON><PERSON>/financial_news_sentiment_mixte_with_phrasebank_75' chargé avec succès\n", "Structure du dataset: DatasetDict({\n", "    train: Dataset({\n", "        features: ['summary_detail', 'title', 'summary_detail_with_title', 'topic', 'labels', '__index_level_0__', 'score_topic'],\n", "        num_rows: 4446\n", "    })\n", "    test: Dataset({\n", "        features: ['summary_detail', 'title', 'summary_detail_with_title', 'topic', 'labels', '__index_level_0__', 'score_topic'],\n", "        num_rows: 785\n", "    })\n", "})\n", "\n", "📊 Split 'train': 4446 exemples\n", "\n", "📊 Split 'test': 785 exemples\n"]}], "source": ["# Étape 3.1: Chargement du Dataset avec vérification complète\n", "print(\"=== ÉTAPE 3.1: CHARGEMENT DU DATASET ===\")\n", "dataset_name = \"<PERSON><PERSON><PERSON>/financial_news_sentiment_mixte_with_phrasebank_75\"\n", "\n", "try:\n", "    dataset = load_dataset(dataset_name)\n", "    print(f\"✅ Dataset '{dataset_name}' chargé avec succès\")\n", "    print(f\"Structure du dataset: {dataset}\")\n", "    \n", "    # Afficher les statistiques de base\n", "    for split_name, split_data in dataset.items():\n", "        print(f\"\\n📊 Split '{split_name}': {len(split_data)} exemples\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Erreur lors du chargement: {e}\")"]}, {"cell_type": "code", "execution_count": 18, "id": "932aa638", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== ÉTAPE 3.2: VÉRIFICATION DES CARACTÉRISTIQUES ===\n", "🔍 Caractéristiques du dataset 'train':\n", "{'summary_detail': Value('string'), 'title': Value('string'), 'summary_detail_with_title': Value('string'), 'topic': Value('int64'), 'labels': Value('int64'), '__index_level_0__': Value('int64'), 'score_topic': Value('float64')}\n", "\n", "📝 Exemple d'entrées (3 premiers exemples):\n", "\n", "--- Exemple 1 ---\n", "Title: N/A...\n", "Summary_detail: N/A...\n", "Summary_detail_with_title: Operating profit improved by 27 % to EUR 579.8 mn from EUR 457.2 mn in 2006 ....\n", "Labels: 2\n", "Topic: 2\n", "\n", "--- Exemple 2 ---\n", "Title: N/A...\n", "Summary_detail: N/A...\n", "Summary_detail_with_title: Finnish construction group Lemminkainen Oyj HEL : LEM1S said today it has won a contract to provide ...\n", "Labels: 2\n", "Topic: 5\n", "\n", "--- Exemple 3 ---\n", "Title: N/A...\n", "Summary_detail: N/A...\n", "Summary_detail_with_title: HELSINKI ( AFX ) - Nokian Tyres reported a fourth quarter pretax profit of 61.5 mln eur , up from 48...\n", "Labels: 2\n", "Topic: 2\n", "\n", "📏 Statistiques des longueurs de texte:\n", "summary_detail - Min: 1.0, Max: 2531.0, <PERSON><PERSON><PERSON>: 384.4\n", "title - Min: 23.0, <PERSON>: 327.0, <PERSON><PERSON><PERSON>: 86.6\n", "summary_detail_with_title - Min: 9, <PERSON>: 2530, <PERSON><PERSON><PERSON>: 230.6\n"]}], "source": ["print(\"\\n=== ÉTAPE 3.2: VÉRIFICATION DES CARACTÉRISTIQUES ===\")\n", "\n", "# Examiner les caractéristiques du dataset\n", "print(\"🔍 Caractéristiques du dataset 'train':\")\n", "print(dataset['train'].features)\n", "\n", "# Examiner quelques exemples pour comprendre la structure\n", "print(\"\\n📝 Exemple d'entrées (3 premiers exemples):\")\n", "for i in range(min(3, len(dataset['train']))):\n", "    example = dataset['train'][i]\n", "    print(f\"\\n--- Exemple {i+1} ---\")\n", "    title = example.get('title', 'N/A')\n", "    summary_detail = example.get('summary_detail', 'N/A')\n", "    summary_with_title = example.get('summary_detail_with_title', 'N/A')\n", "    print(f\"Title: {title[:100] if title else 'N/A'}...\")\n", "    print(f\"Summary_detail: {summary_detail[:100] if summary_detail else 'N/A'}...\")\n", "    print(f\"Summary_detail_with_title: {summary_with_title[:100] if summary_with_title else 'N/A'}...\")\n", "    print(f\"Labels: {example.get('labels', 'N/A')}\")\n", "    print(f\"Topic: {example.get('topic', 'N/A')}\")\n", "\n", "# Analyser les longueurs des textes\n", "df_train = dataset['train'].to_pandas()\n", "print(f\"\\n📏 Statistiques des longueurs de texte:\")\n", "print(f\"summary_detail - Min: {df_train['summary_detail'].str.len().min()}, Max: {df_train['summary_detail'].str.len().max()}, Moyenne: {df_train['summary_detail'].str.len().mean():.1f}\")\n", "print(f\"title - Min: {df_train['title'].str.len().min()}, Max: {df_train['title'].str.len().max()}, Moyenne: {df_train['title'].str.len().mean():.1f}\")\n", "print(f\"summary_detail_with_title - Min: {df_train['summary_detail_with_title'].str.len().min()}, Max: {df_train['summary_detail_with_title'].str.len().max()}, Moyenne: {df_train['summary_detail_with_title'].str.len().mean():.1f}\")"]}, {"cell_type": "code", "execution_count": 19, "id": "c0631b0c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== ÉTAPE 3.3: DIVISION DU DATASET ===\n", "✅ Splits prédéfinis détectés\n", "✅ Utilisation des splits existants avec création de validation:\n", "  - Train: 3556 exemples\n", "  - Validation: 890 exemples\n", "  - Test: 785 exemples\n", "\n", "📊 Distribution des labels dans Train:\n", "  Négatif (label 0): 319 (9.0%)\n", "  Neutre (label 1): 2160 (60.7%)\n", "  Positif (label 2): 1077 (30.3%)\n", "\n", "📊 Distribution des labels dans Validation:\n", "  Négatif (label 0): 83 (9.3%)\n", "  Neutre (label 1): 561 (63.0%)\n", "  Positif (label 2): 246 (27.6%)\n", "\n", "📊 Distribution des labels dans Test:\n", "  Négatif (label 0): 79 (10.1%)\n", "  Neutre (label 1): 481 (61.3%)\n", "  Positif (label 2): 225 (28.7%)\n"]}], "source": ["print(\"\\n=== ÉTAPE 3.3: DIVISION DU DATASET ===\")\n", "\n", "# Vérifier si le dataset a déjà des divisions prédéfinies\n", "if 'test' not in dataset:\n", "    print(\"⚠️ Pas de split 'test' prédéfini. Création de la division...\")\n", "    # Diviser le dataset train en train/validation/test\n", "    dataset_split = dataset['train'].train_test_split(test_size=0.3, seed=42)  # 70% train, 30% temp\n", "    temp_dataset = dataset_split['test'].train_test_split(test_size=0.5, seed=42)  # 15% validation, 15% test\n", "    \n", "    train_dataset = dataset_split['train']  # 70%\n", "    validation_dataset = temp_dataset['train']  # 15%\n", "    test_dataset = temp_dataset['test']  # 15%\n", "    \n", "    print(f\"✅ Division créée:\")\n", "    print(f\"  - Train: {len(train_dataset)} exemples (70%)\")\n", "    print(f\"  - Validation: {len(validation_dataset)} exemples (15%)\")\n", "    print(f\"  - Test: {len(test_dataset)} exemples (15%)\")\n", "    \n", "else:\n", "    print(\"✅ Splits prédéfinis détectés\")\n", "    train_dataset = dataset['train']\n", "    test_dataset = dataset['test']\n", "    \n", "    # Créer un split de validation à partir du train (80% train, 20% validation)\n", "    train_val_split = train_dataset.train_test_split(test_size=0.2, seed=42)\n", "    train_dataset = train_val_split['train']\n", "    validation_dataset = train_val_split['test']\n", "    \n", "    print(f\"✅ Utilisation des splits existants avec création de validation:\")\n", "    print(f\"  - Train: {len(train_dataset)} exemples\")\n", "    print(f\"  - Validation: {len(validation_dataset)} exemples\")\n", "    print(f\"  - Test: {len(test_dataset)} exemples\")\n", "\n", "# Vérifier la distribution des labels dans chaque split\n", "def check_label_distribution(dataset_split, split_name):\n", "    df = dataset_split.to_pandas()\n", "    label_dist = df['labels'].value_counts().sort_index()\n", "    print(f\"\\n📊 Distribution des labels dans {split_name}:\")\n", "    for label, count in label_dist.items():\n", "        percentage = (count / len(df)) * 100\n", "        sentiment_name = ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>ti<PERSON>'][label]\n", "        print(f\"  {sentiment_name} (label {label}): {count} ({percentage:.1f}%)\")\n", "\n", "check_label_distribution(train_dataset, \"Train\")\n", "check_label_distribution(validation_dataset, \"Validation\")\n", "check_label_distribution(test_dataset, \"Test\")"]}, {"cell_type": "code", "execution_count": 20, "id": "3b7d8075", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== ÉTAPE 3.6: NETTOYAGE DES DONNÉES ===\n", "\n", "🧹 Nettoyage du split Train...\n", "Valeurs nulles avant nettoyage:\n", "  summary_detail: 2353 valeurs nulles\n", "  title: 2353 valeurs nulles\n", "  summary_detail_with_title: 0 valeurs nulles\n", "⚠️ 2357 lignes supprimées (texte vide après nettoyage)\n", "Valeurs nulles après nettoyage:\n", "  summary_detail: 0 valeurs nulles\n", "  title: 0 valeurs nulles\n", "  summary_detail_with_title: 0 valeurs nulles\n", "✅ Train nettoyé: 1199 exemples restants\n", "\n", "🧹 Nettoyage du split Validation...\n", "Valeurs nulles avant nettoyage:\n", "  summary_detail: 581 valeurs nulles\n", "  title: 581 valeurs nulles\n", "  summary_detail_with_title: 0 valeurs nulles\n", "⚠️ 581 lignes supprimées (texte vide après nettoyage)\n", "Valeurs nulles après nettoyage:\n", "  summary_detail: 0 valeurs nulles\n", "  title: 0 valeurs nulles\n", "  summary_detail_with_title: 0 valeurs nulles\n", "⚠️ 581 lignes supprimées (texte vide après nettoyage)\n", "Valeurs nulles après nettoyage:\n", "  summary_detail: 0 valeurs nulles\n", "  title: 0 valeurs nulles\n", "  summary_detail_with_title: 0 valeurs nulles\n", "✅ Validation nettoyé: 309 exemples restants\n", "\n", "🧹 Nettoyage du split Test...\n", "Valeurs nulles avant nettoyage:\n", "  summary_detail: 518 valeurs nulles\n", "  title: 518 valeurs nulles\n", "  summary_detail_with_title: 0 valeurs nulles\n", "⚠️ 519 lignes supprimées (texte vide après nettoyage)\n", "Valeurs nulles après nettoyage:\n", "  summary_detail: 0 valeurs nulles\n", "  title: 0 valeurs nulles\n", "  summary_detail_with_title: 0 valeurs nulles\n", "✅ Test nettoyé: 266 exemples restants\n", "\n", "📊 Résumé après nettoyage:\n", "  - Train: 1199 exemples\n", "  - Validation: 309 exemples\n", "  - Test: 266 exemples\n", "✅ Validation nettoyé: 309 exemples restants\n", "\n", "🧹 Nettoyage du split Test...\n", "Valeurs nulles avant nettoyage:\n", "  summary_detail: 518 valeurs nulles\n", "  title: 518 valeurs nulles\n", "  summary_detail_with_title: 0 valeurs nulles\n", "⚠️ 519 lignes supprimées (texte vide après nettoyage)\n", "Valeurs nulles après nettoyage:\n", "  summary_detail: 0 valeurs nulles\n", "  title: 0 valeurs nulles\n", "  summary_detail_with_title: 0 valeurs nulles\n", "✅ Test nettoyé: 266 exemples restants\n", "\n", "📊 Résumé après nettoyage:\n", "  - Train: 1199 exemples\n", "  - Validation: 309 exemples\n", "  - Test: 266 exemples\n"]}], "source": ["print(\"\\n=== ÉTAPE 3.6: NETTOYAGE DES DONNÉES ===\")\n", "\n", "def clean_text(text):\n", "    \"\"\"Fonction de nettoyage du texte\"\"\"\n", "    if pd.isna(text) or text is None:\n", "        return \"\"\n", "    \n", "    # Convertir en string si ce n'est pas déjà le cas\n", "    text = str(text)\n", "    \n", "    # Décoder les entités HTML\n", "    text = unescape(text)\n", "    \n", "    # Supprimer les balises HTML\n", "    text = re.sub(r'<[^>]+>', '', text)\n", "    \n", "    # Supprimer les caractères de contrôle et les espaces multiples\n", "    text = re.sub(r'[\\x00-\\x1f\\x7f-\\x9f]', ' ', text)\n", "    text = re.sub(r'\\s+', ' ', text)\n", "    \n", "    # Supprimer les espaces en début et fin\n", "    text = text.strip()\n", "    \n", "    return text\n", "\n", "def clean_dataset(dataset_split, split_name):\n", "    \"\"\"Nettoyer un split du dataset\"\"\"\n", "    print(f\"\\n🧹 Nettoyage du split {split_name}...\")\n", "    \n", "    # Convertir en pandas pour faciliter le nettoyage\n", "    df = dataset_split.to_pandas()\n", "    \n", "    # Vérifier les valeurs nulles avant nettoyage\n", "    print(f\"Valeurs nulles avant nettoyage:\")\n", "    for col in ['summary_detail', 'title', 'summary_detail_with_title']:\n", "        null_count = df[col].isna().sum()\n", "        print(f\"  {col}: {null_count} valeurs nulles\")\n", "    \n", "    # Net<PERSON>yer les colonnes textuelles\n", "    df['summary_detail'] = df['summary_detail'].apply(clean_text)\n", "    df['title'] = df['title'].apply(clean_text)\n", "    df['summary_detail_with_title'] = df['summary_detail_with_title'].apply(clean_text)\n", "    \n", "    # Supprimer les lignes avec des textes vides\n", "    initial_length = len(df)\n", "    df = df[(df['summary_detail'].str.len() > 0) & \n", "            (df['title'].str.len() > 0) & \n", "            (df['summary_detail_with_title'].str.len() > 0)]\n", "    \n", "    removed_count = initial_length - len(df)\n", "    if removed_count > 0:\n", "        print(f\"⚠️ {removed_count} lignes supprimées (texte vide après nettoyage)\")\n", "    \n", "    # Vérifier les valeurs nulles après nettoyage\n", "    print(f\"Valeurs nulles après nettoyage:\")\n", "    for col in ['summary_detail', 'title', 'summary_detail_with_title']:\n", "        null_count = df[col].isna().sum()\n", "        print(f\"  {col}: {null_count} valeurs nulles\")\n", "    \n", "    # Reconvertir en dataset\n", "    # Supprimer les colonnes d'index en double avant la conversion\n", "    if '__index_level_0__' in df.columns:\n", "        df = df.drop(columns=['__index_level_0__'])\n", "    cleaned_dataset = Dataset.from_pandas(df)\n", "    \n", "    print(f\"✅ {split_name} nettoyé: {len(cleaned_dataset)} exemples restants\")\n", "    return cleaned_dataset\n", "\n", "# Nettoyer tous les splits\n", "train_dataset_clean = clean_dataset(train_dataset, \"Train\")\n", "validation_dataset_clean = clean_dataset(validation_dataset, \"Validation\")\n", "test_dataset_clean = clean_dataset(test_dataset, \"Test\")\n", "\n", "print(f\"\\n📊 Résumé après nettoyage:\")\n", "print(f\"  - Train: {len(train_dataset_clean)} exemples\")\n", "print(f\"  - Validation: {len(validation_dataset_clean)} exemples\")\n", "print(f\"  - Test: {len(test_dataset_clean)} exemples\")"]}, {"cell_type": "code", "execution_count": null, "id": "4a5a898a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== ÉTAPE 3.4: TOKENISATION ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Token indices sequence length is longer than the specified maximum sequence length for this model (597 > 512). Running this sequence through the model will result in indexing errors\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Tokenizer FinBERT chargé: BertTokenizerFast\n", "BertTokenizerFast(name_or_path='ProsusAI/finbert', vocab_size=30522, model_max_length=512, is_fast=True, padding_side='right', truncation_side='right', special_tokens={'unk_token': '[UNK]', 'sep_token': '[SEP]', 'pad_token': '[PAD]', 'cls_token': '[CLS]', 'mask_token': '[MASK]'}, clean_up_tokenization_spaces=True, added_tokens_decoder={\n", "\t0: AddedToken(\"[PAD]\", rstrip=False, lstrip=False, single_word=False, normalized=False, special=True),\n", "\t100: AddedToken(\"[UNK]\", rstrip=False, lstrip=False, single_word=False, normalized=False, special=True),\n", "\t101: AddedToken(\"[CLS]\", rstrip=False, lstrip=False, single_word=False, normalized=False, special=True),\n", "\t102: AddedToken(\"[SEP]\", rstrip=False, lstrip=False, single_word=False, normalized=False, special=True),\n", "\t103: AddedToken(\"[MASK]\", rstrip=False, lstrip=False, single_word=False, normalized=False, special=True),\n", "}\n", ")\n", "\n", "📏 Analyse des longueurs en tokens pour Train:\n", "  Longueur min: 15 tokens\n", "  Longueur max: 597 tokens\n", "  <PERSON><PERSON>ur moyenne: 103.9 tokens\n", "  Longueur médiane: 95.0 tokens\n", "  95e percentile: 207.1 tokens\n", "  99e percentile: 283.0 tokens\n", "  Textes tronqués avec max_length=512: 1 (0.1%)\n", "  Textes tronqués avec max_length=256: 17 (1.7%)\n", "\n", "📏 Analyse des longueurs en tokens pour Validation:\n", "  Longueur min: 17 tokens\n", "  Longueur max: 337 tokens\n", "  <PERSON><PERSON><PERSON> moyenne: 105.0 tokens\n", "  Longueur médiane: 98.0 tokens\n", "  95e percentile: 211.2 tokens\n", "  99e percentile: 282.2 tokens\n", "  Textes tronqués avec max_length=512: 0 (0.0%)\n", "  Textes tronqués avec max_length=256: 5 (1.6%)\n", "\n", "📏 Analyse des longueurs en tokens pour Test:\n", "  Longueur min: 19 tokens\n", "  Longueur max: 313 tokens\n", "  <PERSON><PERSON>ur moyenne: 106.3 tokens\n", "  Longueur médiane: 102.0 tokens\n", "  95e percentile: 203.8 tokens\n", "  99e percentile: 264.1 tokens\n", "  Textes tronqués avec max_length=512: 0 (0.0%)\n", "  Textes tronqués avec max_length=256: 4 (1.5%)\n"]}], "source": ["print(\"\\n=== ÉTAPE 3.4: TOKENISATION ===\")\n", "\n", "# Charger le tokenizer FinBERT\n", "tokenizer = AutoTokenizer.from_pretrained(\"ProsusAI/finbert\")\n", "print(f\"✅ Tokenizer FinBERT chargé: {tokenizer.__class__.__name__}\")\n", "print(tokenizer)\n", "# Analyser les longueurs en tokens avant tokenisation\n", "def analyze_token_lengths(dataset_split, split_name, sample_size=1000):\n", "    \"\"\"Analyser les longueurs en tokens pour déterminer max_length optimal\"\"\"\n", "    print(f\"\\n📏 Analyse des longueurs en tokens pour {split_name}:\")\n", "    \n", "    # Prendre un échantillon pour l'analyse\n", "    sample_indices = np.random.choice(len(dataset_split), min(sample_size, len(dataset_split)), replace=False)\n", "    sample_texts = [dataset_split[int(i)]['summary_detail_with_title'] for i in sample_indices]\n", "    \n", "    # Token<PERSON> sans padding pour mesurer les longueurs réelles\n", "    token_lengths = []\n", "    for text in sample_texts:\n", "        tokens = tokenizer(text, truncation=False, return_attention_mask=False)\n", "        token_lengths.append(len(tokens['input_ids']))\n", "    \n", "    token_lengths = np.array(token_lengths)\n", "    \n", "    print(f\"  Longueur min: {token_lengths.min()} tokens\")\n", "    print(f\"  Longueur max: {token_lengths.max()} tokens\")\n", "    print(f\"  <PERSON><PERSON>ur moyenne: {token_lengths.mean():.1f} tokens\")\n", "    print(f\"  Longueur médiane: {np.median(token_lengths):.1f} tokens\")\n", "    print(f\"  95e percentile: {np.percentile(token_lengths, 95):.1f} tokens\")\n", "    print(f\"  99e percentile: {np.percentile(token_lengths, 99):.1f} tokens\")\n", "    \n", "    # Calculer le pourcentage de textes qui seront tronqués avec max_length=512\n", "    truncated_512 = (token_lengths > 512).sum()\n", "    truncated_256 = (token_lengths > 256).sum()\n", "    print(f\"  Textes tronqués avec max_length=512: {truncated_512} ({truncated_512/len(token_lengths)*100:.1f}%)\")\n", "    print(f\"  Textes tronqués avec max_length=256: {truncated_256} ({truncated_256/len(token_lengths)*100:.1f}%)\")\n", "\n", "# Analyser les longueurs pour chaque split\n", "analyze_token_lengths(train_dataset_clean, \"Train\")\n", "analyze_token_lengths(validation_dataset_clean, \"Validation\", sample_size=500)\n", "analyze_token_lengths(test_dataset_clean, \"Test\", sample_size=500)"]}, {"cell_type": "code", "execution_count": 22, "id": "7693052f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔄 Tokenisation en cours...\n", "Tokenisation du dataset d'entraînement...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Tokenizing train dataset: 100%|██████████| 1199/1199 [00:00<00:00, 4906.89 examples/s]\n", "Tokenizing train dataset: 100%|██████████| 1199/1199 [00:00<00:00, 4906.89 examples/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Tokenisation du dataset de validation...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Tokenizing validation dataset: 100%|██████████| 309/309 [00:00<00:00, 4667.99 examples/s]\n", "Tokenizing validation dataset: 100%|██████████| 309/309 [00:00<00:00, 4667.99 examples/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Tokenisation du dataset de test...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Tokenizing test dataset: 100%|██████████| 266/266 [00:00<00:00, 4641.72 examples/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Tokenisation terminée pour tous les splits\n", "\n", "📋 Structure des données tokenisées:\n", "Colonnes disponibles: ['summary_detail', 'title', 'summary_detail_with_title', 'topic', 'labels', 'score_topic', '__index_level_0__', 'input_ids', 'token_type_ids', 'attention_mask']\n", "Exemple de token_ids (5 premiers): [101, 6846, 21270, 4311, 2353]\n", "Longueur des input_ids: 512\n", "Longueur des attention_mask: 512\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# Fonction de tokenisation\n", "def tokenize_function(examples):\n", "    \"\"\"Fonction de tokenisation avec padding et truncation\"\"\"\n", "    return tokenizer(\n", "        examples['summary_detail_with_title'], \n", "        padding=\"max_length\", \n", "        truncation=True, \n", "        max_length=512,\n", "        return_tensors=None  # Sera converti en torch plus tard\n", "    )\n", "\n", "print(f\"\\n🔄 Tokenisation en cours...\")\n", "\n", "# Tokeniser tous les splits\n", "print(\"Tokenisation du dataset d'entraînement...\")\n", "tokenized_train = train_dataset_clean.map(tokenize_function, batched=True, \n", "                                        desc=\"Tokenizing train dataset\")\n", "\n", "print(\"Tokenisation du dataset de validation...\")\n", "tokenized_validation = validation_dataset_clean.map(tokenize_function, batched=True,\n", "                                                   desc=\"Tokenizing validation dataset\")\n", "\n", "print(\"Tokenisation du dataset de test...\")\n", "tokenized_test = test_dataset_clean.map(tokenize_function, batched=True,\n", "                                      desc=\"Tokenizing test dataset\")\n", "\n", "print(\"✅ Tokenisation terminée pour tous les splits\")\n", "\n", "# Vérifier la structure des données tokenisées\n", "print(f\"\\n📋 Structure des données tokenisées:\")\n", "print(f\"Colonnes disponibles: {tokenized_train.column_names}\")\n", "print(f\"Exemple de token_ids (5 premiers): {tokenized_train[0]['input_ids'][:5]}\")\n", "print(f\"Longueur des input_ids: {len(tokenized_train[0]['input_ids'])}\")\n", "print(f\"Longueur des attention_mask: {len(tokenized_train[0]['attention_mask'])}\")"]}, {"cell_type": "code", "execution_count": 23, "id": "367b98b8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== ÉTAPE 3.5: FORMATAGE POUR PYTORCH ===\n", "🔧 Configuration du format PyTorch...\n", "✅ Toutes les colonnes nécessaires présentes dans train\n", "✅ Toutes les colonnes nécessaires présentes dans validation\n", "✅ Toutes les colonnes nécessaires présentes dans test\n", "Préparation de Train pour PyTorch...\n", "  ✅ Train formaté: 1199 exemples\n", "  Colonnes: ['labels', 'input_ids', 'attention_mask']\n", "  Format: {'type': 'torch', 'format_kwargs': {}, 'columns': ['input_ids', 'attention_mask', 'labels'], 'output_all_columns': False}\n", "Préparation de Validation pour PyTorch...\n", "  ✅ Validation formaté: 309 exemples\n", "  Colonnes: ['labels', 'input_ids', 'attention_mask']\n", "  Format: {'type': 'torch', 'format_kwargs': {}, 'columns': ['input_ids', 'attention_mask', 'labels'], 'output_all_columns': False}\n", "Préparation de Test pour PyTorch...\n", "  ✅ Test formaté: 266 exemples\n", "  Colonnes: ['labels', 'input_ids', 'attention_mask']\n", "  Format: {'type': 'torch', 'format_kwargs': {}, 'columns': ['input_ids', 'attention_mask', 'labels'], 'output_all_columns': False}\n", "\n", "🔍 Vérification d'un exemple du dataset d'entraînement:\n", "Type de input_ids: <class 'torch.Tensor'>\n", "Shape de input_ids: torch.Size([512])\n", "Type de attention_mask: <class 'torch.Tensor'>\n", "Shape de attention_mask: torch.Size([512])\n", "Type de labels: <class 'torch.Tensor'>\n", "Valeur de labels: 2\n", "\n", "🎉 PREPROCESSING TERMINÉ AVEC SUCCÈS!\n", "📊 Datasets finaux prêts pour l'entraînement:\n", "  - Train: 1199 exemples\n", "  - Validation: 309 exemples\n", "  - Test: 266 exemples\n", "  - Tokenizer: FinBERT (ProsusAI/finbert)\n", "  - Max length: 512 tokens\n", "  - Format: PyTorch tensors\n"]}], "source": ["print(\"\\n=== ÉTAPE 3.5: FORMATAGE POUR PYTORCH ===\")\n", "\n", "# Configurer le format PyTorch pour tous les datasets\n", "columns_to_keep = [\"input_ids\", \"attention_mask\", \"labels\"]\n", "\n", "print(\"🔧 Configuration du format PyTorch...\")\n", "\n", "# Vérifier que toutes les colonnes nécessaires sont présentes\n", "for dataset_name, dataset_split in [(\"train\", tokenized_train), \n", "                                   (\"validation\", tokenized_validation), \n", "                                   (\"test\", tokenized_test)]:\n", "    missing_cols = [col for col in columns_to_keep if col not in dataset_split.column_names]\n", "    if missing_cols:\n", "        print(f\"⚠️ Colonnes manquantes dans {dataset_name}: {missing_cols}\")\n", "    else:\n", "        print(f\"✅ Toutes les colonnes nécessaires présentes dans {dataset_name}\")\n", "\n", "# Supprimer les colonnes non nécessaires et définir le format\n", "def prepare_for_pytorch(dataset_split, split_name):\n", "    \"\"\"Préparer un dataset pour PyTorch\"\"\"\n", "    print(f\"Préparation de {split_name} pour PyTorch...\")\n", "    \n", "    # Garder seulement les colonnes nécessaires\n", "    dataset_split = dataset_split.remove_columns([\n", "        col for col in dataset_split.column_names \n", "        if col not in columns_to_keep\n", "    ])\n", "    \n", "    # Définir le format PyTorch\n", "    dataset_split.set_format(\"torch\", columns=columns_to_keep)\n", "    \n", "    print(f\"  ✅ {split_name} formaté: {len(dataset_split)} exemples\")\n", "    print(f\"  Colonnes: {dataset_split.column_names}\")\n", "    print(f\"  Format: {dataset_split.format}\")\n", "    \n", "    return dataset_split\n", "\n", "# Préparer tous les datasets\n", "final_train = prepare_for_pytorch(tokenized_train, \"Train\")\n", "final_validation = prepare_for_pytorch(tokenized_validation, \"Validation\")  \n", "final_test = prepare_for_pytorch(tokenized_test, \"Test\")\n", "\n", "# Vérifier un exemple pour s'assurer que tout fonctionne\n", "print(f\"\\n🔍 Vérification d'un exemple du dataset d'entraînement:\")\n", "example = final_train[0]\n", "print(f\"Type de input_ids: {type(example['input_ids'])}\")\n", "print(f\"Shape de input_ids: {example['input_ids'].shape}\")\n", "print(f\"Type de attention_mask: {type(example['attention_mask'])}\")\n", "print(f\"Shape de attention_mask: {example['attention_mask'].shape}\")\n", "print(f\"Type de labels: {type(example['labels'])}\")\n", "print(f\"Valeur de labels: {example['labels']}\")\n", "\n", "print(f\"\\n🎉 PREPROCESSING TERMINÉ AVEC SUCCÈS!\")\n", "print(f\"📊 Datasets finaux prêts pour l'entraînement:\")\n", "print(f\"  - Train: {len(final_train)} exemples\")\n", "print(f\"  - Validation: {len(final_validation)} exemples\")\n", "print(f\"  - Test: {len(final_test)} exemples\")\n", "print(f\"  - Tokenizer: FinBERT (ProsusAI/finbert)\")\n", "print(f\"  - Max length: 512 tokens\")\n", "print(f\"  - Format: PyTorch tensors\")"]}, {"cell_type": "code", "execution_count": 24, "id": "5f47a00a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SAUVEGARDE DES DATASETS PRÉPROCESSÉS ===\n", "💾 Sauvegarde des datasets dans le dossier 'preprocessed_datasets'...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Saving the dataset (1/1 shards): 100%|██████████| 1199/1199 [00:00<00:00, 144922.93 examples/s]\n", "Saving the dataset (1/1 shards): 100%|██████████| 309/309 [00:00<00:00, 61116.66 examples/s]\n", "Saving the dataset (1/1 shards): 100%|██████████| 309/309 [00:00<00:00, 49204.25 examples/s]\n", "Saving the dataset (1/1 shards): 100%|██████████| 266/266 [00:00<00:00, 54795.19 examples/s]\n", "Saving the dataset (1/1 shards): 100%|██████████| 266/266 [00:00<00:00, 45021.79 examples/s]\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Sauvegarde terminée avec succès!\n", "📁 Fichiers sauvegardés:\n", "  - preprocessed_datasets/train/\n", "  - preprocessed_datasets/validation/\n", "  - preprocessed_datasets/test/\n", "  - preprocessed_datasets/tokenizer/\n", "  - preprocessed_datasets/metadata.json\n", "\n", "🏁 PREPROCESSING COMPLET!\n", "Les datasets sont maintenant prêts pour l'entraînement du modèle FinBERT.\n", "  - preprocessed_datasets/metadata.json\n", "\n", "🏁 PREPROCESSING COMPLET!\n", "Les datasets sont maintenant prêts pour l'entraînement du modèle FinBERT.\n"]}], "source": ["print(\"\\n=== SAUVE<PERSON><PERSON><PERSON> DES DATASETS PRÉPROCESSÉS ===\")\n", "\n", "# Sauvegarder les datasets préprocessés pour utilisation ultérieure\n", "# C<PERSON>er un dossier pour sauvegarder les datasets\n", "save_dir = \"preprocessed_datasets\"\n", "os.makedirs(save_dir, exist_ok=True)\n", "\n", "print(f\"💾 Sauvegarde des datasets dans le dossier '{save_dir}'...\")\n", "\n", "try:\n", "    # Sauvegarder chaque dataset\n", "    final_train.save_to_disk(os.path.join(save_dir, \"train\"))\n", "    final_validation.save_to_disk(os.path.join(save_dir, \"validation\"))\n", "    final_test.save_to_disk(os.path.join(save_dir, \"test\"))\n", "    \n", "    # <PERSON><PERSON><PERSON>er aussi le tokenizer\n", "    tokenizer.save_pretrained(os.path.join(save_dir, \"tokenizer\"))\n", "    \n", "    print(\"✅ Sauvegarde terminée avec succès!\")\n", "    print(f\"📁 Fichiers sauvegardés:\")\n", "    print(f\"  - {save_dir}/train/\")\n", "    print(f\"  - {save_dir}/validation/\")\n", "    print(f\"  - {save_dir}/test/\")\n", "    print(f\"  - {save_dir}/tokenizer/\")\n", "    \n", "    # <PERSON><PERSON>er un fichier de métadonnées\n", "    metadata = {\n", "        \"dataset_name\": dataset_name,\n", "        \"tokenizer_name\": \"ProsusAI/finbert\",\n", "        \"max_length\": 512,\n", "        \"train_size\": len(final_train),\n", "        \"validation_size\": len(final_validation),\n", "        \"test_size\": len(final_test),\n", "        \"preprocessing_date\": pd.Timestamp.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "    }\n", "    \n", "    with open(os.path.join(save_dir, \"metadata.json\"), \"w\") as f:\n", "        json.dump(metadata, f, indent=2)\n", "    \n", "    print(f\"  - {save_dir}/metadata.json\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Erreur lors de la sauvegarde: {e}\")\n", "\n", "print(f\"\\n🏁 PREPROCESSING COMPLET!\")\n", "print(f\"Les datasets sont maintenant prêts pour l'entraînement du modèle FinBERT.\")"]}, {"cell_type": "code", "execution_count": 26, "id": "05b6fe08", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== ÉTAPE 3.7: VISUALISATION DES DONNÉES PRÉPROCESSÉES ===\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABdEAAAHqCAYAAADrpwd3AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjEsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvc2/+5QAAAAlwSFlzAAAPYQAAD2EBqD+naQAAlDZJREFUeJzt3QmcjeX///HPyC5LyDJlK8mSXUkJRYREtJCiEiVLqJQWIUUqiUQkS5Hqm1QqWbKUfYmELGUr27eEL2U//8f7+j3u8z9nZg4z48ycOTOv5+NxO3Pu+z7n3OecMZ/7/lzX9blifD6fzwAAAAAAAAAAQDyZ4q8CAAAAAAAAAABCEh0AAAAAAAAAgBBIogMAAAAAAAAAEAJJdAAAAAAAAAAAQiCJDgAAAAAAAABACCTRAQAAAAAAAAAIgSQ6AAAAAAAAAAAhkEQHAAAAAAAAACAEkugAAAAAAAAAAIRAEh1IIf369bOYmJhUea169eq5xTN//nz32v/5z39S5fXvv/9+K1mypGV03ueuWwBAyiLOpn9xP/ft27e7z33ChAkR+cz0unp9HQcAIG3i/ABASiGJDiThoslbsmfPbrGxsdaoUSMbPny4/e9//wvL6+zevdsF/TVr1lhak5aPLTEnF4HfX6hF+wEAUh9xNm0f27lMmzbNfW/vvvtuyH1mz57t9tH3mda9/PLLNn369EgfBgBkeJwfpO1jS2vX4VOmTLFhw4aF5bmAhGROcC2ABA0YMMBKlSplJ0+etL1797qW5h49etjQoUPtiy++sEqVKvn3fe655+zpp59OcoDs37+/a02uUqVKoh83a9YsS2lnO7axY8famTNnLK16+OGHrUGDBv7727Zts759+1qnTp3shhtu8K+//PLLz+t16tSpY//++69lzZr1vJ4HADIq4mx0xtmmTZta3rx53cXrQw89lOA+2nbBBRdY69atk/06JUqUcHE2S5YsltJJ9DvuuMNatGgRtP6+++5zx58tW7YUfX0AQDDOD6Lz/CC1rsMDzzV+/vln97sBpASS6EASNG7c2GrUqOG/36dPH/vuu+/s1ltvtdtuu802btxoOXLkcNsyZ87slpT0zz//WM6cOSOetE3pi9nzVatWLbd4Vq5c6YK31t17770hH3f06FHLlStXol8nU6ZMrncEACB5iLPRGWeVVFbSefz48e5iX70EAx07dsw+++wzu/nmm61QoULJfh2vF2KkqBFACwAgdXF+kLGuw4G0inIuwHm66aab7Pnnn7cdO3bYBx98cNZabBrKXLt2bcuXL59deOGFduWVV9ozzzzjtqk1/eqrr3Y/P/DAA/6hTV7dT9Vau+qqq2zVqlWux7OCtvfYuLXYPKdPn3b7FClSxCWDdYKxa9euoH3Uop3Q8KnA5zzXsSVUi00J6Mcff9yKFSvmLq71Xl977TXz+XxB++l5unbt6oZN6/1p3woVKtjMmTMtEkMFFyxYYI8++qi7yL/00kvdNn23Wqf3oJOzAgUK2J133hmvJmpCNdG9723Dhg124403uu/tkksusSFDhqTq+wOAaEWcjY44q4th9YabOnVqvG1fffWVHTp0yNq2bevuK9mu71WxVsdTvnx5GzVq1DlfI1RNdO+9KcGuWyXsE6LP57rrrnNxXPG8evXq8erW6vn12U6cODHeMPNQNdHffvtt95nqvagBoUuXLnbw4MGgfTgfAIDw4vwgOs4PEmPZsmV2yy23uFFt+nzr1q1rixYtCtpHpXvUw1zvV8eqcwg1zq9evdpt12em8w39PnifE/XiEW70RAfCQMN7FSQ1nKtjx44J7rN+/XrXUq6hZhqOpj/8W7du9QeHcuXKufVxhzfpYs/z119/uVZ4DSXWxWrhwoXPelwvvfSSCx5PPfWU7d+/39UH03Aq1VPzWuoTIzHHFkgBWicK8+bNsw4dOrhhZ99++609+eST9scff9gbb7wRtP8PP/zg6qkqUZ07d25X365Vq1a2c+dOd6GbmnQMF198sXuvOgGRFStW2OLFi93nrsS6Lp51sa9ArYthBfqz+fvvv91JQcuWLe2uu+5yF+z6TipWrOi+TwDA2RFn036cVWJBMVJDqXv16hW0TesUK73yKIqhulDXe1BvwS+//NIdm5LwSkAnhX4n9F6UiB80aJD7DpVo8BrCA7355pvuNZXMP3HihEv4q1F8xowZriSNvP/++64kzTXXXOO+i3MNM1eyRsPs9b137tzZNm3a5N6fzh30uxfYS5DzAQAIL84P0v75wbloRIE+WzVsv/DCC250t9fY/v3337t4LI888oiLm0r8K+brO9HxaxRCtWrV7Nlnn3UN9r///rv/farBBAgrH4BzGj9+vJptfStWrAi5T968eX1Vq1b133/hhRfcYzxvvPGGu//f//435HPo+bWPXi+uunXrum2jR49OcJsWz7x589y+l1xyie/w4cP+9R9//LFb/+abb/rXlShRwte+fftzPufZjk2P1/N4pk+f7vYdOHBg0H533HGHLyYmxrd161b/Ou2XNWvWoHVr165160eMGOFLCQm9F+87rl27tu/UqVNB+//zzz/xnmPJkiVu/0mTJsX73HUb93sL3O/48eO+IkWK+Fq1apUC7w4Aog9xNn3E2SeffNI976ZNm/zrDh065MuePbuvTZs2Z42rjRo18l122WVn/Yy2bdsW7zOqUqWKr2jRor6DBw/6182aNcvtF/iZJfS6J06c8F111VW+m266KWh9rly5EvzOvN9THYfs37/ffbYNGzb0nT592r/fW2+95fZ77733gt4L5wMAkDQZ7fxgwYIFvvz587vYov0/++yzoGPT/vo5cFGMjXt+EHdRrEuL1+FnzpzxXXHFFe4cQD8HxutSpUr5br755qDvuUuXLmd9/qZNm8aL/UA4Uc4FCBO1cp5tdnANHZPPP/882ZN/qNVcvasSq127dq5F2aN6pUWLFrWvv/46Sa+rYVAJDSPzeott3rzZTfCSJ08et17DqFUztHv37m67em6rJVwtxYrXNWvWdK3M6gUmapUP7OWlXgJ6rt9++81Sm3owxK13GthbQJPZqNW7dOnS7jv1ho+d63cjsOabauepRT0S7w8AolV6jrNJpecPjLMeDd9WnP3mm29s4cKF1qxZM7de8XbdunXx4uy7777remwrzqlX1+jRo+O91pIlS1xvMA1H12O8SbQT4sU69Tz3fPrpp64mulfKJW5cVa+xP//80w3dVlzU/cTas2eP69XXvn17NwTco+Hdej9xBb6ueoXrtdSrLzGxPCFz5sxxn62Gl6vnXOC5hD4rDSsPxPkAAIRfejo/0EhovZ8rrrgi5D4a0aT4p0WjmjSK2uM9v3ple/toUQ9z7/wgUKSvwxXDt2zZYvfcc4+7xtb5gBZ9DvXr13fnMt53pu9RZV809woQKSTRgTA5cuRIUKCM6+6777brr7/eDRHW8C8NBfv444+TFMhVOzMpk5fEDb5KcCv5G7eW57loSLIXcDUUTTXlREOg5dSpU+7C1KsNpyFUqgnqfR6//PKLe5+qGSq6YNWFurd/8eLF473mRRdd5C5wz0aJ+8Al1EV9UmjW97j0vBpC59WVK1iwoDtZUb3TxFzsK0ERty5fYt4fACBjxNmkUr3PwDgbOOzb264L0MqVK4d8Dn0uukhWHVkNhVYiWEOkv/jiC/8+KnPSsGFDNwGYLsw1rFr7BCaMA+niW3VVP/zwQ/86JdQVNxs1auRfpyH0unBXYl4XxYqp3jlBUpLoep+SULJBNWDj0vu59tprXe30/Pnzu9dV6ZWkvGZCrx/3tfQ7dNlll/m3ezgfAIDwS0/nByproutRxc1QdD2qWutadA0eGJMVd9TIrsd7+2hRuRNve6BIX4crgS5qDFdMDlzU0H/8+HF/jNYcIj///LO7JlcDtMqp0QiN1EYSHQgDJY31x12BMRQFOLWkqteSarf99NNPLqCrt5QmHkmMpNRPS6y4F3OewGNSEPMCuXp66SJULdbqNSbq7aX1ujAN1Vru1TUTfU5PPPGEq78mcXt+e+JOfhKXWvMDl48++sjOV0Kfcbdu3VxdO7X064RLNffUkKA6cYk5+Uru+wMAZIw4mxJ0IT5w4MCQ23Vhqliu+T004ky1VpV0X758uX8f9WRXcuLll192+/34448uFuoCPhT1tNYItZUrV7oLa9Vl1WNU+1x+/fVX17tMPc2GDh3qemsrpvbs2dNtT24vwXNRA4DqxCqBrkZ9NQroddX7LbXiMecDABBeGfH8QJONalJNNeBqtFhCcXPw4MHuWrVq1ar26quvuk5vCYn0dbh37DpGxeSEFq+uuc4llDQfMWKE60igx2h+lbi964GUxMSiQBhoEioJ7GWVELUS68JRiy4cdVGqoVa6wFSPrFCBNLm8lt3AYKhJVNRTLLClWT2q41IrtXpRebxjUwBWrzVNGhbqeNXTSsFdw+oCewWoR7qUKFHCXVirF9i2bduS/f68HvEeBdGUoAlM1Dr++uuv+9dpaHpCnxsAIPwyUpxNDMVRJQPOFmfPRYnwXbt2uYnGdDGquK3ktzcZlyZCE5Vu06RfGg6uUWRKVNSuXTvk87Zp08b69OnjeqDrOJQMCCzloklElcBXj/fAHnD6jpLKe59xvwfRBJ+BVFZGCXRNsBbYCKBG/rgS+114r6/XCvwuVeJF5zf6nQMApJyMdn7gTU6t3upqlL7//vtdL3DFWiXEFZf0Wu+99567Jl+8eLGLyV7ZssScH6TmdbhXSkYlZBITM5Ww1ySoWnSeoh726uzmTc4d7u8RiIue6MB50oXliy++6AJZ4EViXAcOHIi3TrNliy4mRcOaJVzJ2UmTJgXVh1MyWBfBXpDxAtfSpUv99clFPc11YR3IOzb15NLxKWCHoqHfCuRvvfVW0HpdmCuwabi5WpAffvjh83p/CrSBi4JqStAJSdzWeB1/SvciBABkvDibmGNr0qTJWeNs4OuHooZslVLRRbaGqOvCfOTIka7muXhDpFWa5cknn3Tfg8q6KAGRUNLao8S4yrapV5oa3fW9XXfddfF6vQXGVfUiTCiZfS6K+/qOJ06cGFSSRRf3GzZsCNpXr6vPJjB2a1j99OnT4z2vvovEfA8699Bn59Wa9YwbN84dT9OmTZP8ngAAiZMRzw9UikajqipWrGgtWrRwMVmPV0O4d36g3t1r1651CftHHnnEdQRTTE7s+UFqXodXr17dfQ6vvfaaG/kW13//+193q9gdt/SaeuOrE4D3HXqfVXJLtAGJQU90IAk0VEi9vNQbe9++fS5w60JNLbrqUaUeTqEMGDDADSPTBZX2V8uphhPr4tXr0aUAogta9fRSzzIFAU3CmVCd7sTQBbKeW5Og6HhVz1w9yDThlUe14RTUdfGsIVJq0dZFb+AEI4HHpn3V0qz3EurY9Fw33nija93XBaqGh6sEiiZz0espea566joODR9P62699VbXy0Ela1S6RsPm1ANQQ+QAAOFDnE3csanMSqg4q9rmcZ87Ibq410WpPld9Xvrs1OtcF6S6IPaGWCtme5OpaVj43LlzXQ+3QYMGnbWki+K7Jv/SMcZtaFfiWe9Bz62L5rFjx7qLYSUYkkrHoe9c38ODDz7okiVq6Na5SuAFufZR70N9Dyrhot8PNRro+9LQ/rgX9Yrz2l+fh74DfRdxqdydevj179/fPa8SG+qVrt87TcgeOIkoACD5OD9I+Nh0X73s1ctdCfWEzg80EksNvRpZnZjzg9SkY1ftcyX3Fbf1ean+vEbJaZSAeqhrBJvOWfR9aYJWvSeVeFGc1txtgaPFFb/VYKBR84rD2s+bZB0ICx+Acxo/fry6F/mXrFmz+ooUKeK7+eabfW+++abv8OHD8R7zwgsvuH09c+fO9TVv3twXGxvrHq/bNm3a+DZv3hz0uM8//9xXvnx5X+bMmd3j9dpSt25dX4UKFRI8Pm3T4pk3b5577Icffujr06ePr1ChQr4cOXL4mjZt6tuxY0e8x7/++uu+Sy65xJctWzbf9ddf71u5cmW855SxY8e6573ggguCjq19+/a+EiVK+F/377//9v3vf//z9ezZ073PLFmy+K644grf888/7ytdurTvvvvu850+fdo9Vvt36dIl3jHp+fS8KWHFihVBxx/4HWtbXHo/DzzwgK9gwYK+Cy+80NeoUSPfL7/8Eu8YvfevW0+o7837zAAAxNlzHVtCMSOhOPvqq6/6zpw5E7Sf95l+9tln/nX//POPW1e/fv2gfTt06OBinPz2229un/fffz9on7vuust3zz33+M7mwIED7r3q8Rs2bIi3/YsvvvBVqlTJlz17dl/JkiV9r7zyiu+9995z+2/bti3k565tceO3fPrpp75y5cq519TnN23atAQ/s3HjxrnPSfuVLVvWPU/c3yNRjK9Tp477TrXNi/Xe72ngMcpbb73lnk/fQ+HChX2dO3d25w6BOB8AgKTLyOcHgbE71LHdcccd7r62hzo/0OcVExPj++uvv4JeO61ch8uPP/7oa9mypa9AgQLus9Ax6HxD350cP37c9+STT/oqV67sy507ty9Xrlzu57fffjvoeY4cOeLOUfLly+deh/iKcIvRP+FJxwNI7zQD9jvvvOOGmHkThAXSMDK1fGs2b7WWB1JrsrapdVgt7KEmMQEAAOGlIdyfffaZG/othw8fdqOrNLlm4NBu9QxXLW/1atclgnp9qXe3hst71Btdj1E9WQAAED4aQaVe5V681YgoXUOrZ7sWjXxq1aqVFSlSxPVc7927t+ulvW7dOjffh0ZML1u2zD1GvdR1XxN3K26r9BmA80M5FwCJomHdqleqYWBxE+iaJFSLF/AVxBW0VRdVwV4J9Hr16rnhc6p35tU2E50AAACAlLsQFyXH16xZ4+Ky4nPdunVdrfMcOXK4+LxgwQJXw1UX7F7iXdtfeOEFN3Taqz2u4fQafg4AAMJr5cqVLgHuUVkS0TX4qFGjXPkxxWLVR1e5MZVIU0O3N2G2bqdOneo6v6lWuEq+KInuPQ+A80NPdACJol5pmvVctT7LlCkTtE1BWq3icSnprglIJ0yY4K+nGhd/ggAACD9vdFhcuhBXXFbjt+p5K76rjrgS6apjrottJdA9gwcPdrXDtY+S6UOGDPHXkAUAAAAyCpLoAAAAAAAAAACEkCnUBgAAAAAAAAAAMjqS6AAAAAAAAAAAhEASHQAAAAAAAACAEDKH2pCRnDlzxnbv3m25c+cOmkgJAIBI0rQl//vf/yw2NtYyZaLdOxCxGwCQFhG7QyN2AwCiOXaTRDdzgbxYsWKRPgwAABK0a9cuu/TSSyN9GGkKsRsAkJYRu+MjdgMAojl2k0Q3cy3h3oeVJ0+eSB8OkKKeeeYZ18I2YsQIdz9v3rzx9hk3bpzdcccd/vtjx461MWPG2M6dO90flCeeeMLatGmTqNc7cOCAXX/99e6keceOHZYvXz63fu3atdalSxf77bff7IYbbrBRo0ZZ/vz53bZTp07ZTTfdZG+88YZVr1496Pnq1atnjzzyiLVu3fq8PgcgGhw+fNhdbHpxCv8fsRsZOXbL5MmTbeTIkbZ161b3/6FFixb2+uuv+7f//PPPLl6vXr3aChYsaJ06dbIePXqEfI1169a5uLt06VL766+/rHjx4vbggw9a586d/fsQu4FzI3aHRuxGRpKc6+6PP/7Y3nzzTfv111/d/5Gbb77ZXnzxRX+sTeha+6GHHrL169e7ny+++GJr0qSJ9e3b1/9/jNgNhDF2++A7dOiQTx+FboH07OjRo748efL4lixZ4l+n3/3x48f79uzZ41/+/fdf//a3337blzt3bt/UqVN9v/76q+/DDz/0XXjhhb4vvvgiUa/ZvHlzX+PGjd3r/P333/711apV8/Xq1cu3adMm3w033OB7/PHH/dsGDx7s69atW4LP99Zbb/lq1KiRzE8AiC7Ep9D4bJCRY/frr7/ui42N9U2ePNm3detW39q1a32ff/65f7v+XxQuXNjXtm1b388//+xid44cOXzvvPNOyNcZN26cr3v37r758+e7eP/++++7x4wYMcK/D7EbODfiU2h8NsgoknPd/cMPP/gyZcrke/PNN32//fab7/vvv/dVqFDBd/vtt4d8nQMHDrjr9RUrVvi2b9/umzNnju/KK6/0tWnTxr8PsRsIX3wiiU4wRwbyySef+C6++OKgdfrd/+yzz0I+platWr4nnngiaJ2C8PXXX3/O11NAr1u3rm/u3Lnxkui6MN+4caN/vyZNmrifdeF+xRVX+A4fPpzgc+7YscM9l5IGQHpHfAqNzwYZNXbrglkxVBfKoSiuXnTRRb7jx4/71z311FPuwjopHn30Ud+NN97ov0/sBqIzPi1YsMB36623+ooWLRry3H/Dhg2+Zs2aucRfzpw5XfJM/3c9Svbpb0L+/Pl9uXLl8rVs2dK3d+/eqP9sgLRy3f3qq6/6LrvssqB1w4cP911yySVJem0l4S+99FL/fWI3EL74xEwnQAby/fffxxumJRrepaHe11xzjb333ntuUgXP8ePHLXv27EH758iRw5YvX24nT54M+VobNmywAQMG2KRJkxKcmKFy5co2e/ZsN4Rs7ty5VqlSJbdeQ8aGDBkSchiNhpcXLlzYvRcAADJa7Fbs1OR8f/zxh5UrV86VWbvrrrtceQTPkiVLrE6dOpY1a1b/ukaNGtmmTZvs77//TvRrHzp0KGgIObEbiE5Hjx51/39VAiohKh1Ru3ZtK1u2rM2fP99++ukne/7554OuAXr27GlffvmlffLJJ7ZgwQJXqrFly5ap+C6A9H3dXatWLRfLv/76a7d+37599p///MeVZ0ks/b+cNm2a1a1b17+O2A2ED0l0IANRTXLNNhxIiW7VXlNgbdWqlT366KNBNVd10f3uu+/aqlWrXDBfuXKlu68E+p9//png6yjxrprpr776qgu+CdFz6KTg8ssvdxf5ffr0sffff99y5sxpV199tXvd0qVL23PPPRfvsXoPei8AAGS02K2apkqiv/zyyzZs2DAXS1UHVXVTT5w44fbZu3evu/AN5N3XtsRYvHixffTRR66WuofYDUSnxo0b28CBA+32229PcPuzzz7rEnVKqFWtWtX9H7/tttusUKFC/gY11W4eOnSoq5+s5OD48ePd3wnNowDg/K+7NY+Y5ju5++67XYwtUqSIq6MeqvErkK69FYsvueQSVwtd8dpD7AbCh4lFgQzk33//jderXL1MPDppVk8VJb+7d+/u364L7muvvdYl0XUR3r59e3eSnVAPc1FgVu+4e++9N+SxVKhQwfVi8WgSsxdeeMEWLlxo3bp1s+uuu861oiuw16xZ05o1axbUE/6ff/45r88CAIBojN1KoKshe/jw4dawYUO37sMPP3QX2/PmzXMXw+dLk5I2b97cxWXvNYTYDaQ/+pvy1VdfWe/evd3fjx9//NFKlSrlzuc1YbGoM43+7jRo0MD/OPVaV2cZjXzRdUKojjVaAiduAzKC5Fx3ayT3Y4895iYF1f/FPXv22JNPPul6jKsR62w0Maji8ebNm93/3V69etnbb7/tthG7gfChJzqQgWjo2LmGcStw/v777/4TXgVODTVT8Ny+fbvt3LnTSpYs6YZ9afbvhHz33XduqGfmzJndUr9+ff/rK2AnRIG+R48ebli6hpHeeeedlitXLmvatKm7H8ibeRwAgIwWu4sWLepuy5cv71+nmKj9FKNFCXUNAw/k3de2s9FFvOK2eqAn1CstELEbiH779++3I0eO2ODBg+2WW26xWbNmuR7rKtXiJd7UoUY9WPPlyxf0WHWuOdvolkGDBrmetN5SrFixFH8/QLRed+v/i3qjK3GukitKpCsRrmtxJdTPRrFdDVsaQfLOO+/YqFGjQj6G2A0kH0l0IANRi7cujs9mzZo1dtFFF1m2bNmC1mfJksUF2gsuuMCmTp1qt956a8ie6J9++qmtXbvWPZcWbziZ6qmpDlxcqs22ceNG69q1q7t/+vRpf7113eq+59ixY65uo94LAAAZLXbrAltU3zzwIlcl1kqUKOGvq6oeZoFzl2j4+JVXXulifCjr16+3G2+80Y04e+mll856XMRuIP30RBeNPlHd8ypVqtjTTz/tzvVHjx59Xs+tHrEqBeMtgXM3AOlZcq671Wkt7vW1rr0lsHZ6Yv9PB44C8RC7gfNDEh3IQNSarQtkr1VckwMpwa1h21u3bnUt1qqxqmFdHg0J++CDD2zLli1uMtHWrVu7/bWf57PPPnMt3x7VW7vqqqv8i4aEikq8eLUVA4OzgviYMWP8Jw1KEKj2mxLxSsh7CQNR3UWdaChBAABARovdZcqUcckuDflWPWLFZCW9FYeVAJd77rnH9Rrt0KGDe6xqm7/55puu91mo2K3n0eNVvkX7qXeplv/+97/xjonYDaSvHrMaORo4usU7bw8c3aI5Fw4ePBhvhMvZRrfo/73qMwcuQEaQnOtulVFRWRVt0/wnixYtcqVeNAmpV189buzWJKSan0DPq1HjKs2k8i+KwRo9HojYDZw/kuhABlKxYkWrVq2am9DE612uoKnAqF4nGvqlCYMCS66oNfr11193s3pr0jIFX120BwZl9SwJ7BGXFP3793dDx/T6HtV5Vct8nTp13MmEJl7xqO5r27Zt3UQoAABktNgtkyZNcsPAFT/r1q3r4vnMmTPdrahsgkoybNu2zU0A+Pjjj7saq4GThMaN3Zp0TAlzNZyrZIy3qEZqXMRuIP1Qg5v+n8c9l1dHGm90i/6O6O+LerF6tL+S7CTYgPBcd99///1u3VtvveU6oqnMikaQKbEeKnar9OrYsWOtdu3aruFLo0lU0mXGjBnxjonYDZy/GF9SxoWkU5rgRBcb+oNE6zjSO7VOq86aWqtDlWNJqzRUXScSK1eu9PduB9Iz4lNofDbISIjdQPRIi/FJNc/V+1VUmkGJOo08yZ8/v5scVL1b7777bpfk03o1yqlmsuojKzknnTt3dr1eJ0yY4N6X14NWnWui+bMBUgqxG4geiY1PmVP1qABEnFqfVZrljz/+iLrJfTRETZOrEMgBABkJsRvA+VAizCv3JF5pJ5WCUlJcE4mq/rkmNlT5CCXPVNrBS6DLG2+84RKB6qmqWsvepIcAEkbsBtIfeqLTIg4ASKOIT6Hx2QAA0iLiU2h8NgCAaI5P0TWmBAAAAAAAAACAVEQSHQAAAAAAAACAEEiiAwAAAAAAAAAQAkl0AAAAAAAAAABCIIkOAAAAAAAAAEAIJNEBAAAAAAAAAAghc6gNAFLY/JhIHwGSo54v0kcAAIgUYnd0InYDQMZF7I5OxG6kQfREBwAAAAAAAAAgBJLoAAAAAAAAAACkxSR6yZIlLSYmJt7SpUsXt/3YsWPu5wIFCtiFF15orVq1sn379gU9x86dO61p06aWM2dOK1SokD355JN26tSpCL0jAAAAAAAAAEB6EtEk+ooVK2zPnj3+Zfbs2W79nXfe6W579uxpX375pX3yySe2YMEC2717t7Vs2dL/+NOnT7sE+okTJ2zx4sU2ceJEmzBhgvXt2zdi7wkAAAAAAAAAkH5ENIl+8cUXW5EiRfzLjBkz7PLLL7e6devaoUOHbNy4cTZ06FC76aabrHr16jZ+/HiXLF+6dKl7/KxZs2zDhg32wQcfWJUqVaxx48b24osv2siRI11iHQAAAAAAAACAdFETXUlvJcMffPBBV9Jl1apVdvLkSWvQoIF/n7Jly1rx4sVtyZIl7r5uK1asaIULF/bv06hRIzt8+LCtX78+5GsdP37c7RO4AAAAAAAAAACQZpPo06dPt4MHD9r999/v7u/du9eyZs1q+fLlC9pPCXNt8/YJTKB7271toQwaNMjy5s3rX4oVK5YC7wgAAAAAAAAAEO3STBJdpVtUjiU2NjbFX6tPnz6uXIy37Nq1K8VfEwAAAAAAAAAQfTJbGrBjxw6bM2eOTZs2zb9ONdJV4kW90wN7o+/bt89t8/ZZvnx50HNpu7ctlGzZsrkFAAAAAAAAAIA03xNdE4YWKlTImjZt6l+niUSzZMlic+fO9a/btGmT7dy502rVquXu63bdunW2f/9+/z6zZ8+2PHnyWPny5VP5XQAAAAAAAAAA0puI90Q/c+aMS6K3b9/eMmf+/4ejWuUdOnSwXr16Wf78+V1ivFu3bi5xfu2117p9GjZs6JLl9913nw0ZMsTVQX/uueesS5cu9DQHAAAAAAAAAER/El1lXNS7/MEHH4y37Y033rBMmTJZq1at7Pjx49aoUSN7++23/dsvuOACmzFjhnXu3Nkl13PlyuWS8QMGDEjldwEAAAAAAAAASI8inkRXb3Kfz5fgtuzZs9vIkSPdEkqJEiXs66+/TsEjBAAAAAAAAABkVGmiJjoAAAAAAAAAAGkRSXQAAAAAAAAAAEIgiQ4AAAAAAAAAQAgk0QEAAAAAAAAACIEkOgAAAAAAAAAAIZBEBwAAAAAAAAAgBJLoAAAg0RYuXGjNmjWz2NhYi4mJsenTpwdt17qElldffdW/T8mSJeNtHzx4cATeDQAAAAAA50YSHQAAJNrRo0etcuXKNnLkyAS379mzJ2h57733XJK8VatWQfsNGDAgaL9u3bql0jsAAAAAACBpMidxfwAAkIE1btzYLaEUKVIk6P7nn39uN954o1122WVB63Pnzh1vXwAAAAAA0iJ6ogMAgBSxb98+++qrr6xDhw7xtql8S4ECBaxq1aqu1MupU6fO+lzHjx+3w4cPBy0AAAAAAKQGeqIDAIAUMXHiRNfjvGXLlkHru3fvbtWqVbP8+fPb4sWLrU+fPq6ky9ChQ0M+16BBg6x///6pcNQAAAAAAAQjiQ4AAFKE6qG3bdvWsmfPHrS+V69e/p8rVapkWbNmtYcfftglyrNly5bgcynRHvg49UQvVqxYCh49AAAAAAD/hyQ6AAAIu++//942bdpkH3300Tn3rVmzpivnsn37drvyyisT3EfJ9VAJdgAAAAAAUhI10QEAQNiNGzfOqlevbpUrVz7nvmvWrLFMmTJZoUKFUuXYAAAAAABICnqiAwCARDty5Iht3brVf3/btm0uCa765sWLF/eXWvnkk0/s9ddfj/f4JUuW2LJly+zGG2909dJ1v2fPnnbvvffaRRddlKrvBQAAAACAxCCJDgAAEm3lypUuAe7x6pS3b9/eJkyY4H6eOnWq+Xw+a9OmTbzHqySLtvfr18+OHz9upUqVckn0wHrnAAAAAACkJSTRAQBAotWrV88lyM+mU6dObklItWrVbOnSpSl0dAAAAAAAhB810QEAAAAAAAAACIEkOgAAAAAAAAAAIZBEBwAAAAAAAAAgBJLoAAAAAAAAAACEQBIdAAAAAIB0auHChdasWTOLjY21mJgYmz59esh9H3nkEbfPsGHDgtYfOHDA2rZta3ny5LF8+fJZhw4d7MiRI6lw9AAApA0k0QEAAAAASKeOHj1qlStXtpEjR551v88++8yWLl3qku1xKYG+fv16mz17ts2YMcMl5jt16pSCRw0AQNqSOdIHAAAAAAAAUkbjxo3dcjZ//PGHdevWzb799ltr2rRp0LaNGzfazJkzbcWKFVajRg23bsSIEdakSRN77bXXEky6AwCQ3tATHQAAAACADOrMmTN233332ZNPPmkVKlSIt33JkiWuhIuXQJcGDRpYpkyZbNmyZal8tAAARAY90QEAAAAAyKBeeeUVy5w5s3Xv3j3B7Xv37rVChQoFrdP++fPnd9tCOX78uFs8hw8fDuNRAwCQuuiJDgAAAABABrRq1Sp78803bcKECW5C0XAaNGiQ5c2b178UK1YsrM8PAEBqIokOAAAAAEAG9P3339v+/futePHirne5lh07dtjjjz9uJUuWdPsUKVLE7RPo1KlTduDAAbctlD59+tihQ4f8y65du1L8/QAAkFIo5wIAAAAAQAakWuiqbx6oUaNGbv0DDzzg7teqVcsOHjzoeq1Xr17drfvuu+9cLfWaNWuGfO5s2bK5BQCA9IAkOgAAAAAA6dSRI0ds69at/vvbtm2zNWvWuJrm6oFeoECBoP2zZMniephfeeWV7n65cuXslltusY4dO9ro0aPt5MmT1rVrV2vdurXFxsam+vsBACASKOcCAAAAAEA6tXLlSqtatapbpFevXu7nvn37Jvo5Jk+ebGXLlrX69etbkyZNrHbt2jZmzJgUPGoAANIWeqIDAAAAAJBO1atXz3w+X6L33759e7x16rU+ZcqUMB8ZAADRg57oAAAAAAAAAACEQBIdAAAAAAAAAIAQSKIDAAAAAAAAABACSXQAAAAAAAAAAEIgiQ4AAAAAAAAAQAgk0QEAAAAAAAAACIEkOgAAAAAAAAAAIZBEBwAAAAAAAAAgBJLoAAAAAAAAAACEQBIdAAAAAAAAAIAQSKIDAAAAAAAAABACSXQAAAAAAAAAAEIgiQ4AAAAAAAAAQFpNov/xxx927733WoECBSxHjhxWsWJFW7lypX+7z+ezvn37WtGiRd32Bg0a2JYtW4Ke48CBA9a2bVvLkyeP5cuXzzp06GBHjhyJwLsBAAAAAAAAAKQnEU2i//3333b99ddblixZ7JtvvrENGzbY66+/bhdddJF/nyFDhtjw4cNt9OjRtmzZMsuVK5c1atTIjh075t9HCfT169fb7NmzbcaMGbZw4ULr1KlThN4VAAAAAAAAACC9yBzJF3/llVesWLFiNn78eP+6UqVKBfVCHzZsmD333HPWvHlzt27SpElWuHBhmz59urVu3do2btxoM2fOtBUrVliNGjXcPiNGjLAmTZrYa6+9ZrGxsRF4ZwAAAAAAAACA9CCiPdG/+OILl/i+8847rVChQla1alUbO3asf/u2bdts7969roSLJ2/evFazZk1bsmSJu69blXDxEuii/TNlyuR6rgMAAAAAAAAAEJVJ9N9++81GjRplV1xxhX377bfWuXNn6969u02cONFtVwJd1PM8kO5723SrBHygzJkzW/78+f37xHX8+HE7fPhw0AIAAAAAAAAAQJoq53LmzBnXg/zll19299UT/eeff3b1z9u3b59irzto0CDr379/ij0/AAAAAAAAACB9iGhP9KJFi1r58uWD1pUrV8527tzpfi5SpIi73bdvX9A+uu9t0+3+/fuDtp86dcoOHDjg3yeuPn362KFDh/zLrl27wvq+AAAAAAAAAADpQ0ST6Ndff71t2rQpaN3mzZutRIkS/klGlQifO3euf7tKr6jWea1atdx93R48eNBWrVrl3+e7775zvdxVOz0h2bJlszx58gQtAAAAAAAAAACkqXIuPXv2tOuuu86Vc7nrrrts+fLlNmbMGLdITEyM9ejRwwYOHOjqpiup/vzzz1tsbKy1aNHC33P9lltusY4dO7oyMCdPnrSuXbta69at3X4AAAAAAAAAAERlEv3qq6+2zz77zJVXGTBggEuSDxs2zNq2bevfp3fv3nb06FHr1KmT63Feu3ZtmzlzpmXPnt2/z+TJk13ivH79+pYpUyZr1aqVDR8+PELvCgAAAAAAAACQXsT4fD6fZXAqEZM3b15XH53SLkg182MifQRIjnoZ/k8mUhHxKTQ+G0QEsTs6EbuRiohPofHZICKI3dGJ2I00GJ8iWhMdAAAAAAAAAIC0jCQ6AAAAAAAAAAAhkEQHAAAAAAAAACAEkugAAAAAAAAAAIRAEh0AACTawoULrVmzZhYbG2sxMTE2ffr0oO3333+/Wx+43HLLLUH7HDhwwNq2besmbcmXL5916NDBjhw5ksrvBAAAAACAxCGJDgAAEu3o0aNWuXJlGzlyZMh9lDTfs2ePf/nwww+DtiuBvn79eps9e7bNmDHDJeY7deqUCkcPAAAAAEDSZU7GYwAAQAbVuHFjt5xNtmzZrEiRIglu27hxo82cOdNWrFhhNWrUcOtGjBhhTZo0sddee831cAcAAAAAIC2hJzoAAAir+fPnW6FChezKK6+0zp07219//eXftmTJElfCxUugS4MGDSxTpky2bNmyCB0xAAAAAACh0RMdAACEjUq5tGzZ0kqVKmW//vqrPfPMM67nupLnF1xwge3du9cl2ANlzpzZ8ufP77aFcvz4cbd4Dh8+nKLvAwAAAAAAD0l0AAAQNq1bt/b/XLFiRatUqZJdfvnlrnd6/fr1k/28gwYNsv79+4fpKAEAAAAASDzKuQAAgBRz2WWXWcGCBW3r1q3uvmql79+/P2ifU6dO2YEDB0LWUZc+ffrYoUOH/MuuXbtS/NgBAAAAABCS6AAAIMX8/vvvriZ60aJF3f1atWrZwYMHbdWqVf59vvvuOztz5ozVrFnzrJOV5smTJ2gBAAAAACA1UM4FAAAk2pEjR/y9ymXbtm22Zs0aV9Nci0qutGrVyvUqV0303r17W+nSpa1Ro0Zu/3Llyrm66R07drTRo0fbyZMnrWvXrq4MTGxsbATfGQAAAAAACaMnOgAASLSVK1da1apV3SK9evVyP/ft29dNHPrTTz/ZbbfdZmXKlLEOHTpY9erV7fvvv3c9yT2TJ0+2smXLuhrpTZo0sdq1a9uYMWMi+K4AAAAAAAiNnugAACDR6tWrZz6fL+T2b7/99pzPoR7rU6ZMCfORAQAAAACQMuiJDgAAAAAAAABACCTRAQAAAAAAAAAIgSQ6AAAAAADp1MKFC61Zs2ZuAu+YmBibPn26f5sm+H7qqaesYsWKlitXLrdPu3btbPfu3UHPceDAAWvbtq3lyZPH8uXL5+Y90WTjAABkFCTRAQAAAABIp44ePWqVK1e2kSNHxtv2zz//2OrVq+355593t9OmTbNNmza5ScIDKYG+fv16mz17ts2YMcMl5jt16pSK7wIAgMhiYlEAAAAAANKpxo0buyUhefPmdYnxQG+99ZZdc801tnPnTitevLht3LjRZs6caStWrLAaNWq4fUaMGGFNmjSx1157zfVeBwAgvaMnOgAAAAAAcA4dOuTKvqhsiyxZssT97CXQpUGDBpYpUyZbtmxZBI8UAIDUQ090AAAAAABgx44dczXS27Rp4+qfy969e61QoUJB+2XOnNny58/vtoVy/Phxt3gOHz6cgkcOAEDKoic6AAAAAAAZnCYZveuuu8zn89moUaPO+/kGDRrkysV4S7FixcJynAAARAJJdAAAAAAAMjAvgb5jxw5XI93rhS5FihSx/fv3B+1/6tQpO3DggNsWSp8+fVxpGG/ZtWtXir4HAABSEuVcAAAAAADI4An0LVu22Lx586xAgQJB22vVqmUHDx60VatWWfXq1d267777zs6cOWM1a9YM+bzZsmVzCwAA6QFJdAAAAAAA0qkjR47Y1q1b/fe3bdtma9ascTXNixYtanfccYetXr3aZsyYYadPn/bXOdf2rFmzWrly5eyWW26xjh072ujRo13SvWvXrta6dWuLjY2N4DsDACD1kEQHAAAAACCdWrlypd14443++7169XK37du3t379+tkXX3zh7lepUiXoceqVXq9ePffz5MmTXeK8fv36lilTJmvVqpUNHz48Vd8HAACRRBIdAAAAAIB0SolwTRYaytm2edQrfcqUKWE+MgAAogcTiwIAAAAAAAAAEAJJdAAAAAAAAAAAQiCJDgAAAAAAAABACCTRAQAAAAAAAAAIgSQ6AAAAAAAAAAAhkEQHAAAAAAAAACAEkugAAAAAAAAAAIRAEh0AAAAAAAAAgBBIogMAAAAAAAAAEAJJdAAAAAAAAAAAQiCJDgAAAAAAAABACCTRAQAAAAAAAAAIgSQ6AAAAAAAAAESh//3vf9ajRw8rUaKE5ciRw6677jpbsWKFf/u+ffvs/vvvt9jYWMuZM6fdcssttmXLlogeczQiiQ4AAAAAAAAAUeihhx6y2bNn2/vvv2/r1q2zhg0bWoMGDeyPP/4wn89nLVq0sN9++80+//xz+/HHH12yXduPHj0a6UOPKiTRAQBhN3jwYIuJiXGt4Z569eq5dYHLI488EvS47t27W/Xq1S1btmxWpUqVCBw5AAAAAADR4d9//7VPP/3UhgwZYnXq1LHSpUtbv3793O2oUaNcj/OlS5e6n6+++mq78sor3c963Icffhjpw48qJNEBAGGlYWPvvPOOVapUKd62jh072p49e/yLAn1cDz74oN19992pdLQAAAAAAESnU6dO2enTpy179uxB61XW5YcffrDjx4+7+4HbM2XK5DquaTsSjyQ6ACBsjhw5Ym3btrWxY8faRRddFG+76q8VKVLEv+TJkydo+/Dhw61Lly522WWXpeJRAwAAAAAQfXLnzm21atWyF1980Xbv3u0S6h988IEtWbLEdVwrW7asFS9e3Pr06WN///23nThxwl555RX7/fff3XYkHkl0AEDYKAHetGlTV18tIZMnT7aCBQvaVVdd5YL4P//8k+rHCAAAAABAeqFa6Kp9fskll7ge5uqc1qZNG9fjPEuWLDZt2jTbvHmz5c+f33VsmzdvnjVu3NhtR+JF9NNSjZ649XHVQuI5duyYS8gUKFDALrzwQmvVqpWbUTbQzp07XcJGvwSFChWyJ5980g1lAACkrqlTp9rq1att0KBBCW6/5557XIu4ArYS6Ar09957b6ofJwAAAAAA6cXll19uCxYscCPDd+3aZcuXL7eTJ0/6R3hr3rE1a9bYwYMHXe/zmTNn2l9//cUI8CTKbBFWoUIFmzNnjv9+5sz//5B69uxpX331lX3yySeWN29e69q1q7Vs2dIWLVrktmuIghLoKgmwePFi94vQrl0718ry8ssvR+T9AEBGpED92GOPuRnB49Zi83Tq1Mn/c8WKFa1o0aJWv359+/XXX13QBwAAAAAAyZMrVy63qGzLt99+G28OMuVWRZONrly50pWAQRQl0ZU0VxI8rkOHDtm4ceNsypQpdtNNN7l148ePt3LlyrlZZa+99lqbNWuWbdiwwSXhCxcubFWqVHG/AE899ZTr5Z41a9YIvCMAyHhWrVpl+/fvt2rVqvnXqaFz4cKF9tZbb7nJTC644IKgx9SsWdPdbt26lSQ6AAAAAADJoIS5yrlceeWV7vpaVTpU6eOBBx5w29U5+eKLL3a10detW+c6wLVo0cIaNmwY6UOPKhEvfqPWj9jYWDeEQJPRqTyLl5DR0IPAurpeMXwVxxfdqjejEuieRo0a2eHDh239+vUReDcAkDGpR7mCsYaIeUuNGjXc33X9HDeBLlov6pEOAAAAAACSTh2RVQ5beVNV6Khdu7ZLrKtSh6hyx3333ee2d+/e3f384YcfRvqwo05Ee6KrF+KECRNcS4m+0P79+9sNN9xgP//8s+3du9f1JM+XL1/QY5Qw1zbRbWAC3dvubQtFPSK1eJR0BwCc34zgmiw0kIaRaU4LrVfJFo0satKkiVv3008/uZJdderUsUqVKvkfo1Zz1XHT3/B///3Xn2gvX748o4sAAAAAAIjjrrvucksoSpxrQRQn0TUTrEdJFCXVS5QoYR9//LHlyJEjxV5Xk94pYQ8ASB1KgKv01rBhw+zo0aNWrFgxN1n0c889F7TfQw895CZE8VStWtXdbtu2zUqWLJnqxw0AAAAAABDxmuiB1Ou8TJkyrifizTffbCdOnHAzxwb2Rt+3b5+/hrpuNeNsIG33toXSp08f69WrV1BPdCV0AADhM3/+fP/P+hsbmBxPzGMAAAAAAADSRU10TRyn4faa+fV8aQi/hvyrPm716tVd7Z65c+f6t2/atMnVTK9Vq5a7r1vV4NVkdp7Zs2dbnjx53ND/ULJly+b2CVwAAMgowhm7AQBAyiN2AwAQWUlOovfo0cPGjRvnD+R169a1atWquV6GSe1B+MQTT7ieidu3b7fFixfb7bff7iafa9OmjeXNm9c6dOjgeozPmzfPTTSqWWWVOL/22mvd4zWLrJLlKoi/du1aVzRfpQFUTF+JcgAAEN7YDQAAUh6xGwCAKE+i/+c//7HKlSu7n7/88ktXp/aXX35xE8Q9++yzSXqu33//3SXMNbGoCuBrsrmlS5faxRdf7La/8cYbduutt7q6uZp8TiVapk2b5n+8Eu4zZsxwt0qu33vvvW4W2gEDBiT1bQEAkG6FM3YDAICUR+wGACBtifH5fL6kPCB79uyuZvmll15qnTp1spw5c7qJ4hTUFeRVXzza6JjV8/3QoUOUdkHqmR8T6SNActRL0p9MIE3EJ2I3ECbE7uhE7EYqInaHRuxGRBC7o1MGid1//fWXlStXzs33WLJkSYsmo0ePtq+++so19Ea7xManJPdEL1y4sG3YsMENKZs5c6abAFT++ecf1yMcAACkLcRuAACiC7EbANK/l156yZo3b+4S6Eqo33LLLRYbG+tKVKt8V9euXYMaTe+//36LiYmJt1SoUCHka6gEmF5D80/mypXLqlSpYpMnTw7aR/NLlilTxiWQVTL7xIkT/m1KLGvbjh07gh7z4IMP2urVq+3777+3jCLJSXTVJVfplauuusp9UQ0aNHDrly1bZmXLlk2JYwQApCIF70KFCrn5KqKNLjJ1UnDmzJlIH0qaEs7YvXDhQmvWrJk7udNzTZ8+3b/t5MmT9tRTT1nFihXdCZr2UZm13bt3Bz2HThLjnvgNHjw4TO8WAIDox3U3AKRvahTV3BeaD1IyZcrkkt1ffPGFbd682SZMmGBz5syxRx55xP+YN9980/bs2eNfdu3aZfnz57c777wz5OtoDspKlSrZp59+aj/99JOLL7pGU3ls0bXzPffc415nyZIltnLlShszZoz/8U8//bTbVqJEiaDnzZo1q3vc8OHDLaNIchK9X79+9u6777ohZYsWLfJP4KnWcH2wAIDolhqt4aJqYq+99ppr1dZzX3LJJe61PT/++KNVrVrVLrzwQpe0PXDggH/bqVOnrHr16m7YWyAda5YsWeK1rGd04YzdR48edcPIR44cmeCJoHojPP/88+5W85hs2rTJbrvttnj7av6SwBPAbt26ncc7BAAkx9kaRkV/xxs2bOjmrtL2NWvWxHuOY8eOWZcuXdw+itmaz2rfvn2p+C7SJ667ASB9+/rrr93f9muvvdbdv+iii6xz585Wo0YNl7CuX7++Pfroo0E9vVVyRPNFeosS3n///bdLjIfyzDPP2IsvvmjXXXedXX755fbYY4+562Zvzsk///zTLXotXcPr2m3jxo3+BPyKFSvcYxKicwgl/f/991/LCDIn50F33HGH/4TJ0759+/AdFQAgoq3h3377bVBr+MCBA92kz6rNqQtlJbSnTJnibw0P7EWsBLeSrGdrDRcF4lmzZrlEunou6zkDE+UPPfSQ3XTTTfbRRx+5n19++WW3r7z++ut2/fXX2zXXXBPveZXUV2u4hqEh/LG7cePGbkmITuo0FDDQW2+95b6nnTt3WvHixf3rc+fO7U78AACR4zWMakh2y5YtE9xeu3Zt1yO6Y8eOCT6HJrpUTdRPPvnExQE1tuu5lPjF+eG6GwDSLyXH1TEsFI3mVaK7bt26IffRtbtGKsXtJX4uKtGiWuyi63yVetG1uZ5Lx6VYo1HGSuq/9957IcuI1ahRw13/a5RUvXr1LL1Lck901WRTC4Z6DKqnwW+//ebWq9eZvjwAQPRKrdZwtWyPGjXKPv/8c9fSXapUKXcC4dX79PbRBbt6qrdp08bfGq64o3gT2Gs9bmu4juHXX38N4ycT3SIZu3WCpt6L+fLlC1qvhhf1WtRog1dffdWdfAEAUpcaRdVQfvvttye4XQ3Sffv29ZcSSehvvOLI0KFDXcO3Yvn48eNdz7WlS5em8NGnb1x3A0D6phrjGgkWl659NZm0/v6rRrlGJYVKsn/zzTeuw1lSfPzxx653uXe9rms1rVPMUU90XZ+pcV3XazfeeKOb6Fod2K688krXQSqQjlP5gLj10tOrJCfRlbRQXZ4hQ4a4+jce1WoL9cUCAKJDarWGawbvyy67zNVhUwJdpWMU/AN7oqtnnHo1K7k6d+5cV8dNVI9NMUg9mROi3s6ajCsjTXCSVmO3es6pRrpOBANnOe/evbtNnTrV5s2bZw8//LAbZdC7d++zPtfx48ddGaHABQAQWatWrXI91QKT7KrXrVisuqpIPq67ASB9UwkUJajjeuONN1xpTHU4U8ewXr16Jfj4iRMnuo5KLVq0SPRr6vpLyfOxY8cGlV/VqDMl1rdt2+bKdup20qRJrqFdDeoqLfb999+7kpyqqx4oR44cbkR7RpDkJLo+RBWYb9u2bVB3fiU7fvnll3AfHwAgHbaGqzeVXktDvxVXdJGoC3Fv2LLoNf7zn/+4um26eOzTp4+9//777jiuvvpqa9SokZUuXdqee+65eM+v95BRWsPTauxWUkXD/1X7XqMOAulEUMP91DCiRhGV5xkxYoRLlIcyaNAg18vBW1SfHwAQWXv37nUxOu5oIzVmaxuSj+tuAEjfChYs6EZwx6XR3WqQ1ojtd955x11LaQ6pQLrGUpkVJbgDG1rPZsGCBW7UtpL0mlj0bNTRSddomnRUc5WpVGuhQoVcZzo9TyB1hFNJmIwgyUn0P/74wyUt4tIHqwtmAED0Sq3WcMUMJUx1gXjDDTe4hKp6sKtlXBNRilrGFaCVDFf9dcWYF154wQ0h0ySUmhhl7dq1rme8erZn1NbwtBi7vQS6vjuNJgjshZ6QmjVruhEH27dvD7mPGlFUNsBbNBM9AADpFdfdAJC+qWzKhg0bzrqP/uZL3M5Guk7WfGUdOnRI1GvNnz/fmjZtaq+88orrVX42ui7Pnz+/S+KrtJh4cefkyZP+daLcgEYf671kBElOopcvXz7BIfLqLZhRPjQASK9SqzVcE5dkzpzZ1Tv3eBObaALKhChx36NHD7v00kvdSYBaw3PlyuVOBnQ/o7aGp7XY7SXQt2zZYnPmzHF1z89lzZo1bhJb9W4IRbX6lYwPXAAAkaXzgxMnTtjBgweD1u/bt4/Jo88T190AkL5pZPX69ev919+an0zzivz888+uc5Em7daoXdUjV/nTuIludURSia+41OlMc5l51FFN18wqqdmqVSs3UkxLYClVz/79+10JF40S9uZI03X6sGHDXJk2lVnV8XgUp1SmVaPHM4LMSX2AJpbRLK1qGVeLiHoAqtegehOqti0AIHrpouyDDz5I8dZwBV71PFbLtRdwN2/e7G4TqqWuYK2JRXVSIWr9DmwND6SWcD0vF5gpE7uPHDnivmeP6uUpCa7eCmocUUkejVrQ8+p78obza7saV3TypdnbNUmN6trrfs+ePe3ee+91J2kAgOiheVSyZMni4rQuzEXxRQ3itWrVivThRTWuuwEgfatYsaJVq1bNTeqp8ikaTa1a5bo20rW2yle2bNnSnn766aDHaVTup59+am+++WaCz/vnn3+66+HA0eIapa3ymFo8Ks0StzPaY489Zo8//nhQiVeVXlU8Gj58uD355JOutKrnww8/tI4dO1pGEeNT18Ek8orJaxi9Lqb1pSvIN2zY0KKRJidTfVX9ItKzDalmfkykjwDJUS/JfzKjyrp169zfdLVAK6Gp1nD1JlOgvPDCC11LuQKnEqI//PBD0GPVA129j5cuXZpga/hnn33mLrJFF4Pec6pVW/e7dOni/gbPmjUr6LHe8DAF6CpVqrh1TZo0cT3S9Zhbb73Vhg4d6nqmi04EVOtNx6366dEsnPEpXLFbn68S4HHpxKpfv35uotiEqAeEyvYowf7oo4+6eq46OdT++t3RSAP1Nk8sYjcigtgdndJ57D4fgQ2jirWKp/obrzivyUHVS00Jcc15ol5smhT6yiuvdL3MvZ7mnTt3ducLusjW32OVXJPFixdbRpQWY3daQexGRBC7o1MGid3qba7ra/U+18jcaLJ+/Xq76aabXGc4/W2PZomNT8lKoqc3BHNEBME8OmWAYK5hYQ8++KBrDVfi89lnn3W12uK2hgdOIqa/n+qFrNbwhFqilVzVxXVgzWtdkOtCW0lzlWVp3Lixm7xEF+5xa2Grt/lrr73mX6cL/nvuucf1yNKEW0rSeycdOu6YmBgbPXq0RTviU2h8NogIYnd0ygCxO7nO1jCquK3lgQceiLddc5QotnuN3eq1psZunStoePrbb7+dYcu5EJ9C47NBRBC7o1MGit3qVKbRXLrWjiZz5sxxI48V96MdSfQkIJgjIgjm0SkDBPNobg3X0DX1kFu5cmXIHtHRhPgUGp8NIoLYHZ0yQOxG2pEW49PChQvt1VdftVWrVrk5bTQ6MHASeKUE1DCiMgKqb6+ye5r/5oorrvDvo1EJ6vygydx1fqiEjzpPaFRhNH82yACI3dGJ2I1UlNj4lKia6BrSr159iZFQYXoAQPTQcG2VZVENzmhrDVdPd/V+Sw8J9PNF7AYAILqkVOw+evSoVa5c2Y001IjCuIYMGeJq3apurs6hnn/+edezUCMRs2fP7vbRyD8l4GfPnu1GCGqEQqdOnWzKlClJeIcAAESvzIkdWgAAyDh69Ohh0ahGjRpuAbEbAIBok1KxWyXztCREvdD1us8995w1b97crdPkpYULF7bp06db69at3eTuM2fOtBUrVvjPs0aMGOHmqFG5vcAJ6AAAyNBJdNXEAwAA0YPYDQBAdIlE7N62bZvt3bvXGjRo4F+nIe2aI2fJkiUuia5bzYUT2FFB+6usy7Jly+z2229P9eMGACBNJtHjUuF41VFTi7SUL1/etVpnzpyspwMAACmM2A0AQHRJjditBLqo53kg3fe26bZQoUJB23UMmgze2ychmmhWS2DNWQAAolWSo+/69evttttuc8FSk7fJK6+8YhdffLGbZOSqq65KieMEAADJROwGACC6pIfYPWjQIOvfv3+kDwMAgLDIlNQHPPTQQ1ahQgX7/fffbfXq1W7ZtWuXVapUyU0sAgAA0hZiNwCkb3/99ZfrKawJtqONam1XqVLFzpw5E+lDyZCxu0iRIu523759Qet139um2/379wdtP3XqlJvc1NsnIX369LFDhw75Fx0/AADRKsk90desWWMrV650M4d79PNLL71kV199dbiPDwAAnCdiNwCkb/p7rjIfJUuWdAn1tm3b2k8//eRPrmvbyy+/bHny5HH7z58/32688cZ4z7Nnz56zJkU1CeXrr79uY8aMsR07dljBggXt0UcftWeffdZt//HHH+3BBx+0LVu2uOefOHGiK/nhJV1VZ3vUqFF2zTXX+J/zlltuseeff94mT55s9913Xwp8OtEptWJ3qVKl3Hc+d+5c15jhlV1RrfPOnTu7+7Vq1bKDBw/aqlWrrHr16m7dd9995xo+9J2Gki1bNrcAQFpXcWLFSB8CkmFd+3WWpnuilylTJl4rtahlunTp0uE6LgAAECbEbgBIv/755x8bN26cdejQwd3XZI9Kmn/xxRe2efNmmzBhgs2ZM8ceeeSReI/dtGmTS5x7S9y613E99thj9u6779prr71mv/zyi3uNwIS4ek/fdNNNrte0eh4rce9R8v36668P2t9z//332/Dhw8/zk0hfwhm7jxw54pLyWrzJRPXzzp07LSYmxnr06GEDBw503+e6deusXbt2Fhsbay1atHD7lytXzjV2dOzY0ZYvX26LFi2yrl27uklHtR8AABlB5uTUNevevbv169fPrr32Wrdu6dKlNmDAAFejLXCyEK+nAwAg6WgNj16p3SJ+LsRuAEi/vv76a9fb1/v7rt7KXg9iKVGihOst/uqrr8Z7rJLm+fLlS9TraHJL9SL/+eef/TW61Ys57j7qUa4EcJs2bWzGjBlu/W+//eYS/erJnJBmzZq5pOyvv/5ql19+eRLeffoVztitHu2BIw969erlbtu3b+8aWXr37m1Hjx51ZWLU47x27dquzE727Nn9j9H3qu+ofv36rqGmVatWNHwAADKUJCfRb731Vnd71113uVZrb1ifd/Lj3dc2zSYOAAAii9gNAOnX999/7y+xkZDdu3fbtGnTrG7duvG2qXzH8ePH3SSVStaqp3gomszysssuc4lx9UpW3GjQoIENGTLEX7KlcuXKNnv2bNdTWuVBVL9b1Ate++XOnTvB5y5evLgVLlzYvReS6OGP3fXq1fM/NiF6DiXntYSi73jKlCnJei8AAGTIJPq8efNS5kgAAECKIHYDQPql2uQJldRQT/DPP//c/v33X5d0VRkWT9GiRW306NFWo0YNl0TXNiVaVQe7WrVqCb6OepPrtT755BObNGmSS9z27NnT7rjjDlcfW/Q86vWuci9KyGtiyffff99y5szp6ng3atTI9TZXGRCVDwmk96Dnx/8hdgMAEOVJ9IR6MAAAgLSL2A0A6ZeS5IFlNzxvvPGGvfDCC64uupLZKuHx9ttvu20qx+KVZJHrrrvOJbf1GCW9E6JJJJVwVwJd5VpEJVrUC1611fV8FSpUsAULFvgfo4lNdQwLFy60bt26uddRr3gl1DUhpdejWnLkyOHqu+P/ELsBAIjyJLocO3bMzfauSU10MhXotttuC9exAQCAMCF2A0D6VLBgQfv777/jrS9SpIhbypYt60px3HDDDfb888+7XugJ0YSfP/zwQ8jX0eMyZ87sT6B7E06KJqgMTMp7lLjXpJWXXnqpzZ8/3/U+z5UrlzVt2tTdD0yiHzhwwC6++OIkv//0jNgNAEAUJ9E1wYhm6/7zzz/jbaOWKgAAaQ+xGwDSr6pVq9oHH3xw1n28BKx6koeyZs2akAl2UXmWU6dOBU3+qV7u3uSlcakmuiYaHT9+vLuvWHPy5En3s3cbmCzW8+q94P8QuwEASFsyJfUBGoZ355132p49e9zJWOBCIAcAIO0hdgNA+qU64+vXr/f3Rv/6669d4vrnn3+27du321dffeUm9lQSvGTJkm6fYcOGuXrpW7dudfupt7jqmnfp0sX/vG+99ZbVr1/ff1+TiKpe+oMPPmg//vijrVq1yh5++GG7+eabg3qne0nxrl272pgxYyxTpv+75NTrjxw50tauXWuffvpp0CSmS5cutWzZslmtWrVS/POKFsRuAACiPIm+b98+NyxPs6cDAIC0j9gNAOlXxYoVXXL7448/9tcWHzt2rNWuXduVW9Hknyr9MWPGDP9jTpw4YY8//rh7rGpvK7E9Z86coKS5ekCrd7hHyfAvv/zSlY+pU6eOK8mi5586dWq8Y+rfv7/bXqVKFf+64cOHu97ueqzKuLRq1cq/7cMPP7S2bdu6CUjxf4jdAACkLTE+n8+XlAeo54F6DXTo0MHSi8OHD1vevHnt0KFDlidPnkgfDjKK+TGRPgIkR70k/ck8LxUnVky110J4rWu/Lk3FJ2I3ECbE7uiUirE7UtTb/Mknn3S9yr2e39FCyXrVU1+5cqWVKlXKoh2xOzRiNyKC2B2dUjl2c+0dndal8nV3kmuia1ifhpV9//33rudClixZgrZ37949eUcMAABSBLEbANI39fresmWL/fHHH1asWDGLJio58/bbb6eLBHo4EbsBAEhbkpxE11C7WbNmWfbs2d2M6prUxKOfCeYAAKQtxG4ASP9U1zwa1ahRwy0IRuwGACDKk+jPPvusq3H39NNPR91QQQAAMiJiNwAA0YXYDQBA2pLkaKxJaO6++24COQAAUYLYDQBAdCF2AwCQtiQ5Irdv394++uijlDkaAAAQdsRuAACiC7EbAIAoL+dy+vRpGzJkiH377bdWqVKleBOcDB06NJzHBwAAzhOxGwCA6ELsBgAgypPo69ats6pVq7qff/7556BtgZOdAACAtIHYDQBAdCF2AwAQ5Un0efPmpcyRAACAFEHsBgAguhC7AQCI8iS6Z+vWrfbrr79anTp1LEeOHObz+WgRBwAgDSN2A0DKqjixYqQPAcm0rv06S4uI3QAAROnEon/99ZfVr1/fypQpY02aNLE9e/a49R06dLDHH388JY4RAACcB2I3AADRhdgNAECUJ9F79uzpJjXZuXOn5cyZ07/+7rvvtpkzZ4b7+AAAwHkidgMAEF2I3QAApC1JLucya9YsN0P4pZdeGrT+iiuusB07doTz2AAAQBgQuwEAiC7EbgAAorwn+tGjR4Nawj0HDhywbNmyheu4AABAmBC7AQCILsRuAACiPIl+ww032KRJk/z3NanJmTNnbMiQIXbjjTeG+/gAAMB5InYDABBdiN0AAER5ORcFbU1wsnLlSjtx4oT17t3b1q9f71rEFy1alDJHCQAAko3YDQBAdCF2AwAQ5T3Rr7rqKtu8ebPVrl3bmjdv7oaZtWzZ0n788Ue7/PLLU+YoAQBAshG7AQCILsRuAACivCf6sWPHLG/evPbss8/G27Znzx4rWrRosg5k8ODB1qdPH3vsscds2LBh/td6/PHHberUqXb8+HFr1KiRvf3221a4cGH/4zRbeefOnW3evHl24YUXWvv27W3QoEGWOXOS3xoAAOlSSsVuAACQMojdAABEeU/0atWq2Zo1a+Kt//TTT61SpUrJOogVK1bYO++8E+/xPXv2tC+//NI++eQTW7Bgge3evdu1vntOnz5tTZs2dcPbFi9ebBMnTrQJEyZY3759k3UcAACkRykRuwEAQMohdgMAEOVJ9Hr16tm1115rr7zyiruvYWX333+/3XffffbMM88k+QCOHDlibdu2tbFjx9pFF13kX3/o0CEbN26cDR061G666SarXr26jR8/3iXLly5d6vaZNWuWbdiwwT744AOrUqWKNW7c2F588UUbOXKkS6wDAIDwx24AAJCyiN0AAKQtSa55onIq6v390EMP2YwZM9xQMpVRWb58uavbllRdunRxz9egQQMbOHCgf/2qVavs5MmTbr2nbNmyVrx4cVuyZIk7odBtxYoVg8q7qOSLyrto0pWqVasm+JoqDaPFc/jw4SQfNwAA0SLcsRsAAKQsYjcAAGlLsgqHq8e3yqqMGjXK1R5XyZXkBHLVOl+9erUr5xLX3r17LWvWrJYvX76g9UqYa5u3T2AC3dvubQtFNdP79++f5OMFACBahSt2AwCA1EHsBgAgisu5/Prrr1arVi3XGv7tt99a79697bbbbnO36jmeWLt27XKTiE6ePNmyZ89uqUkTmKpcjLfoWAAASK/CFbsBAEDqIHYDAJC2JDmJrtrjpUqVsrVr19rNN9/sSrDMmzfPpk2bZtdcc02in0flWvbv3+8mTFGruhZNHjp8+HD3s3qUq675wYMHgx63b98+K1KkiPtZt7ofd7u3LZRs2bJZnjx5ghYAANKrcMVuAACQOojdAABEeRJdtdlUhiWwzMp1111nP/74o0uIJ1b9+vVt3bp1bsZxb6lRo4abZNT7OUuWLDZ37lz/YzZt2mQ7d+50LfKiWz2HkvGe2bNnu6R4+fLlk/rWAABIl8IVuwEAQOogdgMAEOU10TUbuKiX+LZt2+zyyy93Pcdz585t48aNS/TzaP+49dxy5cplBQoU8K/v0KGD9erVy/Lnz+8S4926dXOJc00qKg0bNnTJch3TkCFDXB305557zk1Wqt7mAAAgfLEbAACkDmI3AABR3hP933//dcntnDlzWoUKFVzPcFGC+5VXXgnrwb3xxht26623WqtWraxOnTquRIuGr3kuuOACVyNOt0qu33vvvdauXTsbMGBAWI8DAIBoFs7YvXDhQmvWrJnFxsZaTEyMTZ8+PWi7z+ezvn37WtGiRS1HjhzWoEED27JlS9A+Bw4ccCPP1ECuHnY6tiNHjoThnQIAkD6k5nU3AABIgST6008/7eqyzZ8/P2hCUF0ka7jZ+dBzDhs2zH9fzz9y5Eh3sX306FGXQI9b67xEiRL29ddf2z///GP//e9/7bXXXnMt9AAAIPyxW/G4cuXKLj4nRCPDNL/J6NGjbdmyZW6UWaNGjezYsWP+fZRAX79+vSvBpsZwJeY7dep0Hu8QAID0JSWvuwEAQNIlOdusHmcfffSRK6miHmgetY5rBnEAAJC2hDN2N27c2C0JUS90NYartFrz5s3dukmTJrnJwnUMrVu3to0bN9rMmTNtxYoVbv4TGTFihDVp0sQ1hKuHOwAAGR3X3QAARHlPdPX2LlSoUII90wKDOwAASBtSK3arZqvmJ1EvOU/evHmtZs2atmTJEndftyrh4iXQRftnypTJ9VwHAABcdwMAEPVJdF30fvXVV/77XgB/9913XV1yAACQtqRW7FYCXdTzPJDue9t0GzcpoDJsmkTc2ychx48ft8OHDwctAACkV1x3AwAQ5eVcXn75ZTeMe8OGDXbq1Cl788033c+LFy+2BQsWpMxRAgCAZEsPsXvQoEHWv3//SB8GAACpIj3EbgAAMnRP9Nq1a9uaNWtcIK9YsaLNmjXL9SjT8Ozq1aunzFECAIBkS63Y7U3+vW/fvqD1uu9t0+3+/fuDtuu4NIl43MnDA/Xp08cOHTrkX3bt2hW24wYAIK3huhsAgCjviS6XX365jR07NvxHAwAAUkRqxO5SpUq5RPjcuXOtSpUqbp3KrqjWeefOnd19DUE/ePCgrVq1yp8E+O677+zMmTOudnoo2bJlcwsAABkF190AAKQdyUqiAwCAjOnIkSO2devWoMlE1VNONc2LFy9uPXr0sIEDB9oVV1zhkurPP/+8xcbGWosWLdz+5cqVs1tuucU6duxoo0ePtpMnT1rXrl2tdevWbj8AAAAAAKK2nEumTJnsggsu8N8CAIC0LSVi98qVK61q1apukV69ermf+/bt6+737t3bunXrZp06dbKrr77aJd1nzpxp2bNn9z/H5MmTrWzZsla/fn1r0qSJG7I+ZsyYsBwfAADRLBLX3adPn3aN3mr8zpEjh+sB/+KLL5rP5/Pvo58V64sWLer2adCggW3ZsiVVjg8AgKjqia6eZgAAIHqkROyuV69e0EV1XDExMTZgwAC3hKJe61OmTAn7sQEAEO0icd39yiuv2KhRo2zixIlWoUIF12D+wAMPWN68ea179+5unyFDhtjw4cPdPt5Is0aNGrnJTgMbygEAsIyeRC9RokTKHgkAAAgrYjcAANElErF78eLF1rx5c2vatKm7X7JkSfvwww9t+fLl7r4az4cNG2bPPfec208mTZpkhQsXtunTp7uSbAAApHeJSqL/9NNPiX7CSpUqnc/xAACAMCB2AwAQXSIVu6+77jpXVm3z5s1WpkwZW7t2rf3www82dOhQf+/4vXv3uhIuHvVS14TgS5YsIYkOAMgQEpVEr1KlihuerRZo3Z6rnhoAAIgsYjcAANElUrH76aeftsOHD7v5SlSHXc/90ksvWdu2bd12JdBFPc8D6b63LSHHjx93i0evAQBAup5YVC3Pv/32m7v99NNPXQ20t99+23788Ue36GdNPqJtAAAg8ojdAABEl0jF7o8//thN+q35SlavXu3qnr/22mvu9nwMGjTI9Vj3lmLFioXtmAEASJM90QPrst15551uQpEmTZoEDSVTQNTkIi1atEiZIwUAAIlG7AYAILpEKnY/+eSTrje6V5alYsWKtmPHDpcEb9++vRUpUsSt37dvnxUtWtT/ON1X7/lQ+vTpY7169QrqiU4iHQCQrnuiB1q3bp1rEY9L6zQzNwAASFuI3QAARJfUjN3//POPZcoUnBpQWZczZ874X1OJ9Llz5wYlxJctW2a1atUK+bzZsmWzPHnyBC0AAGSYJHq5cuVci/SJEyf86/Sz1mkbAABIW4jdAABEl9SM3c2aNXM10L/66ivbvn27ffbZZ25S0dtvv91tV332Hj162MCBA+2LL75wCf527dpZbGwso9kAABlGosq5BBo9erQLspdeeql/RnDNIq7A+uWXX6bEMQIAgPNA7AYAILqkZuweMWKEKxHz6KOP2v79+11y/OGHH7a+ffv69+ndu7cdPXrUOnXqZAcPHrTatWvbzJkzLXv27GE9FgAA0qoYn6b+TiIFT0088ssvv7j7agm/5557LFeuXBaNNBRNE50cOnSIIWZIPfNjIn0ESI56Sf6TmWwVJ1ZMtddCeK1rvy7NxSdiNxAGxO7oROxGIhC7Ux6xGxFB7I5OqRi7hfgdndalcuxOck90UdBWCzQAAIgOxG4AAKILsRsAgCiuiQ4AAAAAAAAAQEZBEh0AAAAAAAAAgBBIogMAAAAAAAAAEAJJdAAAAAAAAAAAwplEP3jwoL377rvWp08fO3DggFu3evVq++OPP5LzdAAAIIURuwEAiC7EbgAA0o7MSX3ATz/9ZA0aNLC8efPa9u3brWPHjpY/f36bNm2a7dy50yZNmpQyRwoAAJKF2A0AQHQhdgMAEOU90Xv16mX333+/bdmyxbJnz+5f36RJE1u4cGG4jw8AAJwnYjcAANGF2A0AQJQn0VesWGEPP/xwvPWXXHKJ7d27N1zHBQAAwoTYDQBAdCF2AwAQ5Un0bNmy2eHDh+Ot37x5s1188cXhOi4AABAmxG4AAKILsRsAgLQlyUn02267zQYMGGAnT55092NiYlxNtqeeespatWqVEscIAADOA7EbAIDoQuwGACDKk+ivv/66HTlyxAoVKmT//vuv1a1b10qXLm25c+e2l156KWWOEgAAJBuxGwCA6ELsBgAgbcmc1AdodvDZs2fbokWLbO3atS6wV6tWzc0cDgAA0h5iNwAA0YXYDQBAFCfRNZQsR44ctmbNGrv++uvdAgAA0i5iNwAA0YXYDQBAlJdzyZIlixUvXtxOnz6dckcEAADChtgNAEB0IXYDAJAOaqI/++yz9swzz9iBAwdS5ogAAEBYEbsBAIguxG4AAKK8Jvpbb71lW7dutdjYWCtRooTlypUraPvq1avDeXwAAOA8EbsBAIguxG4AAKI8id6iRYuUORIAAJAiiN0AAEQXYjcAAFGeRH/hhRdS5kgAAECKIHYDABBdiN0AAER5Et2zcuVK27hxo/u5fPnyVr169XAeFwAACDNiNwAA0YXYDQBAlCbRf//9d2vTpo0tWrTI8uXL59YdPHjQrrvuOps6dapdeumlKXGcAAAgmYjdAABEF2I3AABpS6akPuChhx6ykydPutZwzRSuRT+fOXPGbQMAAGkLsRsAgOhC7AYAIMp7oi9YsMAWL15sV155pX+dfh4xYoTdcMMN4T4+AABwnojdAABEF2I3AABR3hO9WLFirkU8rtOnT1tsbGy4jgsAAIQJsRsAgOhC7AYAIMqT6K+++qp169bNTXDi0c+PPfaYvfbaa+E+PgAAcJ6I3QAARBdiNwAAUVjO5aKLLrKYmBj//aNHj1rNmjUtc+b/e/ipU6fczw8++KC1aNEi5Y4WAAAkCrEbAIDoQuwGACDKk+jDhg1L+SMBAABhQ+wGACC6ELsBAIjyJHr79u1T5MVHjRrllu3bt7v7FSpUsL59+1rjxo3d/WPHjtnjjz9uU6dOtePHj1ujRo3s7bfftsKFC/ufY+fOnda5c2ebN2+eXXjhhe5YBw0a5G+tBwAgI0qp2A0AAFIGsRsAgLQr2Znm/fv3u+XMmTNB6ytVqpTo57j00ktt8ODBdsUVV5jP57OJEyda8+bN7ccff3QJ9Z49e9pXX31ln3zyieXNm9e6du1qLVu2tEWLFvknVWnatKkVKVLEzVy+Z88ea9eunWXJksVefvnl5L41AADSpXDEbgAAkHqI3QAARGkSfdWqVa6FfOPGjS7xHUj125TYTqxmzZoF3X/ppZdcz/SlS5e6BPu4ceNsypQpdtNNN7nt48ePt3Llyrnt1157rc2aNcs2bNhgc+bMcb3Tq1SpYi+++KI99dRT1q9fP8uaNWtS3x4AAOlOOGM3AABIecRuAACiPImuSUzKlCnjEtxKXAdOfHI+dBKgHueaPKVWrVrupOHkyZPWoEED/z5ly5a14sWL25IlS1wSXbcVK1YMKu+iki8q77J+/XqrWrVqWI4NAIBollKxGwAApAxiNwAAUZ5E/+233+zTTz+10qVLh+UA1q1b55Lmqn+umuafffaZlS9f3tasWeN6kufLly9of51A7N271/2s28AEurfd2xaK6qtr8Rw+fDgs7wUAgLQo3LEbAACkLGI3AABpS6akPqB+/fq2du3asB3AlVde6RLmy5Ytcz3INWRNJVpSkiYeVY11bylWrFiKvh4AAJEU7tgNAABSFrEbAIAo74n+7rvvukT3zz//bFdddZWbxDPQbbfdlqTnU29zr3W9evXqtmLFCnvzzTft7rvvthMnTtjBgweDeqPv27fPTSQqul2+fHnQ82m7ty2UPn36WK9evYJ6opNIBwCkV+GO3QAAIGURuwEAiPIkuuqQL1q0yL755pt428IxwYlmHVepFSXUdaIwd+5ca9Wqldu2adMm27lzpyv/IrrVZKSarbxQoUJu3ezZsy1PnjyuJEwo2bJlcwsAABlBSsduAAAQXsRuAACivJxLt27d7N5777U9e/a4hHfgktRArh7hCxcutO3bt7va6Lo/f/58a9u2rSuz0qFDB9djfN68eW6i0QceeMAlzjWpqDRs2NAly++77z431O3bb7+15557zrp06UKSHACAFIjdiVGyZEl3gR93UXyWevXqxdv2yCOPhP04AACIVqkduwEAQJh7ov/111/Ws2fPeBN6Jod6kLdr186dGChpXqlSJZcIv/nmm932N954wzJlyuR6oqt3eqNGjeztt9/2P/6CCy6wGTNmuFrqSq7nypXLDXkbMGDAeR8bAADpRThjd2KoNFvgBb6Goiu233nnnf51HTt2DIrXOXPmTJVjAwAgGqR27AYAAGFOords2dL1DL/88svtfI0bN+6s27Nnz24jR450SyglSpSwr7/++ryPBQCA9CqcsTsxLr744qD7gwcPdq9dt27doKT52eYvAQAgI0vt2A0AAMKcRC9Tpowru/LDDz9YxYoV401w0r1796Q+JQAASEGRjN2aJPyDDz5w5dlUtsUzefJkt16J9GbNmtnzzz9/1t7oGpGmJXBScAAA0iuuuwEAiPIkumYJv/DCC23BggVuCaSLY4I5AABpSyRj9/Tp0+3gwYN2//33+9fdc889biRZbGys/fTTT/bUU0+5ycOnTZsW8nkGDRpk/fv3T7HjBAAgLeG6GwCAKE+ib9u2LWWOBAAApIhIxm6VbmvcuLFLmHs6derk/1m964oWLWr169e3X3/9NeSwdfXGU2/2wJ7oxYoVS+GjBwAgMrjuBgAgbcl0Pg/2+XxuAQAA0SE1Y/eOHTtszpw59tBDD511v5o1a7rbrVu3htwnW7ZslidPnqAFAICMIDVi9x9//GH33nuvFShQwHLkyOEauVeuXBl0DH379nUN39reoEED27JlS4oeEwAAUZ9EnzRpkguqCp5aKlWqZO+//374jw4AAIRFJGL3+PHjrVChQta0adOz7rdmzRp3qwtzAACQurH777//tuuvv97VXf/mm29sw4YN9vrrr9tFF13k32fIkCE2fPhwGz16tC1btsxy5cpljRo1smPHjoX9eAAASIuSXM5l6NChbvKvrl27ukArmuzkkUcesT///NN69uyZEscJAACSKRKx+8yZMy6J3r59e8uc+f+fbqhky5QpU6xJkyaut5tqouv169Sp45IDAAAgdWP3K6+84kqkKW57SpUqFdQLfdiwYfbcc89Z8+bN/Qn+woULu7lPWrduHbZjAQAg3STRR4wYYaNGjbJ27dr51912221WoUIF69evH0l0AADSmEjEbpVx2blzpz344INB67Nmzeq26WL86NGj7qK9VatW7sIcAACkfuz+4osvXK/yO++8001ieskll9ijjz5qHTt29Ndn37t3ryvh4smbN68rx7ZkyRKS6ACADCHJSfQ9e/bYddddF2+91mkbAABIWyIRuxs2bJhg/VYlzXWBDgAA0kbs/u2331zCXhN4P/PMM7ZixQrr3r27a/jWiDIl0EU9zwPpvrctIcePH3dL4KTgAABkmJropUuXto8//jje+o8++siuuOKKcB0XAAAIE2I3AADRJTVjt0qwVatWzV5++WWrWrWqderUyfVCV/3z8zFo0CDXY91b1JAOAECG6Ynev39/u/vuu23hwoX+2myLFi2yuXPnJhjkAQBAZBG7AQCILqkZuzWxd/ny5YPWlStXzj799FP3c5EiRdztvn37giYB1/0qVaqEfN4+ffq43u2BPdFJpAMAMkxPdNUt1WzcBQsWdJOIaNHPy5cvt9tvvz1ljhIAACQbsRsAgOiSmrFbSfpNmzYFrdu8ebOVKFHCP8moEulK4AcmxHV8tWrVCvm82bJlszx58gQtAABkmJ7oUr16dfvggw/CfzQAACBFELsBAIguqRW7NUmpaq2rnMtdd93lEvVjxoxxi8TExFiPHj1s4MCBrpSMkurPP/+8xcbGWosWLVL8+AAAiNokOgAAAAAAiH5XX321ffbZZ678yoABA1ySfNiwYda2bVv/Pr1797ajR4+6eukHDx602rVr28yZMy179uwRPXYAANJcEj1TpkyuBfpstP3UqVPhOC4AAHCeiN0AAESXSMXuW2+91S1ne00l2LUAAJARJTqJrpbpUJYsWWLDhw93s3oDAIC0gdgNAEB0IXYDABDlSfTmzZvHW6fJR55++mn78ssv3VAvWqUBAEg7iN0AAEQXYjcAAGlTpuQ8aPfu3daxY0erWLGiG0a2Zs0amzhxon/2bgAAkLYQuwEAiC7EbgAAojSJfujQIXvqqaesdOnStn79eps7d65rDb/qqqtS7ggBAECyEbsBAIguxG4AAKK4nMuQIUPslVdesSJFitiHH36Y4DAzAACQdhC7AQCILsRuAACiPImuGmw5cuRwreEaQqYlIdOmTQvn8QEAgGQidgMAEF2I3QAARHkSvV27dhYTE5OyRwMAAMKG2A0AQHQhdgMAEOVJ9AkTJqTskQAAgLAidgMAEF2I3QAApIOJRQEAAAAAAAAAyEhIogMAAAAAAAAAEAJJdAAAAAAAAAAAQiCJDgAAAAAAAABACCTRAQAAAAAAAAAIgSQ6AAAAAAAAAAAhkEQHAAAAAAAAACAEkugAAAAAAAAAAIRAEh0AAAAAAAAAgBBIogMAAAAAAAAAEAJJdAAAAAAAAAAAQiCJDgAAAAAAAABACCTRAQAAAAAAAAAIgSQ6AAAAAAAAAAAhkEQHAAAAAAAAACAEkugAAAAAAAAAAIRAEh0AAAAAAAAAgBBIogMAAAAAAAAAEAJJdAAAAAAAAAAAQiCJDgAAAAAAAABACCTRAQAAAAAAAAAIgSQ6AAAAAAAAAAAhkEQHAAAAAAAAACAEkugAAAAAAAAAAKTFJPqgQYPs6quvtty5c1uhQoWsRYsWtmnTpqB9jh07Zl26dLECBQrYhRdeaK1atbJ9+/YF7bNz505r2rSp5cyZ0z3Pk08+aadOnUrldwMAAAAAAAAASG8imkRfsGCBS5AvXbrUZs+ebSdPnrSGDRva0aNH/fv07NnTvvzyS/vkk0/c/rt377aWLVv6t58+fdol0E+cOGGLFy+2iRMn2oQJE6xv374RelcAAAAAAAAAgPQicyRffObMmUH3lfxWT/JVq1ZZnTp17NChQzZu3DibMmWK3XTTTW6f8ePHW7ly5Vzi/dprr7VZs2bZhg0bbM6cOVa4cGGrUqWKvfjii/bUU09Zv379LGvWrBF6dwAAAAAAAACAaJemaqIraS758+d3t0qmq3d6gwYN/PuULVvWihcvbkuWLHH3dVuxYkWXQPc0atTIDh8+bOvXr0/wdY4fP+62By4AAAAAAAAAAKTZJPqZM2esR48edv3119tVV13l1u3du9f1JM+XL1/QvkqYa5u3T2AC3dvubQtViz1v3rz+pVixYin0rgAAAAAAAAAA0SzNJNFVG/3nn3+2qVOnpvhr9enTx/V695Zdu3al+GsCAJBRqJxaTExM0KKRZEmZNBwAAAAAgLQiTSTRu3btajNmzLB58+bZpZde6l9fpEgRN2HowYMHg/bXhba2efvEvfD27nv7xJUtWzbLkydP0AIAAMKnQoUKtmfPHv/yww8/JHrScAAAAAAA0pKIJtF9Pp9LoH/22Wf23XffWalSpYK2V69e3bJkyWJz5871r9u0aZPt3LnTatWq5e7rdt26dbZ//37/PrNnz3aJ8fLly6fiuwEAAJ7MmTO7xmxvKViwoFvvTRo+dOhQN2m4Yr0mDV+8eLGbNBwAAAAAgLQmokl0DeX+4IMPbMqUKZY7d25Xw1zLv//+67arXnmHDh2sV69erpe6Jhp94IEHXOL82muvdfs0bNjQJcvvu+8+W7t2rX377bf23HPPuedWj3MAAJD6tmzZYrGxsXbZZZdZ27ZtXQN4YicNTwiTggMAAAAAMmQSfdSoUa5HWr169axo0aL+5aOPPvLv88Ybb9itt97q6qXWqVPH9WabNm2af/sFF1zgSsHoVsn1e++919q1a2cDBgyI0LsCACBjq1mzpk2YMMFmzpzpYv22bdvshhtusP/973+JmjQ8IUwKDgBA6hg8eLCbz6RHjx7+dcxnAgDI6DJHupzLuWTPnt1GjhzpllBKlChhX3/9dZiPDgAAJEfjxo39P1eqVMkl1RWrP/74Y8uRI0eyJwXXyDSPeqKTSAcAILxWrFhh77zzjovfgTSfyVdffeXmM1Fjtsqyaj6TRYsWRexYAQDIcBOLAgCA9Eu9zsuUKWNbt25N1KThCWFScAAAUtaRI0dcCbaxY8faRRdd5F/PfCYAAJBEBwAAqXBR/uuvv7qSbYmZNBwAAKQ+lWtp2rRp0Lwl5zOfCQAA6UlEy7kAAID054knnrBmzZq5Ei67d++2F154wc1d0qZNm6BJw/Pnz+96lHfr1i1o0nAAAJC6pk6daqtXr3blXOJK7nwmmhRci4dJwQEA0YwkOgAACKvff//dJcz/+usvu/jii6127dpuuLd+9iYNz5Qpk5uUTBfXjRo1srfffjvShw0AQIa0a9cue+yxx2z27NluTrJw0aTg/fv3D9vzAQAQSSTRAQBA2Huzne+k4QAAIHWoXMv+/futWrVq/nWnT5+2hQsX2ltvvWXffvutfz6TwN7o55rPhEnBAQDpCUl0AAAAAAAyqPr169u6deuC1j3wwAOu7vlTTz3lEt/efCYaRZbY+Uw0KbgWAADSA5LoAAAAAABkULlz57arrroqaF2uXLmsQIEC/vXMZwIAyOhIogMAAAAAgJCYzwQAkNGRRAcAAAAAAH7z588Pus98JgCAjC5TpA8AAAAAAAAAAIC0iiQ6AAAAAAAAAAAhkEQHAAAAAAAAACAEkugAAAAAAAAAAIRAEh0AAAAAAAAAgBBIogMAAAAAAAAAEAJJdAAAAAAAAAAAQiCJDgAAAAAAAABACCTRAQAAAAAAAAAIgSQ6AAAAAAAAAAAhkEQHAAAAAAAAACAEkugAAAAAAAAAAIRAEh0AAAAAAAAAgBBIogMAAAAAAAAAEAJJdAAAAAAAAAAAQiCJDgAAAAAAAABACCTRAQAAAAAAAAAIgSQ6AAAAAAAAAAAhkEQHAAAAAAAAACAEkugAAAAAAAAAAIRAEh0AAAAAAAAAgBBIogMAAAAAAAAAEAJJdAAAAAAAAAAAQiCJDgAAAAAAAABACCTRAQAAAAAAAAAIgSQ6AAAAAAAAAAAhkEQHAAAAAAAAACAEkugAAAAAAAAAAIRAEh0AAAAAAAAAgBBIogMAAAAAAAAAEAJJdAAAAAAAgFS0cOFCa9asmcXGxlpMTIxNnz7dv+3kyZP21FNPWcWKFS1Xrlxun3bt2tnu3bsjeswAkJGRRAcAAAAAAEhFR48etcqVK9vIkSPjbfvnn39s9erV9vzzz7vbadOm2aZNm+y2226LyLECAMwyR/oAAAAAAAAAMpLGjRu7JSF58+a12bNnB61766237JprrrGdO3da8eLFU+koAQAeeqIDAAAAAACkYYcOHXJlX/LlyxfpQwGADIkkOgAAQDquqyr9+vWzsmXLurqqF110kTVo0MCWLVsWseMFAACJd+zYMVcjvU2bNpYnT55IHw4AZEgk0QEAANJxXVUpU6aMGwa+bt06++GHH6xkyZLWsGFD++9//5vqxwoAABJPk4zedddd5vP5bNSoUZE+HADIsKiJDgAAkI7rqso999wTdH/o0KE2btw4++mnn6x+/fqpcIQAACC5CfQdO3bYd999Ry90AMioPdHPNfRYLa19+/a1okWLWo4cOdzQ4y1btgTtc+DAAWvbtq0LJqoN1qFDBzty5EgqvxMAAIDocOLECRszZoybtEy91wEAQNpNoCsHMmfOHCtQoECkDwkAMrSIJtHPNfR4yJAhNnz4cBs9erSr26k6no0aNXL1wDxKoK9fv97NXD1jxgyXmO/UqVMqvgsAAIC0T+dJF154oWXPnt3eeOMNd+5UsGDBSB8WAAAZkjr/rVmzxi2ybds29/POnTtdAv2OO+6wlStX2uTJk+306dO2d+9et6gxHACQwZLoGnY8cOBAu/322+NtUy/0YcOG2XPPPWfNmze3SpUq2aRJk2z37t3+HusbN260mTNn2rvvvms1a9a02rVr24gRI2zq1KluPwAAkLoGDRpkV199teXOndsKFSpkLVq0sE2bNgXtU69ePTcCLXB55JFHInbMGcWNN97oLs4XL15st9xyi+vdtn///kgfFgAAGZIS5FWrVnWL9OrVy/2s0fh//PGHffHFF/b7779blSpV3Oh8b1EcBwCkvjQ7sahaYdXKqhIuHg07VrJ8yZIl7r5uVcKlRo0a/n20f6ZMmVzP9VCOHz9uhw8fDloAAMD5W7BggXXp0sWWLl3qejqrJ5UmsNTos0AdO3a0PXv2+BeNPkPK0oi+0qVL27XXXuvqoWfOnNndAgCA1KdOBeo8GHeZMGGCmwA8oW1a9DgAQOpLs0l0JdClcOHCQet139umW/VyC6QLwvz58/v3CdVLTgl5bylWrFiKvIdopNm+1etfNea11KpVy7755pugfdR4cdNNN7mLce1Tp04d+/fffyN2zACAtEMjxO6//36rUKGCK9mmC0ENS161alXQfjlz5rQiRYr4FybKSn1nzpxxHQsAAEjMSDKVVVVDuWpzqzxYq1atbN++fRE7ZgAAUlOaTaKnpD59+tihQ4f8y65duyJ9SGnGpZdeaoMHD3bJDg0vU7Jc5XRUd95LoGsIuHoVLl++3FasWGFdu3Z1vf8BAIhLcVbUwB1I9T1Vj/uqq65ycfmff/6J0BGm/7qqGgXwzDPPuNEBO3bscDH+wQcfdEPF77zzzkgfOgAgSkaS9ezZ07788kv75JNP3P4qodqyZcuIHjcAAKkls6VR6pUmatlW3S+P7qsmmLdP3Fqep06dsgMHDvgfn5Bs2bK5BfE1a9Ys6P5LL73keqfrZEq9CnXi1L17d3v66af9+1x55ZUROFIAQDT0dO7Ro4ddf/31Llnuueeee6xEiRIWGxtrP/30kz311FOut9u0adNCPpd6TAf2mqYUWzA1fKvmuUd1VaV9+/ZugvZffvnFJk6caH/++afrQajeht9//72L7QAAaCRZII0kU490Nbxq5LEaxVUCbMqUKa6jlYwfP97KlSvnrhVVKgwAgPQszSbRS5Uq5RLhc+fO9SfNdcGsWuedO3d291Vq5ODBgy6wV69e3a377rvv3EW7aqfj/GgGcPUyUO8DfdZqsNDn37ZtW7vuuuvs119/tbJly7pEuyZ1BQAgkHq0/fzzz/bDDz8Ere/UqZP/54oVK7rG8vr167u4cvnll4ccZt6/f/8UP+Zor6saytkaKAAAONdIMl1zq3d64JxluhYsXry4G61MEh0AkN5lSqtDj2NiYlzvtYEDB7pZqdetW2ft2rVzvdZUn03U6q3SIpqcTKVFFi1a5EqLtG7d2u2H5NFnrRp36q3/yCOP2GeffWbly5e33377zW3v16+f+8zVW6FatWou8bFly5ZIHzYAIA1RPJ4xY4bNmzfPlQo7G6/he+vWrSH3oRQbAACRG0mmOceyZs1q+fLlCzlnWVwaQaaOcIELAADRKlOkhx5XrVrVLd7QY/3ct29fd793797WrVs312NNw46VdFfiNnv27EE1VdUCrkRukyZNXI/oMWPGROw9pQcqz6LGDK/Xv4aCb9iwwZ1MycMPP2wPPPCA+67eeOMNt/97770X6cMGAKQB6g2tBLoaYDU6TCPLzsVrTA8s3xaXGna9Sa+9BQAApNxIsqlTp57X82gUWd68ef1LsWLFLD3666+/XOmb7du3W1qia3h1ZAisaw8AiNIkujf0OO6i+mui3ugDBgxwLduaCXzOnDlWpkyZoOfQ8DLVZfvf//7neqYpmate1Eg+9TAoXbq0K5GjE5/KlSvbm2++6U9uqFd6II0I0OgBAAB04f3BBx+42Jw7d24Xw7X8+++/brtKtrz44otuWLguNjXaTCPNVG+1UqVKkT58AAAytFAjyVRq9cSJE66caiDNWRZqPrKMMopM5U2bN29uJUuWjJdc12eovEbczy0uPVb7BS6DBw/2b9c5k86VcuXK5W7jJuxvvfVW+/TTT4PW6bpdZXaGDh0alvcJABldRJPoiA7qga6heArsKpOjyd8Cbd682U0QBwCAJqPWhbIaytX46i0fffSRv6FWjeINGzZ0I8kef/xxa9WqlX355ZeRPnQAADKsc40kUwerLFmyuDnLPLouVGcqzZ+VUUeR/fPPP27C1Q4dOsTbpnVJ6SCgDoR79uzxLxqV79H50iWXXOJG7+m86oknnvBv0zlWpkyZ3PlUXBpBrnOzU6dOJev9AQCiYGJRRIZ6CzRu3NhNEKPe/epJOH/+fPv2229da/iTTz5pL7zwguudrglfJ06caL/88ov95z//ifShAwDSgLNNbikayr1gwYJUO55oo15rGuGluV7i9miL9JBwNXwoYaJecACA9DeSTNd+n3/+uX8kmagMS44cOdytksIqwarR4EqIK8mrBHpGnlT066+/do0FcT8DJa7V+1ylar/55ptEPZc+91C9+jdu3Oh6lF9xxRV2//33+5Poeo3nnnvONXwk5Oabb7YDBw64cy+VwAUAJB890RFk//79bli96pwryK5YscIl0BV8RRPMKNHes2dPl0hXT4TZs2fb5ZdfHulDBwDAMvqQcA3vVpJDPQiV9FB8VuO3huAH7sOQcABAUkaSiebDUoxQj2fFDyV8p02bZhnZ999/73rpx214Vq/ySZMmuR7iiaXyLQUKFHBzj7366qtBvcd17a2RfBolPmvWLH8Pd3VyUwNIqHrzGgGozm86TgDA+SGJjiAaiqaLaZVvUUJdgdpLoHuefvppV89OE5QsXrzYTeYKAAAiPyRco8N0gf3OO+/Y+vXrXcJj9OjR9swzz/j3YUg4ACCuhOYq06Jez57s2bPbyJEjXc9mXQsqgR6q53RGsWPHDlfy1KPr6DZt2rgkuEZ3J1b37t3dRK6qRf/www/byy+/bL179/Zvf+2111yMVyP7li1b3P2FCxe6WK5OcHfddZdddtll9sgjjwQ1nIuOT8cJADg/lHMBAABIJ0PCb7nlFrd4dEGtEix6Dl1wC0PCAQAID02crsYFj0Ztqyzbvffem6TnUZkcjxrN1YNcyfRBgwa5cwM1fmvC18BkfaNGjVx51YEDB7pSMIr3OgdQQ3pgPXWNTFNDPQDg/NATHQAAIJ0NCQ+k4fmqX+thSDgAAOFRsGBB+/vvv/331RD9ySefWObMmd3iNTprP5VXS6yaNWu6kV9xS6551FNdc5XovEFzmGkEmSZ+bdmypbsfSA3gF198cbLfIwDg/5BEBwAASEdDwgNt3brVRowY4XqzeRgSDgBAeKh+uRq8PZpTZO3atS6mann33XfdejVAq6E6sfRYNZ4XKlQo3jaNKNMksC+++KK7f/r0aTt58qT7Wbe6H+jnn392xwkAOD+UcwEAAEhHQ8I9f/zxhxvWfeedd1rHjh396xkSDgBAeCh+Kl6rN/pFF13kJvQO9Oeff7pbxfN8+fK5n5cvX+4arefOneti8pIlS2zZsmV24403uhis+z179nTxX88ZSHXqO3Xq5OY80QThcv3119vYsWOtTJkybuSaGuA96smu84EGDRqkwqcBAOkbPdEBAADS2ZDw3bt3u4vx6667zsaMGXPWfRkSDgBA8lSsWNGqVatmH3/8caIfo8ZoNVZ7vcdV81yTitatW9cqVKhgL730kkuiJxS/ta5w4cJ26623+tf169fPjh075krAlC5dOqjH+4cffuhifIkSJc77vQJARkdP9HTsr7/+ci3eaunWkO20QsPdFMh14uC1ngMAkNFpqPUHH3wQNCRcvdM9K1assAcffNANCY/b0y2Qepwpga6k+Pjx489aS90bEq5h44kdEn7HHXec1/sEACA90cTfmldEo77ixtx69eq53uNnW6ck/NKlSxP1WirPFliiTVTyRXOdxKWSbKNHj3ZxHgBw/uiJno6pBbt58+Yuga6EuoZlq5apWro1aVjXrl3t8OHDZ30O9Thr27at5cmTxw0/69Chgx05ciRoeFidOnVcMly3cSc+UQu5kgCBypcvb9dee60NHTo0zO8YAIDoHhK+fv16f290Jcqvuuoq/1KqVCm3Xg3kXo1UNZSXLVvWJc5Ft7o4Vw111Tr/73//a3v37nVLXGcbEq7kuoaE676HIeEAAMTXtGlTF0+9WJxW7Ny505555pmgWA4ASD6S6OmUhoiNGzfOJb1FLeJKqH/xxRe2efNmmzBhgmut1qRhZ6MEui7oZ8+e7eqnauIxnSB4Hn/8cVfHTT3YihYtak888YR/20cffeReV8PC43rggQds1KhRbsZxAAAQniHhiteaTFR1Vi+99FIXm70lLoaEAwAQHj169HAd1dISxfG4vdYBAMkX44s7tigDUm/svHnz2qFDh1yP6/TgP//5jz366KO2f//+kPsMHz7cXn31Vdu1a1eC29ULTb3GNXy8Ro0abt3MmTOtSZMm9vvvv7te7dquHuXq5f7NN9+4JLqS7gcPHrSrr77a1XNN6GRCQ8v0WX/11Vf+Gq8ZzvyYSB8BkqNe6v3JrDixYqq9FsJrXft1YXme9BifwiW9fjaKixoSrrIpZyvDktoUt6+44go3JDxD92gjdkcnYjcSgdid8vhsEBHE7uiUirFbiN/RaV0qx+60c3WGsFK9VNVCPduEY9OmTXOTl4SiWcFVwsVLoIuGcOuiXrOHS+XKlV2P9jNnztisWbOsUqVKbr0SAOq9Fqo1PmvWrFalShV3nAAA4P8wJBwAAAAA0h6S6OnUjh07XE/xuNq0aWM5c+Z0JVjUuvLuu++GfA7VT/VqrnoyZ85s+fPn99dWVb3VX375xdVd37Jli7uvki8q79KuXTu766677LLLLnNlY9SLLZCOT8cJAAD+P4aEAwAAAEDaQhI9nfr3338te/bs8dZr8rDVq1fb559/br/++qv16tXrvF5HyXjVSlcPNd0WLFjQlZHRLOADBw603Llzu1qtSrC/8847QY/NkSOHq+UKAAAAAAAAAGkVSfR0Ssnsv//+O976IkWKWNmyZe22225zSW1N7rlnz54En0P7xq2prolADxw44LYl5OWXX3aTjqmUzPz5892kolmyZLGWLVu6+4H0PBdffPF5vU8AAAAAAAAASEkk0dOpqlWr2oYNG866j+qYy/HjxxPcXqtWLTdB6KpVq/zrNFGoHlezZs0EJyLVhGMvvviiu3/69Gk7efKk+1m3uh9Ik6bpOAEAAAAAAAAgrSKJnk41atTI1q9f7++N/vXXX9v48eNd4nr79u321VdfuTrlmiBM9cxl+fLlrpe6N5lZuXLl7JZbbrGOHTu6bYsWLbKuXbta69at49Vb9/l8biI0lYvJlSuXW6fnHjt2rEuuT5o0KWgyMh2DXkcTlQIAAAAAAABAWpU50geAlFGxYkWrVq2affzxx24iMNUfV0K7Z8+erue5JixTiZWnn37a/xjVJ1f9cq/3uEyePNklzuvXr2+ZMmVy5VmGDx8e7/XGjBljhQsXtltvvdW/rl+/fnbPPfe4XutKxnfp0sW/7cMPP3RlX0qUKJGinwMAAAAAIP3ZWLZcpA8ByVDul42RPgQASBaS6OlY37597cknn3Q9yW+88UZbvHjxWfevV6+e61EeKH/+/K5Ey7koUa8lUKFChWzOnDnx9j1x4oSbeDQxzwsAAAAAAAAAkUQSPR1r2rSpbdmyxZVNUc/ztGLnzp32zDPPBJV3AQAAAAAAAIC0iCR6OtejRw9La0qXLu0WAADSCoaERyeGhAMAAABIDUwsCgAAAAAAAABACCTRAQAAAAAAAAAIgSQ6AAAAAAAAAAAhkEQHAAAAAAAAACAEkugAAAAAAAAAAIRAEh0AAAAAAAAAgBAyh9qA5NtYtlykDwHJUO6XjZE+BAAAAAAAAABpDD3RAQAAAAAAAAAIgSQ6AAAAAAAAAAAhkEQHAAAAAAAAACAEkugAAAAAAAAAAIRAEh0AAAAAAAAAgBBIogMAAAAAAAAAEAJJdAAAAAAAAAAAQiCJDgAAAAAAAABACCTRAQAAAAAAAAAIgSQ6AAAAAAAAAAAhkEQHAAAAAAAAACAEkugAAAAAAAAAAIRAEh0AAAAAAAAAgBBIogMAAAAAAAAAEAJJdAAAAAAAAAAA0nsSfeTIkVayZEnLnj271axZ05YvXx7pQwIAAGdB7AYAILoQuwEAGVW6SKJ/9NFH1qtXL3vhhRds9erVVrlyZWvUqJHt378/0ocGAAASQOwGACC6ELsBABlZukiiDx061Dp27GgPPPCAlS9f3kaPHm05c+a09957L9KHBgAAEkDsBgAguhC7AQAZWdQn0U+cOGGrVq2yBg0a+NdlypTJ3V+yZElEjw0AAMRH7AYAILoQuwEAGV1mi3J//vmnnT592goXLhy0Xvd/+eWXBB9z/Phxt3gOHTrkbg8fPhyWYzpy+nRYngepK1zff6IdTd2XQ5ik4u/J6X/5W5LR/554z+Pz+Sw9IXYjXIjdSBRiNxKB2H12xG6EC7EbiZLKvyfE7+h0OJVjd9Qn0ZNj0KBB1r9//3jrixUrFpHjQRqRN2+kjwBRgd8TnFvezuH9Pfnf//5neTP43yhiNxKUwf9fILH4PcG5EbvDj9iNBGXw/xdILH5PkPZid9Qn0QsWLGgXXHCB7du3L2i97hcpUiTBx/Tp08dNiOI5c+aMHThwwAoUKGAxMTEpfszRSi0zOuHZtWuX5cmTJ9KHgzSK3xMkBr8niaOWcAXy2NhYS0+I3amH/2tIDH5PkBj8niQOsfv/I3YnD//XkBj8niAx+D0Jb+yO+iR61qxZrXr16jZ37lxr0aKFPzjrfteuXRN8TLZs2dwSKF++fKlyvOmB/uPxnw/nwu8JEoPfk3NLj73YiN2pj/9rSAx+T5AY/J6cG7H7/xC7zw//15AY/J4gMfg9CU/sjvokuqh1u3379lajRg275pprbNiwYXb06FE3azgAAEh7iN0AAEQXYjcAICNLF0n0u+++2/773/9a3759be/evValShWbOXNmvElPAABA2kDsBgAguhC7AQAZWbpIoouGkIUaRobw0FC8F154Id6QPCAQvydIDH5PIMTulMf/NSQGvydIDH5PIMTulMf/NSQGvydIDH5PwivGp+rpAAAAAAAAAAAgnkzxVwEAAAAAAAAAACGJDgAAAAAAAABACCTRYfPnz7dRo0ZF+jAAAEAiEbsBAIguxG4AiG4k0TO43377ze699167+uqrU+01S5YsacOGDfPf18zuN998s+XKlcvy5cuXascBIO1cUMTExNjBgwfPuh9/O4D/Q+wGEGnEbiBpiN0AIo3Yff5Ioqcz999/v/tPMXjw4KD106dPd+sDHT9+3Fq3bm1jx461GjVqhP1YJkyYkOB/sBUrVlinTp3899944w3bs2ePrVmzxjZv3hz240B4fl/Ox/bt293z6TtG9P++aMmaNauVLl3aBgwYYKdOnTqv573uuuvc34C8efO6+/ztQEZD7EZSELuRFMRuIGUQu5EUxG4kBbE77SKJng5lz57dXnnlFfv777/Pul+2bNls+fLl1rhxY0tNF198seXMmdN//9dff7Xq1avbFVdcYYUKFUrVY0Hif19Sw4kTJyJ9CDiHW265xQXQLVu22OOPP279+vWzV1999byeUycGRYoUOecJJH87kJ4Ru5EUxG4kBbEbSBnEbiQFsRtJQexOm0iip0MNGjRw/zEGDRoUcp8ffvjBbrjhBsuRI4cVK1bMunfvbkePHvVv13/Wpk2buu2lSpWyKVOmxBvSMXToUKtYsaIb0qHnePTRR+3IkSP+YSIPPPCAHTp0yN+Cpv/0Evg8+vnTTz+1SZMmuX3U4obo+33Rd6dW9EBq0VTLpuh3SKpWrer2rVevnruv77tFixb20ksvWWxsrF155ZVu/a5du+yuu+5yz5E/f35r3ry5a1VH5OkiQL8vJUqUsM6dO7vfny+++MKdDLZr184uuugiF3B1kaCA79mxY4c1a9bMbdffjAoVKtjXX38db1gZfzuQURG7kRTEbiQFsRtIGcRuJAWxG0lB7E6bSKKnQxdccIG9/PLLNmLECPv999/jbVcrklq1WrVqZT/99JN99NFH7o91165d/fvoP+Xu3bvdfyz9hxkzZozt378/6HkyZcpkw4cPt/Xr19vEiRPtu+++s969e/uHieg/XZ48edyJgZYnnngiwWEiOhb94dY+b775Zop8JkjZ35dzUc8LmTNnjvuep02b5t82d+5c27Rpk82ePdtmzJhhJ0+etEaNGlnu3Lnt+++/t0WLFtmFF17ojoEW87RHJ3j6XhRMV65c6QL7kiVLzOfzWZMmTdz3KV26dHFDWRcuXGjr1q1zvTD0vcbF3w5kVMRuJAWxG+eD2A2EB7EbSUHsxvkgdqcNmSN9AEgZt99+u1WpUsVeeOEFGzduXNA2tXy2bdvWevTo4e5rSIaCct26dd1s4Wp51B9d/Wfxara9++67br9A3uO91qmBAwfaI488Ym+//bYbJqI6S2qpUuvZ2YaJqIVNfxDOth/S7u+LhqWdi75nKVCgQLzvWa2j+v3S74x88MEHdubMGbfOG2Y0fvx41zquk8uGDRuG7X0j+RSsdSL27bffutZv9YjQiZeCsUyePNn1ntD6O++803bu3OlOCNWLRi677LIEn5e/HcjIiN1ICmI3korYDYQfsRtJQexGUhG70xaS6OmYWpxuuummeK1Ja9eudS2b+s8W+B9Tf0C3bdvmJgrInDmzVatWzb9dExloOEggBXz9of/ll1/s8OHDbpKDY8eO2T///BNUPwnp+/elXLly5/W6+uPuBXLv9bZu3epaxAPpd0ut84gs9VpQS7ZauvU7cM8991jLli3d+po1a/r304mbhglu3LjR3ddQRA1DmzVrlhuKpsBeqVKlCL4TIG0idiMpiN1IDGI3kLKI3UgKYjcSg9idNlHOJR2rU6eOG57Tp0+foPWqn/bwww+7mXW9RX9AVUfp8ssvT9Rzq9X81ltvdf8ZNexs1apVNnLkSLeNoT8Z7/dFrZcK8IG84UTnohbxuK+nSSsCX8+bBVqBA5F14403uu9D3/+///7rhpQmZkb5hx56yH777Te777773LAy9bbRUEYAwYjdSApiNxKD2A2kLGI3koLYjcQgdqdN9ERP5wYPHuyGC3kTR4haujds2OBauROifdW6/eOPP7o/qqIWysBZpBW81Rr2+uuvuxpt8vHHHwc9j1o5T58+nULvDGnl98Ub4qP6WB79oVfPCI/X4p2Y3we9nuq/adZn1edC2qKTr7i/C+oVob8Zy5Yt8w8r++uvv1zNvfLly/v30zAzDT3VopPGsWPHWrdu3eK9Bn87kNERu5EUxG6cC7EbSHnEbiQFsRvnQuxOm+iJns5pyI7qaqmWluepp56yxYsXuwkqvJatzz//3D9hRdmyZd2wj06dOrmJKRTU9bNqIHktX/rPrBZPtWiplev999+30aNHB7226rWpdVP1m/7888+gP+5IP78vouFob731lvtd0SQX+mOdJUsW/3YFZv3+zJw50/bt2+dmgA5Fr1+wYEE3M7gmONHQNdVk07CkhCZgQeSpXp++r44dO7rJb9Rj4t5777VLLrnErRfV9lMdN32fq1evtnnz5oUcksjfDmR0xG4kBbEbyUHsBsKL2I2kIHYjOYjdkUcSPQMYMGCAa732aCjYggUL3DCdG264wapWrWp9+/a12NhY/z6TJk2ywoULu6FGmvxC/0lVK8ubzKJy5co2dOhQV8/rqquucnW7VKctkFrG9Ef97rvvdi2mQ4YMScV3jdT8fVHPCLV2aruGfqm+W2B9PtX60wnCO++84x7n/YFPiB6nmaSLFy/uan7pD36HDh1cbTZayNMuTUKjHjQablqrVi03zPDrr7/2n9SphVszhev71OzeZcqUcZMhJYS/HQCxG0lD7EZyELuB8CJ2IymI3UgOYndkxfjiFlQCEqCWSP2x1qQm9evXj/ThAACAcyB2AwAQXYjdAJB2kURHgr777js3rEPDjFRzq3fv3vbHH3+4VtHA4UIAACBtIHYDABBdiN0AED2YWBQJUt21Z555xtVd03AyDfPQ0DECOQAAaROxGwCA6ELsBoDoQU90AAAAAAAAAABCYGJRAAAAAAAAAABCIIkOAAAAAAAAAEAIJNEBAAAAAAAAAAiBJDoAAAAAAAAAACGQRAcAAAAAAAAAIASS6AAAAAAAAAAAhEASHQAAAAAAAACAEEiiAwAAAAAAAAAQAkl0AAAAAAAAAAAsYf8PMYGNB9+RlSAAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📏 Analyse des longueurs de tokens après preprocessing:\n", "\n", "Train:\n", "  Longueur min: 15 tokens\n", "  Longueur max: 512 tokens\n", "  <PERSON><PERSON><PERSON> moyenne: 104.0 tokens\n", "  Longueur médiane: 97.0 tokens\n", "\n", "Validation:\n", "  Longueur min: 17 tokens\n", "  Longueur max: 337 tokens\n", "  <PERSON><PERSON><PERSON> moyenne: 105.0 tokens\n", "  Longueur médiane: 98.0 tokens\n", "\n", "Test:\n", "  Longueur min: 19 tokens\n", "  Longueur max: 313 tokens\n", "  <PERSON><PERSON>ur moyenne: 106.3 tokens\n", "  Longueur médiane: 102.0 tokens\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25020\\3378871796.py:83: MatplotlibDeprecationWarning: The 'labels' parameter of boxplot() has been renamed 'tick_labels' since Matplotlib 3.9; support for the old name will be dropped in 3.11.\n", "  axes[1, 1].boxplot([all_lengths[name] for name, _ in splits],\n"]}, {"data": {"image/png": "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****************************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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📝 Exemples de données préprocessées:\n", "\n", "--- Exemple 1 du split Train ---\n", "Label: 2 (Positif)\n", "Texte décodé: novagold reports third quarter 2022 financial results - - advancing donlin gold to prepare the project for the next phase of development ; robust treasury of $ 132 million in cash and term deposits, w...\n", "<PERSON><PERSON>ur réelle: 52 tokens\n", "Premiers tokens: [101, 6846, 21270, 4311, 2353, 4284, 16798, 2475, 3361, 3463]\n", "Attention mask (10 premiers): [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]\n", "\n", "--- Exemple 1 du split Validation ---\n", "Label: 1 (Neutre)\n", "Texte décodé: israeli cannabis technology company bynd cannasoft enterprises inc. and matrix medika to explore further ( fda ) development of the ez - g device - - vancouver, british columbia and ashkelon, israel, ...\n", "Longueur réelle: 161 tokens\n", "Premiers tokens: [101, 5611, 17985, 2974, 2194, 2011, 4859, 2064, 11649, 15794]\n", "Attention mask (10 premiers): [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]\n", "\n", "--- Exemple 1 du split Test ---\n", "Label: 1 (Neutre)\n", "Texte décodé: pyrogenesis announces closing of a small non - brokered private placement - - pyrogenesis canada inc. ( http : / / pyrogenesis. com ) ( tsx : pyr ) ( nasdaq : pyr ) ( fra : 8py ), a high - tech compan...\n", "Longueur réelle: 218 tokens\n", "Premiers tokens: [101, 1052, 12541, 23924, 19009, 17472, 5494, 1997, 1037, 2235]\n", "Attention mask (10 premiers): [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]\n", "\n", "📊 RÉSUMÉ COMPLET DES DONNÉES PRÉPROCESSÉES:\n", "============================================================\n", "📈 Nombre total d'exemples: 1774\n", "  - Train: 1199 exemples (67.6%)\n", "  - Validation: 309 exemples (17.4%)\n", "  - Test: 266 exemples (15.0%)\n", "\n", "🔧 Configuration du preprocessing:\n", "  - Tokenizer: ProsusAI/finbert\n", "  - Longueur maximale: 512 tokens\n", "  - Padding: max_length\n", "  - Truncation: True\n", "\n", "💾 Données sauvegardées dans: preprocessed_datasets/\n", "  - Formats: PyTorch tensors\n", "  - Colonnes: ['labels', 'input_ids', 'attention_mask']\n", "\n", "🔍 VÉRIFICATION DE LA QUALITÉ:\n", "✅ Train: Aucun problème détecté\n", "✅ Validation: Aucun problème détecté\n", "✅ Test: <PERSON><PERSON>n problème détecté\n", "\n", "🎉 VISUALISATION ET ANALYSE TERMINÉES!\n"]}], "source": ["print(\"\\n=== ÉTAPE 3.7: VISUALISATION DES DONNÉES PRÉPROCESSÉES ===\")\n", "\n", "# Imports pour la visualisation\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from collections import Counter\n", "\n", "# Configuration du style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "# 1. Distribution des labels dans tous les splits\n", "fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "splits = [('Train', final_train), ('Validation', final_validation), ('Test', final_test)]\n", "\n", "for idx, (split_name, dataset_split) in enumerate(splits):\n", "    labels = [dataset_split[i]['labels'].item() for i in range(len(dataset_split))]\n", "    label_counts = Counter(labels)\n", "    \n", "    # <PERSON><PERSON><PERSON> le graphique\n", "    sentiment_names = ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>']\n", "    colors = ['#D62728', '#FFBF00', '#2CA02C']\n", "    \n", "    bars = axes[idx].bar(sentiment_names, [label_counts[i] for i in range(3)], color=colors)\n", "    axes[idx].set_title(f'Distribution - {split_name}')\n", "    axes[idx].set_ylabel('Nombre d\\'exemples')\n", "    \n", "    # Ajouter les pourcentages\n", "    total = len(dataset_split)\n", "    for i, bar in enumerate(bars):\n", "        height = bar.get_height()\n", "        percentage = (height / total) * 100\n", "        axes[idx].text(bar.get_x() + bar.get_width()/2., height + total*0.01,\n", "                      f'{height}\\n({percentage:.1f}%)', \n", "                      ha='center', va='bottom', fontsize=10)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 2. <PERSON><PERSON><PERSON> des longueurs de tokens\n", "print(\"\\n📏 Analyse des longueurs de tokens après preprocessing:\")\n", "\n", "def analyze_token_lengths_final(dataset_split, split_name, max_samples=1000):\n", "    \"\"\"Analyser les longueurs réelles des tokens dans les données préprocessées\"\"\"\n", "    sample_size = min(max_samples, len(dataset_split))\n", "    token_lengths = []\n", "    \n", "    for i in range(sample_size):\n", "        input_ids = dataset_split[i]['input_ids']\n", "        # Compter les tokens non-padding (différents de 0)\n", "        actual_length = (input_ids != 0).sum().item()\n", "        token_lengths.append(actual_length)\n", "    \n", "    return token_lengths\n", "\n", "# Analyser chaque split\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "all_lengths = {}\n", "for split_name, dataset_split in splits:\n", "    lengths = analyze_token_lengths_final(dataset_split, split_name)\n", "    all_lengths[split_name] = lengths\n", "    \n", "    print(f\"\\n{split_name}:\")\n", "    print(f\"  Longueur min: {min(lengths)} tokens\")\n", "    print(f\"  Longueur max: {max(lengths)} tokens\")\n", "    print(f\"  Longueur moyenne: {np.mean(lengths):.1f} tokens\")\n", "    print(f\"  Longueur médiane: {np.median(lengths):.1f} tokens\")\n", "\n", "# Histogrammes des longueurs\n", "for idx, (split_name, lengths) in enumerate(all_lengths.items()):\n", "    row, col = idx // 2, idx % 2\n", "    axes[row, col].hist(lengths, bins=30, alpha=0.7, edgecolor='black')\n", "    axes[row, col].set_title(f'Distribution des longueurs - {split_name}')\n", "    axes[row, col].set_xlabel('Longueur en tokens')\n", "    axes[row, col].set_ylabel('Fréquence')\n", "    axes[row, col].axvline(np.mean(lengths), color='red', linestyle='--', \n", "                          label=f'Moyenne: {np.mean(lengths):.1f}')\n", "    axes[row, col].legend()\n", "\n", "# Boxplot comparatif\n", "axes[1, 1].clear()\n", "axes[1, 1].boxplot([all_lengths[name] for name, _ in splits], \n", "                   labels=[name for name, _ in splits])\n", "axes[1, 1].set_title('Comparaison des longueurs entre splits')\n", "axes[1, 1].set_ylabel('Longueur en tokens')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 3. Exemples de données préprocessées\n", "print(\"\\n📝 Exemples de données préprocessées:\")\n", "\n", "def show_example(dataset_split, split_name, idx=0):\n", "    \"\"\"Afficher un exemple préprocessé avec décodage\"\"\"\n", "    example = dataset_split[idx]\n", "    \n", "    print(f\"\\n--- Exemple {idx+1} du split {split_name} ---\")\n", "    label_value = example['labels'].item()\n", "    sentiment_name = ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Positif'][label_value]\n", "    print(f\"Label: {label_value} ({sentiment_name})\")\n", "    \n", "    # Décoder les tokens\n", "    input_ids = example['input_ids']\n", "    attention_mask = example['attention_mask']\n", "    \n", "    # Trouver les tokens non-padding\n", "    actual_tokens = input_ids[attention_mask.bool()]\n", "    decoded_text = tokenizer.decode(actual_tokens, skip_special_tokens=True)\n", "    \n", "    print(f\"Texte décodé: {decoded_text[:200]}...\")\n", "    print(f\"Longueur réelle: {len(actual_tokens)} tokens\")\n", "    print(f\"Premiers tokens: {input_ids[:10].tolist()}\")\n", "    print(f\"Attention mask (10 premiers): {attention_mask[:10].tolist()}\")\n", "# Afficher des exemples de chaque split\n", "for split_name, dataset_split in splits:\n", "    show_example(dataset_split, split_name, idx=0)\n", "\n", "# 4. Statistiques détaillées\n", "print(f\"\\n📊 <PERSON><PERSON><PERSON><PERSON>É COMPLET DES DONNÉES PRÉPROCESSÉES:\")\n", "print(f\"=\" * 60)\n", "\n", "total_examples = sum(len(ds) for _, ds in splits)\n", "print(f\"📈 Nombre total d'exemples: {total_examples}\")\n", "\n", "for split_name, dataset_split in splits:\n", "    percentage = (len(dataset_split) / total_examples) * 100\n", "    print(f\"  - {split_name}: {len(dataset_split)} exemples ({percentage:.1f}%)\")\n", "\n", "print(f\"\\n🔧 Configuration du preprocessing:\")\n", "print(f\"  - Tokenizer: {tokenizer.name_or_path}\")\n", "print(f\"  - <PERSON><PERSON>ur maximale: 512 tokens\")\n", "print(f\"  - Padding: max_length\")\n", "print(f\"  - Truncation: True\")\n", "\n", "print(f\"\\n💾 Données sauvegardées dans: {save_dir}/\")\n", "print(f\"  - Formats: PyTorch tensors\")\n", "print(f\"  - <PERSON>onnes: {final_train.column_names}\")\n", "\n", "# 5. Vérification de la qualité des données\n", "print(f\"\\n🔍 VÉRIFICATION DE LA QUALITÉ:\")\n", "\n", "def quality_check(dataset_split, split_name):\n", "    \"\"\"Vérifier la qualité des données préprocessées\"\"\"\n", "    issues = []\n", "    \n", "    # Vérifier quelques exemples\n", "    sample_size = min(100, len(dataset_split))\n", "    \n", "    for i in range(sample_size):\n", "        example = dataset_split[i]\n", "        \n", "        # Vérifier les dimensions\n", "        if len(example['input_ids']) != 512:\n", "            issues.append(f\"Longueur input_ids incorrecte: {len(example['input_ids'])}\")\n", "        \n", "        if len(example['attention_mask']) != 512:\n", "            issues.append(f\"Longueur attention_mask incorrecte: {len(example['attention_mask'])}\")\n", "        \n", "        # Vérifier les labels\n", "        label = example['labels'].item()\n", "        if label not in [0, 1, 2]:\n", "            issues.append(f\"Label invalide: {label}\")\n", "        \n", "        # Vérifier que les tokens padding sont cohérents\n", "        input_ids = example['input_ids']\n", "        attention_mask = example['attention_mask']\n", "        \n", "        # Les positions avec attention_mask=0 devraient avoir input_ids=0 (PAD)\n", "        padding_positions = (attention_mask == 0)\n", "        if (input_ids[padding_positions] != 0).any():\n", "            issues.append(\"Incohérence entre padding et attention_mask\")\n", "    \n", "    if issues:\n", "        print(f\"⚠️ {split_name}: {len(issues)} problèmes détectés\")\n", "        for issue in issues[:5]:  # <PERSON><PERSON><PERSON><PERSON> les 5 premiers\n", "            print(f\"  - {issue}\")\n", "    else:\n", "        print(f\"✅ {split_name}: Aucun problème détecté\")\n", "\n", "for split_name, dataset_split in splits:\n", "    quality_check(dataset_split, split_name)\n", "\n", "print(f\"\\n🎉 VISUALISATION ET ANALYSE TERMINÉES!\")"]}, {"cell_type": "code", "execution_count": 27, "id": "6b560296", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== ÉTAPE 3.8: EXPORT DES DONNÉES VERS CSV ===\n", "\n", "📄 Export de Train vers CSV...\n", "✅ Train exporté: exported_csv_data\\train_processed.csv\n", "   - 1199 lignes\n", "   - Colonnes: ['index', 'text', 'label', 'sentiment', 'token_length', 'input_ids', 'attention_mask']\n", "\n", "📄 Export de Validation vers CSV...\n", "✅ Validation exporté: exported_csv_data\\validation_processed.csv\n", "   - 309 lignes\n", "   - Colonnes: ['index', 'text', 'label', 'sentiment', 'token_length', 'input_ids', 'attention_mask']\n", "\n", "📄 Export de Test vers CSV...\n", "✅ Test exporté: exported_csv_data\\test_processed.csv\n", "   - 266 lignes\n", "   - Colonnes: ['index', 'text', 'label', 'sentiment', 'token_length', 'input_ids', 'attention_mask']\n", "\n", "📊 Création du fichier de résumé...\n", "✅ Résumé exporté: exported_csv_data\\summary_statistics.csv\n", "\n", "📋 Aperçu du résumé:\n", "     split  label sentiment  count  percentage  avg_token_length  total_examples\n", "     Train      0   Négatif     36        3.00             135.0            1199\n", "     Train      1    Neutre    712       59.38              94.5            1199\n", "     Train      2   Positif    451       37.61             117.2            1199\n", "Validation      0   Négatif     13        4.21             128.8             309\n", "Validation      1    Neutre    186       60.19              96.7             309\n", "Validation      2   Positif    110       35.60             116.3             309\n", "      Test      0   Négatif     12        4.51             128.1             266\n", "      Test      1    Neutre    155       58.27              96.3             266\n", "      Test      2   Positif     99       37.22             119.3             266\n", "\n", "💾 Fichiers CSV exportés dans: exported_csv_data/\n", "📁 Fichiers créés:\n", "  - train_processed.csv\n", "  - validation_processed.csv\n", "  - test_processed.csv\n", "  - summary_statistics.csv\n", "  - export_metadata.json\n", "\n", "🎉 EXPORT CSV TERMINÉ!\n"]}], "source": ["print(\"\\n=== ÉTAPE 3.8: EXPORT DES DONNÉES VERS CSV ===\")\n", "\n", "# C<PERSON>er un dossier pour les exports CSV\n", "csv_export_dir = \"exported_csv_data\"\n", "os.makedirs(csv_export_dir, exist_ok=True)\n", "\n", "def export_to_csv(dataset_split, split_name, tokenizer):\n", "    \"\"\"Exporter un dataset vers CSV avec texte décodé\"\"\"\n", "    print(f\"\\n📄 Export de {split_name} vers CSV...\")\n", "    \n", "    # Préparer les données pour l'export\n", "    export_data = []\n", "    \n", "    for i in range(len(dataset_split)):\n", "        example = dataset_split[i]\n", "        \n", "        # Dé<PERSON>r le texte\n", "        input_ids = example['input_ids']\n", "        attention_mask = example['attention_mask']\n", "        actual_tokens = input_ids[attention_mask.bool()]\n", "        decoded_text = tokenizer.decode(actual_tokens, skip_special_tokens=True)\n", "        \n", "        # P<PERSON><PERSON>er la ligne de données\n", "        row = {\n", "            'index': i,\n", "            'text': decoded_text,\n", "            'label': example['labels'].item(),\n", "            'sentiment': ['<PERSON><PERSON>gat<PERSON>', '<PERSON>eu<PERSON>', '<PERSON><PERSON>ti<PERSON>'][example['labels'].item()],\n", "            'token_length': len(actual_tokens),\n", "            'input_ids': input_ids.tolist(),\n", "            'attention_mask': attention_mask.tolist()\n", "        }\n", "        export_data.append(row)\n", "    \n", "    # <PERSON><PERSON><PERSON> le DataFrame\n", "    df_export = pd.DataFrame(export_data)\n", "    \n", "    # Sauvegarder en CSV\n", "    csv_path = os.path.join(csv_export_dir, f\"{split_name.lower()}_processed.csv\")\n", "    df_export.to_csv(csv_path, index=False, encoding='utf-8')\n", "    \n", "    print(f\"✅ {split_name} exporté: {csv_path}\")\n", "    print(f\"   - {len(df_export)} lignes\")\n", "    print(f\"   - Colonnes: {list(df_export.columns)}\")\n", "    \n", "    return df_export\n", "\n", "# Exporter tous les splits\n", "exported_dfs = {}\n", "for split_name, dataset_split in splits:\n", "    exported_dfs[split_name] = export_to_csv(dataset_split, split_name, tokenizer)\n", "\n", "# Créer un résumé global\n", "print(f\"\\n📊 Création du fichier de résumé...\")\n", "summary_data = []\n", "\n", "for split_name, df in exported_dfs.items():\n", "    # Statistiques par split\n", "    label_counts = df['label'].value_counts().sort_index()\n", "    \n", "    for label, count in label_counts.items():\n", "        sentiment = ['<PERSON><PERSON>gat<PERSON>', '<PERSON>eu<PERSON>', 'Positif'][label]\n", "        percentage = (count / len(df)) * 100\n", "        \n", "        summary_data.append({\n", "            'split': split_name,\n", "            'label': label,\n", "            'sentiment': sentiment,\n", "            'count': count,\n", "            'percentage': round(percentage, 2),\n", "            'avg_token_length': round(df[df['label'] == label]['token_length'].mean(), 1),\n", "            'total_examples': len(df)\n", "        })\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> le résumé\n", "summary_df = pd.DataFrame(summary_data)\n", "summary_path = os.path.join(csv_export_dir, \"summary_statistics.csv\")\n", "summary_df.to_csv(summary_path, index=False)\n", "\n", "print(f\"✅ Résumé exporté: {summary_path}\")\n", "\n", "# A<PERSON><PERSON><PERSON> le résumé\n", "print(f\"\\n📋 Aperçu du résumé:\")\n", "print(summary_df.to_string(index=False))\n", "\n", "# C<PERSON>er un fichier de métadonnées pour l'export\n", "export_metadata = {\n", "    \"export_date\": pd.Timestamp.now().strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "    \"tokenizer_used\": tokenizer.name_or_path,\n", "    \"max_length\": 512,\n", "    \"files_exported\": {\n", "        \"train\": f\"train_processed.csv ({len(exported_dfs['Train'])} lignes)\",\n", "        \"validation\": f\"validation_processed.csv ({len(exported_dfs['Validation'])} lignes)\",\n", "        \"test\": f\"test_processed.csv ({len(exported_dfs['Test'])} lignes)\",\n", "        \"summary\": f\"summary_statistics.csv ({len(summary_df)} lignes)\"\n", "    },\n", "    \"columns_description\": {\n", "        \"index\": \"Index de l'exemple dans le dataset\",\n", "        \"text\": \"Texte décodé (sans tokens spéciaux)\",\n", "        \"label\": \"Label numérique (0=Négatif, 1=Neutre, 2=Positif)\",\n", "        \"sentiment\": \"Nom du sentiment\",\n", "        \"token_length\": \"Longueur réelle en tokens (sans padding)\",\n", "        \"input_ids\": \"IDs des tokens (avec padding)\",\n", "        \"attention_mask\": \"Masque d'attention\"\n", "    }\n", "}\n", "\n", "metadata_path = os.path.join(csv_export_dir, \"export_metadata.json\")\n", "with open(metadata_path, \"w\", encoding='utf-8') as f:\n", "    json.dump(export_metadata, f, indent=2, ensure_ascii=False)\n", "\n", "print(f\"\\n💾 Fichiers CSV exportés dans: {csv_export_dir}/\")\n", "print(f\"📁 Fichiers créés:\")\n", "for split_name in ['Train', 'Validation', 'Test']:\n", "    filename = f\"{split_name.lower()}_processed.csv\"\n", "    print(f\"  - {filename}\")\n", "print(f\"  - summary_statistics.csv\")\n", "print(f\"  - export_metadata.json\")\n", "\n", "print(f\"\\n🎉 EXPORT CSV TERMINÉ!\")"]}, {"cell_type": "code", "execution_count": 28, "id": "9d3cc375", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>index</th>\n", "      <th>text</th>\n", "      <th>label</th>\n", "      <th>sentiment</th>\n", "      <th>token_length</th>\n", "      <th>input_ids</th>\n", "      <th>attention_mask</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>novagold reports third quarter 2022 financial ...</td>\n", "      <td>2</td>\n", "      <td>Positif</td>\n", "      <td>52</td>\n", "      <td>[101, 6846, 21270, 4311, 2353, 4284, 16798, 24...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>ackroo launches marketinghub for their ackroom...</td>\n", "      <td>1</td>\n", "      <td>Neutre</td>\n", "      <td>43</td>\n", "      <td>[101, 9353, 21638, 9541, 18989, 5821, 6979, 24...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>kraken signs $ 8 million contract with global ...</td>\n", "      <td>2</td>\n", "      <td>Positif</td>\n", "      <td>158</td>\n", "      <td>[101, 1047, 16555, 2368, 5751, 1002, 1022, 245...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>rivalry to present at sbc summit latinoamerica...</td>\n", "      <td>1</td>\n", "      <td>Neutre</td>\n", "      <td>130</td>\n", "      <td>[101, 10685, 2000, 2556, 2012, 24829, 2278, 64...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>profound medical announces third quarter 2022 ...</td>\n", "      <td>1</td>\n", "      <td>Neutre</td>\n", "      <td>124</td>\n", "      <td>[101, 13769, 2966, 17472, 2353, 4284, 16798, 2...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1194</th>\n", "      <td>1194</td>\n", "      <td>investmentpitch media video discusses enduranc...</td>\n", "      <td>1</td>\n", "      <td>Neutre</td>\n", "      <td>155</td>\n", "      <td>[101, 5211, 23270, 2818, 2865, 2678, 15841, 14...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1195</th>\n", "      <td>1195</td>\n", "      <td>medavail ranked in the top 500 fastest - growi...</td>\n", "      <td>2</td>\n", "      <td>Positif</td>\n", "      <td>84</td>\n", "      <td>[101, 19960, 12462, 4014, 4396, 1999, 1996, 23...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1196</th>\n", "      <td>1196</td>\n", "      <td>kraken ’ s katfish towed sonar providing stron...</td>\n", "      <td>2</td>\n", "      <td>Positif</td>\n", "      <td>141</td>\n", "      <td>[101, 1047, 16555, 2368, 1521, 1055, 10645, 75...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1197</th>\n", "      <td>1197</td>\n", "      <td>digihost reports positive third quarter 2022 r...</td>\n", "      <td>2</td>\n", "      <td>Positif</td>\n", "      <td>56</td>\n", "      <td>[101, 10667, 19190, 14122, 4311, 3893, 2353, 4...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1198</th>\n", "      <td>1198</td>\n", "      <td>kraken receives $ 1. 1 million of contracts fo...</td>\n", "      <td>2</td>\n", "      <td>Positif</td>\n", "      <td>148</td>\n", "      <td>[101, 1047, 16555, 2368, 8267, 1002, 1015, 101...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1199 rows × 7 columns</p>\n", "</div>"], "text/plain": ["      index                                               text  label  \\\n", "0         0  novagold reports third quarter 2022 financial ...      2   \n", "1         1  ackroo launches marketinghub for their ackroom...      1   \n", "2         2  kraken signs $ 8 million contract with global ...      2   \n", "3         3  rivalry to present at sbc summit latinoamerica...      1   \n", "4         4  profound medical announces third quarter 2022 ...      1   \n", "...     ...                                                ...    ...   \n", "1194   1194  investmentpitch media video discusses enduranc...      1   \n", "1195   1195  medavail ranked in the top 500 fastest - growi...      2   \n", "1196   1196  kraken ’ s katfish towed sonar providing stron...      2   \n", "1197   1197  digihost reports positive third quarter 2022 r...      2   \n", "1198   1198  kraken receives $ 1. 1 million of contracts fo...      2   \n", "\n", "     sentiment  token_length  \\\n", "0      Positif            52   \n", "1       Neutre            43   \n", "2      Positif           158   \n", "3       Neutre           130   \n", "4       Neutre           124   \n", "...        ...           ...   \n", "1194    Neutre           155   \n", "1195   Positif            84   \n", "1196   Positif           141   \n", "1197   Positif            56   \n", "1198   Positif           148   \n", "\n", "                                              input_ids  \\\n", "0     [101, 6846, 21270, 4311, 2353, 4284, 16798, 24...   \n", "1     [101, 9353, 21638, 9541, 18989, 5821, 6979, 24...   \n", "2     [101, 1047, 16555, 2368, 5751, 1002, 1022, 245...   \n", "3     [101, 10685, 2000, 2556, 2012, 24829, 2278, 64...   \n", "4     [101, 13769, 2966, 17472, 2353, 4284, 16798, 2...   \n", "...                                                 ...   \n", "1194  [101, 5211, 23270, 2818, 2865, 2678, 15841, 14...   \n", "1195  [101, 19960, 12462, 4014, 4396, 1999, 1996, 23...   \n", "1196  [101, 1047, 16555, 2368, 1521, 1055, 10645, 75...   \n", "1197  [101, 10667, 19190, 14122, 4311, 3893, 2353, 4...   \n", "1198  [101, 1047, 16555, 2368, 8267, 1002, 1015, 101...   \n", "\n", "                                         attention_mask  \n", "0     [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "1     [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "2     [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "3     [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "4     [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "...                                                 ...  \n", "1194  [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "1195  [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "1196  [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "1197  [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "1198  [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "\n", "[1199 rows x 7 columns]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_csv(os.path.join(csv_export_dir, \"train_processed.csv\"))"]}, {"cell_type": "markdown", "id": "d994945d", "metadata": {}, "source": ["## **R<PERSON><PERSON><PERSON> du Prétraitement**\n", "\n", "Ce notebook a implémenté avec succès toutes les étapes de prétraitement nécessaires :\n", "\n", "### **✅ Étapes Complétées :**\n", "\n", "1. **Chargement du Dataset** : Dataset financier chargé depuis Hugging Face\n", "2. **Vérification des Caractéristiques** : Analyse complète des colonnes et statistiques\n", "3. **Division du Dataset** : Création des splits train/validation/test avec distribution équilibrée\n", "4. **Nettoyage des Données** : Suppression des balises HTML, caractères spéciaux, gestion des valeurs nulles\n", "5. **Tokenisation** : Utilisation du tokenizer FinBERT avec analyse des longueurs optimales\n", "6. **Formatage PyTorch** : Configuration pour l'entraînement avec tensors PyTorch\n", "7. **Sauvegarde** : Datasets et métadonnées sauvegardés pour réutilisation\n", "\n", "### **📊 Datasets Finaux :**\n", "- **Tokenizer** : FinBERT (ProsusAI/finbert)\n", "- **<PERSON><PERSON><PERSON> maximale** : 512 tokens\n", "- **Format** : PyTorch tensors\n", "- **Colonnes** : input_ids, attention_mask, label\n", "\n", "Les datasets sont maintenant prêts pour l'étape suivante : l'entraînement du modèle FinBERT pour l'analyse de sentiment financier."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}