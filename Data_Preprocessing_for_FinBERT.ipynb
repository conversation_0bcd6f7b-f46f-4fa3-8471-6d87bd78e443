{"cells": [{"cell_type": "markdown", "id": "6e4558cf", "metadata": {}, "source": ["# **Data Preprocessing for FinBERT Fine-tuning**\n", "## **Financial Sentiment Analysis with data.csv**\n", "\n", "This notebook processes the `data.csv` file for FinBERT fine-tuning on financial sentiment analysis. The dataset contains financial sentences with sentiment labels (positive, negative, neutral) that will be preprocessed and formatted for transformer model training.\n", "\n", "### **Objectives:**\n", "1. **Load and explore** the CSV dataset structure\n", "2. **Clean and preprocess** text data for financial sentiment analysis\n", "3. **Tokenize** using FinBERT tokenizer with optimal parameters\n", "4. **Split** data into training, validation, and test sets\n", "5. **Format** for PyTorch and HuggingFace compatibility\n", "6. **Save** preprocessed datasets for model training\n", "\n", "### **Dataset Structure:**\n", "- `Sentence`: Financial text/news for sentiment analysis\n", "- `Sentiment`: Labels (positive, negative, neutral)"]}, {"cell_type": "markdown", "id": "9ab64969", "metadata": {}, "source": ["## **1. Import Required Libraries**"]}, {"cell_type": "code", "execution_count": null, "id": "7ef8ca28", "metadata": {}, "outputs": [], "source": ["# Core data processing libraries\n", "import pandas as pd\n", "import numpy as np\n", "import re\n", "import os\n", "import json\n", "import pickle\n", "from collections import Counter\n", "from typing import Dict, List, Tuple\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Machine Learning and NLP libraries\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.utils import shuffle\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "\n", "# HuggingFace Transformers and Datasets\n", "from transformers import AutoTokenizer, AutoConfig\n", "from datasets import Dataset, DatasetDict\n", "import torch\n", "from torch.utils.data import DataLoader\n", "\n", "# Visualization libraries\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from wordcloud import WordCloud\n", "\n", "# Text preprocessing\n", "from html import unescape\n", "from unicodedata import normalize\n", "\n", "# Set style for better visualizations\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✅ All libraries imported successfully!\")\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")"]}, {"cell_type": "markdown", "id": "245fa939", "metadata": {}, "source": ["## **2. <PERSON><PERSON> and Explore the CSV Data**"]}, {"cell_type": "code", "execution_count": null, "id": "ca270451", "metadata": {}, "outputs": [], "source": ["# Load the CSV data\n", "print(\"📊 Loading data.csv...\")\n", "try:\n", "    df = pd.read_csv(\"data.csv\", encoding='utf-8')\n", "    print(f\"✅ Data loaded successfully!\")\n", "except UnicodeDecodeError:\n", "    print(\"⚠️ UTF-8 encoding failed, trying with latin-1...\")\n", "    df = pd.read_csv(\"data.csv\", encoding='latin-1')\n", "    print(f\"✅ Data loaded with latin-1 encoding!\")\n", "\n", "# Basic dataset information\n", "print(f\"\\n📈 Dataset Overview:\")\n", "print(f\"Shape: {df.shape}\")\n", "print(f\"Columns: {list(df.columns)}\")\n", "print(f\"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "\n", "# Display first few rows\n", "print(f\"\\n📝 First 5 rows:\")\n", "display(df.head())\n", "\n", "# Check data types\n", "print(f\"\\n🔍 Data Types:\")\n", "print(df.dtypes)\n", "\n", "# Basic statistics\n", "print(f\"\\n📊 Basic Statistics:\")\n", "print(df.describe(include='all'))"]}, {"cell_type": "code", "execution_count": null, "id": "10d5e681", "metadata": {}, "outputs": [], "source": ["# Check for missing values and data quality issues\n", "print(\"🔍 Data Quality Analysis:\")\n", "print(f\"\\nMissing values:\")\n", "missing_data = df.isnull().sum()\n", "for col, missing_count in missing_data.items():\n", "    if missing_count > 0:\n", "        print(f\"  {col}: {missing_count} ({missing_count/len(df)*100:.2f}%)\")\n", "    else:\n", "        print(f\"  {col}: No missing values ✅\")\n", "\n", "# Check for empty strings\n", "print(f\"\\nEmpty strings:\")\n", "for col in df.columns:\n", "    if df[col].dtype == 'object':\n", "        empty_count = (df[col].astype(str).str.strip() == '').sum()\n", "        if empty_count > 0:\n", "            print(f\"  {col}: {empty_count} empty strings\")\n", "        else:\n", "            print(f\"  {col}: No empty strings ✅\")\n", "\n", "# Check for duplicates\n", "duplicate_count = df.duplicated().sum()\n", "print(f\"\\nDuplicate rows: {duplicate_count}\")\n", "\n", "# Sentiment label distribution\n", "print(f\"\\n📊 Sentiment Distribution:\")\n", "sentiment_counts = df['Sentiment'].value_counts()\n", "print(sentiment_counts)\n", "print(f\"\\nPercentages:\")\n", "sentiment_percentages = df['Sentiment'].value_counts(normalize=True) * 100\n", "for sentiment, percentage in sentiment_percentages.items():\n", "    print(f\"  {sentiment}: {percentage:.2f}%\")\n", "\n", "# Visualize sentiment distribution\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Bar plot\n", "sentiment_counts.plot(kind='bar', ax=ax1, color=['#d62728', '#ff7f0e', '#2ca02c'])\n", "ax1.set_title('Sentiment Distribution (Count)', fontsize=14, fontweight='bold')\n", "ax1.set_xlabel('Sentiment')\n", "ax1.set_ylabel('Count')\n", "ax1.tick_params(axis='x', rotation=45)\n", "\n", "# Pie chart\n", "ax2.pie(sentiment_counts.values, labels=sentiment_counts.index, autopct='%1.1f%%', \n", "        colors=['#d62728', '#ff7f0e', '#2ca02c'], startangle=90)\n", "ax2.set_title('Sentiment Distribution (Percentage)', fontsize=14, fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "754ede17", "metadata": {}, "source": ["## **3. Data Cleaning and Preprocessing**"]}, {"cell_type": "code", "execution_count": null, "id": "59966a3f", "metadata": {}, "outputs": [], "source": ["def clean_text(text):\n", "    \"\"\"\n", "    Comprehensive text cleaning function for financial text data\n", "    \"\"\"\n", "    if pd.isna(text) or text is None:\n", "        return \"\"\n", "    \n", "    # Convert to string if not already\n", "    text = str(text)\n", "    \n", "    # Decode HTML entities\n", "    text = unescape(text)\n", "    \n", "    # Remove HTML tags\n", "    text = re.sub(r'<[^>]+>', '', text)\n", "    \n", "    # Normalize unicode characters\n", "    text = normalize('NFKD', text)\n", "    \n", "    # Remove control characters but keep basic punctuation\n", "    text = re.sub(r'[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F-\\x9F]', ' ', text)\n", "    \n", "    # Fix common encoding issues\n", "    text = text.replace('â€™', \"'\")\n", "    text = text.replace('â€œ', '\"')\n", "    text = text.replace('â€�', '\"')\n", "    text = text.replace('â€\"', '-')\n", "    text = text.replace('â€\"', '--')\n", "    \n", "    # Clean up multiple spaces, tabs, and newlines\n", "    text = re.sub(r'\\s+', ' ', text)\n", "    \n", "    # Remove leading/trailing whitespace\n", "    text = text.strip()\n", "    \n", "    return text\n", "\n", "def validate_text(text, min_length=5):\n", "    \"\"\"\n", "    Validate if text meets minimum requirements\n", "    \"\"\"\n", "    if pd.isna(text) or text is None:\n", "        return False\n", "    \n", "    text = str(text).strip()\n", "    \n", "    # Check minimum length\n", "    if len(text) < min_length:\n", "        return False\n", "    \n", "    # Check if text contains at least some alphabetic characters\n", "    if not re.search(r'[a-zA-Z]', text):\n", "        return False\n", "    \n", "    return True\n", "\n", "# Create a copy for processing\n", "print(\"🧹 Starting data cleaning process...\")\n", "df_clean = df.copy()\n", "initial_rows = len(df_clean)\n", "\n", "print(f\"Initial dataset size: {initial_rows} rows\")\n", "\n", "# Clean the Sentence column\n", "print(\"\\n🔧 Cleaning text data...\")\n", "df_clean['Sentence_cleaned'] = df_clean['Sentence'].apply(clean_text)\n", "\n", "# Remove rows with invalid text\n", "print(\"🔍 Filtering invalid entries...\")\n", "valid_mask = df_clean['Sentence_cleaned'].apply(lambda x: validate_text(x, min_length=10))\n", "df_clean = df_clean[valid_mask].copy()\n", "\n", "print(f\"Removed {initial_rows - len(df_clean)} invalid entries\")\n", "print(f\"Remaining rows: {len(df_clean)}\")\n", "\n", "# Remove exact duplicates based on cleaned text\n", "print(\"\\n🔄 Removing duplicates...\")\n", "before_dedup = len(df_clean)\n", "df_clean = df_clean.drop_duplicates(subset=['Sentence_cleaned'], keep='first')\n", "duplicates_removed = before_dedup - len(df_clean)\n", "\n", "print(f\"Removed {duplicates_removed} duplicate entries\")\n", "print(f\"Final dataset size: {len(df_clean)} rows\")\n", "\n", "# Show some examples of cleaned text\n", "print(f\"\\n✨ Text cleaning examples:\")\n", "for i in range(min(3, len(df_clean))):\n", "    idx = df_clean.index[i]\n", "    original = df.loc[idx, 'Sentence']\n", "    cleaned = df_clean.loc[idx, 'Sentence_cleaned']\n", "    \n", "    print(f\"\\nExample {i+1}:\")\n", "    print(f\"Original:  {original[:100]}...\")\n", "    print(f\"Cleaned:   {cleaned[:100]}...\")\n", "    print(f\"Sentiment: {df_clean.loc[idx, 'Sentiment']}\")\n", "\n", "# Update sentiment distribution after cleaning\n", "print(f\"\\n📊 Sentiment distribution after cleaning:\")\n", "sentiment_after_cleaning = df_clean['Sentiment'].value_counts()\n", "print(sentiment_after_cleaning)\n", "\n", "# Reset index for clean dataset\n", "df_clean = df_clean.reset_index(drop=True)\n", "print(f\"\\n✅ Data cleaning completed successfully!\")\n", "print(f\"Dataset ready for further processing: {len(df_clean)} samples\")"]}, {"cell_type": "markdown", "id": "32a93c8c", "metadata": {}, "source": ["## **4. Text Analysis and Statistics**"]}, {"cell_type": "code", "execution_count": null, "id": "2c3dccd8", "metadata": {}, "outputs": [], "source": ["# Analyze text characteristics\n", "print(\"📏 Analyzing text characteristics...\")\n", "\n", "# Calculate text statistics\n", "df_clean['text_length'] = df_clean['Sentence_cleaned'].str.len()\n", "df_clean['word_count'] = df_clean['Sentence_cleaned'].str.split().str.len()\n", "df_clean['sentence_count'] = df_clean['Sentence_cleaned'].str.count(r'[.!?]+') + 1\n", "\n", "# Text statistics by sentiment\n", "print(\"\\n📊 Text Statistics by Sentiment:\")\n", "stats_by_sentiment = df_clean.groupby('Sentiment').agg({\n", "    'text_length': ['mean', 'median', 'std', 'min', 'max'],\n", "    'word_count': ['mean', 'median', 'std', 'min', 'max'],\n", "    'sentence_count': ['mean', 'median', 'std', 'min', 'max']\n", "}).round(2)\n", "\n", "print(stats_by_sentiment)\n", "\n", "# Overall statistics\n", "print(f\"\\n📈 Overall Text Statistics:\")\n", "print(f\"Character length - Mean: {df_clean['text_length'].mean():.1f}, Median: {df_clean['text_length'].median():.1f}\")\n", "print(f\"Word count - Mean: {df_clean['word_count'].mean():.1f}, Median: {df_clean['word_count'].median():.1f}\")\n", "print(f\"Sentence count - Mean: {df_clean['sentence_count'].mean():.1f}, Median: {df_clean['sentence_count'].median():.1f}\")\n", "\n", "# Create comprehensive visualizations\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "\n", "# 1. Text length distribution by sentiment\n", "sns.boxplot(data=df_clean, x='Sentiment', y='text_length', ax=axes[0,0])\n", "axes[0,0].set_title('Text Length Distribution by Sentiment', fontsize=12, fontweight='bold')\n", "axes[0,0].set_ylabel('Character Count')\n", "\n", "# 2. Word count distribution by sentiment\n", "sns.boxplot(data=df_clean, x='Sentiment', y='word_count', ax=axes[0,1])\n", "axes[0,1].set_title('Word Count Distribution by Sentiment', fontsize=12, fontweight='bold')\n", "axes[0,1].set_ylabel('Word Count')\n", "\n", "# 3. Overall text length histogram\n", "df_clean['text_length'].hist(bins=50, ax=axes[0,2], alpha=0.7, edgecolor='black')\n", "axes[0,2].set_title('Overall Text Length Distribution', fontsize=12, fontweight='bold')\n", "axes[0,2].set_xlabel('Character Count')\n", "axes[0,2].set_ylabel('Frequency')\n", "\n", "# 4. Word count by sentiment (violin plot)\n", "sns.violinplot(data=df_clean, x='Sentiment', y='word_count', ax=axes[1,0])\n", "axes[1,0].set_title('Word Count Distribution (Violin Plot)', fontsize=12, fontweight='bold')\n", "\n", "# 5. Sentiment distribution (updated)\n", "sentiment_counts = df_clean['Sentiment'].value_counts()\n", "axes[1,1].pie(sentiment_counts.values, labels=sentiment_counts.index, autopct='%1.1f%%', \n", "              colors=['#d62728', '#2ca02c', '#ff7f0e'], startangle=90)\n", "axes[1,1].set_title('Final Sentiment Distribution', fontsize=12, fontweight='bold')\n", "\n", "# 6. Text length vs word count scatter\n", "for sentiment in df_clean['Sentiment'].unique():\n", "    subset = df_clean[df_clean['Sentiment'] == sentiment]\n", "    axes[1,2].scatter(subset['word_count'], subset['text_length'], \n", "                     label=sentiment, alpha=0.6, s=20)\n", "axes[1,2].set_xlabel('Word Count')\n", "axes[1,2].set_ylabel('Character Count')\n", "axes[1,2].set_title('Text Length vs Word Count by Sentiment', fontsize=12, fontweight='bold')\n", "axes[1,2].legend()\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Find potential outliers\n", "print(f\"\\n🔍 Identifying potential outliers:\")\n", "\n", "# Text length outliers (using IQR method)\n", "Q1 = df_clean['text_length'].quantile(0.25)\n", "Q3 = df_clean['text_length'].quantile(0.75)\n", "IQR = Q3 - Q1\n", "lower_bound = Q1 - 1.5 * IQR\n", "upper_bound = Q3 + 1.5 * IQR\n", "\n", "length_outliers = df_clean[(df_clean['text_length'] < lower_bound) | \n", "                          (df_clean['text_length'] > upper_bound)]\n", "\n", "print(f\"Text length outliers: {len(length_outliers)} samples\")\n", "if len(length_outliers) > 0:\n", "    print(f\"Outlier range: < {lower_bound:.0f} or > {upper_bound:.0f} characters\")\n", "    print(f\"Shortest outlier: {length_outliers['text_length'].min()} characters\")\n", "    print(f\"Longest outlier: {length_outliers['text_length'].max()} characters\")\n", "\n", "# Show some examples of very short and very long texts\n", "print(f\"\\n📝 Example of shortest text:\")\n", "shortest_idx = df_clean['text_length'].idxmin()\n", "print(f\"Length: {df_clean.loc[shortest_idx, 'text_length']} characters\")\n", "print(f\"Text: {df_clean.loc[shortest_idx, 'Sentence_cleaned']}\")\n", "print(f\"Sentiment: {df_clean.loc[shortest_idx, 'Sentiment']}\")\n", "\n", "print(f\"\\n📝 Example of longest text:\")\n", "longest_idx = df_clean['text_length'].idxmax()\n", "print(f\"Length: {df_clean.loc[longest_idx, 'text_length']} characters\")\n", "print(f\"Text: {df_clean.loc[longest_idx, 'Sentence_cleaned'][:200]}...\")\n", "print(f\"Sentiment: {df_clean.loc[longest_idx, 'Sentiment']}\")\n", "\n", "print(f\"\\n✅ Text analysis completed!\")"]}, {"cell_type": "markdown", "id": "6b156390", "metadata": {}, "source": ["## **5. Label Encoding and Validation**"]}, {"cell_type": "code", "execution_count": null, "id": "9a2fc21f", "metadata": {}, "outputs": [], "source": ["# Label encoding for sentiment analysis\n", "print(\"🏷️ Encoding sentiment labels...\")\n", "\n", "# Check unique sentiment values\n", "unique_sentiments = df_clean['Sentiment'].unique()\n", "print(f\"Unique sentiment labels: {unique_sentiments}\")\n", "\n", "# Create label mapping\n", "# Standard mapping: negative=0, neutral=1, positive=2\n", "label_mapping = {\n", "    'negative': 0,\n", "    'neutral': 1, \n", "    'positive': 2\n", "}\n", "\n", "# Handle case variations\n", "def normalize_sentiment(sentiment):\n", "    \"\"\"Normalize sentiment labels to handle case variations\"\"\"\n", "    if pd.isna(sentiment):\n", "        return sentiment\n", "    \n", "    sentiment_str = str(sentiment).lower().strip()\n", "    \n", "    # Map variations to standard labels\n", "    if sentiment_str in ['negative', 'neg', 'bad', '-1', '0']:\n", "        return 'negative'\n", "    elif sentiment_str in ['neutral', 'neu', 'none', 'neut', '0', '1']:\n", "        return 'neutral'\n", "    elif sentiment_str in ['positive', 'pos', 'good', '1', '2']:\n", "        return 'positive'\n", "    else:\n", "        return sentiment_str\n", "\n", "# Normalize sentiment labels\n", "df_clean['Sentiment_normalized'] = df_clean['Sentiment'].apply(normalize_sentiment)\n", "\n", "# Check for any unmapped labels\n", "unmapped_labels = df_clean[~df_clean['Sentiment_normalized'].isin(label_mapping.keys())]\n", "if len(unmapped_labels) > 0:\n", "    print(f\"⚠️ Found {len(unmapped_labels)} unmapped labels:\")\n", "    print(unmapped_labels['Sentiment_normalized'].value_counts())\n", "    \n", "    # Remove rows with unmapped labels\n", "    df_clean = df_clean[df_clean['Sentiment_normalized'].isin(label_mapping.keys())].copy()\n", "    print(f\"Removed unmapped labels. Remaining samples: {len(df_clean)}\")\n", "\n", "# Apply label encoding\n", "df_clean['label'] = df_clean['Sentiment_normalized'].map(label_mapping)\n", "\n", "# Validate label encoding\n", "print(f\"\\n✅ Label encoding validation:\")\n", "print(f\"Label mapping used: {label_mapping}\")\n", "\n", "# Check label distribution\n", "label_distribution = df_clean['label'].value_counts().sort_index()\n", "print(f\"\\nLabel distribution:\")\n", "for label, count in label_distribution.items():\n", "    sentiment_name = [k for k, v in label_mapping.items() if v == label][0]\n", "    percentage = (count / len(df_clean)) * 100\n", "    print(f\"  {label} ({sentiment_name}): {count} samples ({percentage:.2f}%)\")\n", "\n", "# Verify no missing labels\n", "missing_labels = df_clean['label'].isna().sum()\n", "if missing_labels > 0:\n", "    print(f\"⚠️ Found {missing_labels} missing labels\")\n", "    df_clean = df_clean.dropna(subset=['label']).copy()\n", "    print(f\"Removed missing labels. Final samples: {len(df_clean)}\")\n", "else:\n", "    print(f\"✅ No missing labels found\")\n", "\n", "# Create reverse mapping for reference\n", "reverse_label_mapping = {v: k for k, v in label_mapping.items()}\n", "print(f\"\\nReverse mapping: {reverse_label_mapping}\")\n", "\n", "# Final validation\n", "print(f\"\\n🔍 Final label validation:\")\n", "print(f\"Total samples: {len(df_clean)}\")\n", "print(f\"Unique labels: {sorted(df_clean['label'].unique())}\")\n", "print(f\"Label range: {df_clean['label'].min()} to {df_clean['label'].max()}\")\n", "\n", "# Visualize final label distribution\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))\n", "\n", "# Bar plot with proper labels\n", "label_names = [reverse_label_mapping[i] for i in sorted(label_distribution.index)]\n", "colors = ['#d62728', '#ff7f0e', '#2ca02c']  # Red, Orange, Green\n", "\n", "ax1.bar(label_names, label_distribution.sort_index().values, color=colors)\n", "ax1.set_title('Final Label Distribution', fontsize=14, fontweight='bold')\n", "ax1.set_ylabel('Count')\n", "ax1.set_xlabel('Sentiment')\n", "\n", "# Add count labels on bars\n", "for i, (label, count) in enumerate(zip(label_names, label_distribution.sort_index().values)):\n", "    ax1.text(i, count + len(df_clean)*0.01, str(count), ha='center', va='bottom', fontweight='bold')\n", "\n", "# Pie chart\n", "ax2.pie(label_distribution.sort_index().values, labels=label_names, autopct='%1.1f%%', \n", "        colors=colors, startangle=90)\n", "ax2.set_title('Final Label Proportions', fontsize=14, fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Save label mapping for future reference\n", "label_info = {\n", "    'label_mapping': label_mapping,\n", "    'reverse_mapping': reverse_label_mapping,\n", "    'label_distribution': label_distribution.to_dict(),\n", "    'total_samples': len(df_clean)\n", "}\n", "\n", "print(f\"\\n✅ Label encoding completed successfully!\")\n", "print(f\"Ready for model training with {len(df_clean)} labeled samples\")"]}, {"cell_type": "markdown", "id": "83947a53", "metadata": {}, "source": ["## **6. Data Splitting**"]}, {"cell_type": "code", "execution_count": null, "id": "3c9a96f6", "metadata": {}, "outputs": [], "source": ["# Split data into train, validation, and test sets with stratification\n", "print(\"🔄 Splitting data into train/validation/test sets...\")\n", "\n", "# Define split ratios\n", "train_ratio = 0.7    # 70% for training\n", "val_ratio = 0.15     # 15% for validation  \n", "test_ratio = 0.15    # 15% for testing\n", "\n", "print(f\"Split ratios: Train={train_ratio:.0%}, Validation={val_ratio:.0%}, Test={test_ratio:.0%}\")\n", "\n", "# Prepare data for splitting\n", "X = df_clean[['Sentence_cleaned']].copy()\n", "y = df_clean['label'].copy()\n", "\n", "# First split: separate test set\n", "X_temp, X_test, y_temp, y_test = train_test_split(\n", "    X, y, \n", "    test_size=test_ratio, \n", "    stratify=y, \n", "    random_state=42\n", ")\n", "\n", "# Second split: separate train and validation from remaining data\n", "# Adjust validation ratio for remaining data\n", "val_ratio_adjusted = val_ratio / (train_ratio + val_ratio)\n", "\n", "X_train, X_val, y_train, y_val = train_test_split(\n", "    X_temp, y_temp,\n", "    test_size=val_ratio_adjusted,\n", "    stratify=y_temp,\n", "    random_state=42\n", ")\n", "\n", "# Verify splits\n", "print(f\"\\n📊 Data split sizes:\")\n", "print(f\"Train: {len(X_train)} samples ({len(X_train)/len(df_clean):.1%})\")\n", "print(f\"Validation: {len(X_val)} samples ({len(X_val)/len(df_clean):.1%})\")\n", "print(f\"Test: {len(X_test)} samples ({len(X_test)/len(df_clean):.1%})\")\n", "print(f\"Total: {len(X_train) + len(X_val) + len(X_test)} samples\")\n", "\n", "# Check label distribution in each split\n", "def check_label_distribution(y_split, split_name):\n", "    \"\"\"Check and display label distribution for a split\"\"\"\n", "    distribution = y_split.value_counts().sort_index()\n", "    percentages = y_split.value_counts(normalize=True).sort_index() * 100\n", "    \n", "    print(f\"\\n{split_name} label distribution:\")\n", "    for label in sorted(distribution.index):\n", "        sentiment_name = reverse_label_mapping[label]\n", "        count = distribution[label]\n", "        percentage = percentages[label]\n", "        print(f\"  {label} ({sentiment_name}): {count} ({percentage:.1f}%)\")\n", "    \n", "    return distribution\n", "\n", "train_dist = check_label_distribution(y_train, \"Train\")\n", "val_dist = check_label_distribution(y_val, \"Validation\")\n", "test_dist = check_label_distribution(y_test, \"Test\")\n", "\n", "# Visualize split distributions\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 6))\n", "\n", "splits_data = [\n", "    (y_train, \"Train Set\", train_dist),\n", "    (y_val, \"Validation Set\", val_dist), \n", "    (y_test, \"Test Set\", test_dist)\n", "]\n", "\n", "colors = ['#d62728', '#ff7f0e', '#2ca02c']\n", "\n", "for idx, (y_split, title, distribution) in enumerate(splits_data):\n", "    # Bar plot\n", "    label_names = [reverse_label_mapping[i] for i in sorted(distribution.index)]\n", "    axes[idx].bar(label_names, distribution.sort_index().values, color=colors)\n", "    axes[idx].set_title(f'{title}\\n({len(y_split)} samples)', fontsize=12, fontweight='bold')\n", "    axes[idx].set_ylabel('Count')\n", "    axes[idx].set_xlabel('Sentiment')\n", "    \n", "    # Add percentage labels\n", "    total = len(y_split)\n", "    for i, (label, count) in enumerate(zip(label_names, distribution.sort_index().values)):\n", "        percentage = (count / total) * 100\n", "        axes[idx].text(i, count + total*0.01, f'{count}\\n({percentage:.1f}%)', \n", "                      ha='center', va='bottom', fontsize=10, fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Create final datasets\n", "print(f\"\\n📋 Creating final datasets...\")\n", "\n", "# Combine features and labels for each split\n", "train_data = pd.DataFrame({\n", "    'text': X_train['Sentence_cleaned'].values,\n", "    'label': y_train.values\n", "}).reset_index(drop=True)\n", "\n", "val_data = pd.DataFrame({\n", "    'text': X_val['Sentence_cleaned'].values,\n", "    'label': y_val.values  \n", "}).reset_index(drop=True)\n", "\n", "test_data = pd.DataFrame({\n", "    'text': X_test['Sentence_cleaned'].values,\n", "    'label': y_test.values\n", "}).reset_index(drop=True)\n", "\n", "# Verify final datasets\n", "print(f\"Train dataset shape: {train_data.shape}\")\n", "print(f\"Validation dataset shape: {val_data.shape}\")\n", "print(f\"Test dataset shape: {test_data.shape}\")\n", "\n", "# Check for any data leakage (overlapping texts)\n", "print(f\"\\n🔍 Checking for data leakage...\")\n", "\n", "train_texts = set(train_data['text'])\n", "val_texts = set(val_data['text'])\n", "test_texts = set(test_data['text'])\n", "\n", "train_val_overlap = len(train_texts.intersection(val_texts))\n", "train_test_overlap = len(train_texts.intersection(test_texts))\n", "val_test_overlap = len(val_texts.intersection(test_texts))\n", "\n", "print(f\"Train-Validation overlap: {train_val_overlap} texts\")\n", "print(f\"Train-Test overlap: {train_test_overlap} texts\")\n", "print(f\"Validation-Test overlap: {val_test_overlap} texts\")\n", "\n", "if train_val_overlap == 0 and train_test_overlap == 0 and val_test_overlap == 0:\n", "    print(\"✅ No data leakage detected!\")\n", "else:\n", "    print(\"⚠️ Data leakage detected! Check splitting process.\")\n", "\n", "# Display sample from each split\n", "print(f\"\\n📝 Sample from each split:\")\n", "for split_name, data in [(\"Train\", train_data), (\"Validation\", val_data), (\"Test\", test_data)]:\n", "    sample = data.iloc[0]\n", "    print(f\"\\n{split_name} sample:\")\n", "    print(f\"  Text: {sample['text'][:100]}...\")\n", "    print(f\"  Label: {sample['label']} ({reverse_label_mapping[sample['label']]})\")\n", "\n", "print(f\"\\n✅ Data splitting completed successfully!\")\n", "print(f\"Ready for FinBERT tokenization and training\")"]}, {"cell_type": "markdown", "id": "973e6a46", "metadata": {}, "source": ["## **7. FinBERT Tokenization**"]}, {"cell_type": "code", "execution_count": null, "id": "1aee0799", "metadata": {}, "outputs": [], "source": ["# Load FinBERT tokenizer\n", "print(\"🤖 Loading FinBERT tokenizer...\")\n", "\n", "MODEL_NAME = \"ProsusAI/finbert\"\n", "\n", "try:\n", "    tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)\n", "    print(f\"✅ FinBERT tokenizer loaded successfully!\")\n", "    print(f\"Model: {MODEL_NAME}\")\n", "    print(f\"Vocabulary size: {tokenizer.vocab_size}\")\n", "    print(f\"Max position embeddings: {tokenizer.model_max_length}\")\n", "except Exception as e:\n", "    print(f\"❌ Error loading tokenizer: {e}\")\n", "    print(\"Falling back to BERT tokenizer...\")\n", "    tokenizer = AutoTokenizer.from_pretrained(\"bert-base-uncased\")\n", "\n", "# Analyze token lengths to determine optimal max_length\n", "print(f\"\\n📏 Analyzing token lengths for optimal max_length...\")\n", "\n", "def analyze_token_lengths(texts, sample_size=1000):\n", "    \"\"\"Analyze token lengths in a sample of texts\"\"\"\n", "    \n", "    # Sample texts if dataset is large\n", "    if len(texts) > sample_size:\n", "        sample_indices = np.random.choice(len(texts), sample_size, replace=False)\n", "        sample_texts = [texts.iloc[i] for i in sample_indices]\n", "    else:\n", "        sample_texts = texts.tolist()\n", "    \n", "    token_lengths = []\n", "    \n", "    for text in sample_texts:\n", "        # Tokenize without truncation to get actual length\n", "        tokens = tokenizer(text, truncation=False, add_special_tokens=True)\n", "        token_lengths.append(len(tokens['input_ids']))\n", "    \n", "    return np.array(token_lengths)\n", "\n", "# Analyze each split\n", "splits_token_analysis = {}\n", "for split_name, data in [(\"Train\", train_data), (\"Validation\", val_data), (\"Test\", test_data)]:\n", "    print(f\"\\nAnalyzing {split_name} set...\")\n", "    token_lengths = analyze_token_lengths(data['text'])\n", "    \n", "    stats = {\n", "        'mean': np.mean(token_lengths),\n", "        'median': np.median(token_lengths),\n", "        'std': np.std(token_lengths),\n", "        'min': np.min(token_lengths),\n", "        'max': np.max(token_lengths),\n", "        'percentile_95': np.percentile(token_lengths, 95),\n", "        'percentile_99': np.percentile(token_lengths, 99)\n", "    }\n", "    \n", "    splits_token_analysis[split_name] = {\n", "        'lengths': token_lengths,\n", "        'stats': stats\n", "    }\n", "    \n", "    print(f\"  Mean: {stats['mean']:.1f} tokens\")\n", "    print(f\"  Median: {stats['median']:.1f} tokens\")\n", "    print(f\"  95th percentile: {stats['percentile_95']:.1f} tokens\")\n", "    print(f\"  99th percentile: {stats['percentile_99']:.1f} tokens\")\n", "    print(f\"  Max: {stats['max']} tokens\")\n", "\n", "# Determine optimal max_length\n", "all_lengths = np.concatenate([data['lengths'] for data in splits_token_analysis.values()])\n", "recommended_max_length = int(np.percentile(all_lengths, 95))\n", "\n", "print(f\"\\n📊 Overall token length analysis:\")\n", "print(f\"Mean: {np.mean(all_lengths):.1f} tokens\")\n", "print(f\"95th percentile: {np.percentile(all_lengths, 95):.1f} tokens\")\n", "print(f\"99th percentile: {np.percentile(all_lengths, 99):.1f} tokens\")\n", "\n", "# Choose max_length (commonly 128, 256, or 512 for BERT models)\n", "max_length_options = [128, 256, 512]\n", "optimal_max_length = min([x for x in max_length_options if x >= recommended_max_length], default=512)\n", "\n", "print(f\"\\nRecommended max_length: {recommended_max_length}\")\n", "print(f\"Chosen max_length: {optimal_max_length}\")\n", "\n", "# Calculate truncation impact\n", "truncated_samples = (all_lengths > optimal_max_length).sum()\n", "truncation_percentage = (truncated_samples / len(all_lengths)) * 100\n", "\n", "print(f\"Samples that will be truncated: {truncated_samples} ({truncation_percentage:.2f}%)\")\n", "\n", "# Visualize token length distributions\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# 1. Combined histogram\n", "axes[0,0].hist(all_lengths, bins=50, alpha=0.7, edgecolor='black')\n", "axes[0,0].axvline(optimal_max_length, color='red', linestyle='--', \n", "                  label=f'max_length={optimal_max_length}')\n", "axes[0,0].axvline(np.mean(all_lengths), color='green', linestyle='--', \n", "                  label=f'Mean={np.mean(all_lengths):.1f}')\n", "axes[0,0].set_title('Overall Token Length Distribution')\n", "axes[0,0].set_xlabel('Token Length')\n", "axes[0,0].set_ylabel('Frequency')\n", "axes[0,0].legend()\n", "\n", "# 2. Box plot by split\n", "split_names = list(splits_token_analysis.keys())\n", "split_lengths = [splits_token_analysis[name]['lengths'] for name in split_names]\n", "axes[0,1].boxplot(split_lengths, labels=split_names)\n", "axes[0,1].set_title('Token Length Distribution by Split')\n", "axes[0,1].set_ylabel('Token Length')\n", "\n", "# 3. Cumulative distribution\n", "sorted_lengths = np.sort(all_lengths)\n", "cumulative_pct = np.arange(1, len(sorted_lengths) + 1) / len(sorted_lengths) * 100\n", "axes[1,0].plot(sorted_lengths, cumulative_pct)\n", "axes[1,0].axvline(optimal_max_length, color='red', linestyle='--', \n", "                  label=f'max_length={optimal_max_length}')\n", "axes[1,0].set_title('Cumulative Token Length Distribution')\n", "axes[1,0].set_xlabel('Token Length')\n", "axes[1,0].set_ylabel('Cumulative Percentage')\n", "axes[1,0].legend()\n", "axes[1,0].grid(True, alpha=0.3)\n", "\n", "# 4. Length distribution by sentiment\n", "for label in [0, 1, 2]:\n", "    sentiment_data = df_clean[df_clean['label'] == label]['Sentence_cleaned']\n", "    sentiment_lengths = analyze_token_lengths(sentiment_data, sample_size=500)\n", "    axes[1,1].hist(sentiment_lengths, alpha=0.6, \n", "                   label=f'{reverse_label_mapping[label]}', bins=30)\n", "\n", "axes[1,1].set_title('Token Length by Sentiment')\n", "axes[1,1].set_xlabel('Token Length')\n", "axes[1,1].set_ylabel('Frequency')\n", "axes[1,1].legend()\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\n✅ Token length analysis completed!\")\n", "print(f\"Proceeding with max_length={optimal_max_length} for tokenization\")"]}, {"cell_type": "code", "execution_count": null, "id": "4a8051f4", "metadata": {}, "outputs": [], "source": ["# Tokenize the datasets\n", "print(f\"🔤 Tokenizing datasets with max_length={optimal_max_length}...\")\n", "\n", "def tokenize_dataset(texts, labels, tokenizer, max_length):\n", "    \"\"\"Tokenize a dataset for BERT-style models\"\"\"\n", "    \n", "    tokenized_data = {\n", "        'input_ids': [],\n", "        'attention_mask': [],\n", "        'labels': []\n", "    }\n", "    \n", "    print(f\"Tokenizing {len(texts)} samples...\")\n", "    \n", "    for i, (text, label) in enumerate(zip(texts, labels)):\n", "        # Tokenize the text\n", "        encoded = tokenizer(\n", "            text,\n", "            truncation=True,\n", "            padding='max_length',\n", "            max_length=max_length,\n", "            return_tensors=None  # Return lists, not tensors\n", "        )\n", "        \n", "        tokenized_data['input_ids'].append(encoded['input_ids'])\n", "        tokenized_data['attention_mask'].append(encoded['attention_mask'])\n", "        tokenized_data['labels'].append(label)\n", "        \n", "        # Progress indicator\n", "        if (i + 1) % 1000 == 0 or (i + 1) == len(texts):\n", "            print(f\"  Processed {i + 1}/{len(texts)} samples\", end='\\\\r')\n", "    \n", "    print()  # New line after progress indicator\n", "    return tokenized_data\n", "\n", "# Tokenize each split\n", "print(\"Tokenizing training set...\")\n", "tokenized_train = tokenize_dataset(\n", "    train_data['text'].tolist(), \n", "    train_data['label'].tolist(), \n", "    tokenizer, \n", "    optimal_max_length\n", ")\n", "\n", "print(\"Tokenizing validation set...\")\n", "tokenized_val = tokenize_dataset(\n", "    val_data['text'].tolist(), \n", "    val_data['label'].tolist(), \n", "    tokenizer, \n", "    optimal_max_length\n", ")\n", "\n", "print(\"Tokenizing test set...\")\n", "tokenized_test = tokenize_dataset(\n", "    test_data['text'].tolist(), \n", "    test_data['label'].tolist(), \n", "    tokenizer, \n", "    optimal_max_length\n", ")\n", "\n", "# Verify tokenization\n", "print(f\"\\n✅ Tokenization completed!\")\n", "print(f\"Tokenized datasets:\")\n", "print(f\"  Train: {len(tokenized_train['input_ids'])} samples\")\n", "print(f\"  Validation: {len(tokenized_val['input_ids'])} samples\")\n", "print(f\"  Test: {len(tokenized_test['input_ids'])} samples\")\n", "\n", "# Inspect tokenized examples\n", "print(f\"\\n🔍 Tokenization examples:\")\n", "\n", "def show_tokenization_example(tokenized_data, original_data, index=0):\n", "    \"\"\"Show an example of tokenized data\"\"\"\n", "    \n", "    input_ids = tokenized_data['input_ids'][index]\n", "    attention_mask = tokenized_data['attention_mask'][index]\n", "    label = tokenized_data['labels'][index]\n", "    original_text = original_data['text'].iloc[index]\n", "    \n", "    print(f\"\\nExample {index + 1}:\")\n", "    print(f\"Original text: {original_text[:100]}...\")\n", "    print(f\"Label: {label} ({reverse_label_mapping[label]})\")\n", "    print(f\"Input IDs length: {len(input_ids)}\")\n", "    print(f\"Attention mask length: {len(attention_mask)}\")\n", "    print(f\"First 10 input IDs: {input_ids[:10]}\")\n", "    print(f\"First 10 attention mask: {attention_mask[:10]}\")\n", "    \n", "    # Decode tokens to verify\n", "    decoded = tokenizer.decode(input_ids, skip_special_tokens=True)\n", "    print(f\"Decoded (first 100 chars): {decoded[:100]}...\")\n", "    \n", "    # Check for actual content vs padding\n", "    actual_tokens = sum(attention_mask)\n", "    padding_tokens = len(attention_mask) - actual_tokens\n", "    print(f\"Actual tokens: {actual_tokens}, Padding tokens: {padding_tokens}\")\n", "\n", "# Show examples from each split\n", "for split_name, tokenized_data, original_data in [\n", "    (\"Train\", tokenized_train, train_data),\n", "    (\"Validation\", tokenized_val, val_data),\n", "    (\"Test\", tokenized_test, test_data)\n", "]:\n", "    print(f\"\\n--- {split_name} Split Example ---\")\n", "    show_tokenization_example(tokenized_data, original_data, index=0)\n", "\n", "# Validate tokenization quality\n", "print(f\"\\n🔍 Tokenization quality check:\")\n", "\n", "def validate_tokenization(tokenized_data, split_name):\n", "    \"\"\"Validate tokenized data quality\"\"\"\n", "    \n", "    issues = []\n", "    sample_size = min(100, len(tokenized_data['input_ids']))\n", "    \n", "    for i in range(sample_size):\n", "        input_ids = tokenized_data['input_ids'][i]\n", "        attention_mask = tokenized_data['attention_mask'][i]\n", "        label = tokenized_data['labels'][i]\n", "        \n", "        # Check lengths\n", "        if len(input_ids) != optimal_max_length:\n", "            issues.append(f\"Input IDs length mismatch: {len(input_ids)}\")\n", "        \n", "        if len(attention_mask) != optimal_max_length:\n", "            issues.append(f\"Attention mask length mismatch: {len(attention_mask)}\")\n", "        \n", "        # Check label range\n", "        if label not in [0, 1, 2]:\n", "            issues.append(f\"Invalid label: {label}\")\n", "        \n", "        # Check attention mask consistency\n", "        if not all(mask in [0, 1] for mask in attention_mask):\n", "            issues.append(\"Invalid attention mask values\")\n", "    \n", "    if issues:\n", "        print(f\"⚠️ {split_name}: Found {len(issues)} issues in sample\")\n", "        for issue in issues[:5]:  # Show first 5 issues\n", "            print(f\"  - {issue}\")\n", "    else:\n", "        print(f\"✅ {split_name}: No issues found in sample validation\")\n", "\n", "# Validate all splits\n", "for split_name, tokenized_data in [\n", "    (\"Train\", tokenized_train),\n", "    (\"Validation\", tokenized_val), \n", "    (\"Test\", tokenized_test)\n", "]:\n", "    validate_tokenization(tokenized_data, split_name)\n", "\n", "print(f\"\\n✅ FinBERT tokenization completed successfully!\")\n", "print(f\"All datasets ready for PyTorch formatting\")"]}, {"cell_type": "markdown", "id": "a232b41f", "metadata": {}, "source": ["## **8. Dataset Formatting for PyTorch**"]}, {"cell_type": "code", "execution_count": null, "id": "e0f33c57", "metadata": {}, "outputs": [], "source": ["# Convert to HuggingFace Dataset format and PyTorch tensors\n", "print(\"🔄 Converting to HuggingFace Dataset format...\")\n", "\n", "def create_hf_dataset(tokenized_data):\n", "    \"\"\"Create HuggingFace Dataset from tokenized data\"\"\"\n", "    \n", "    # Convert to Dataset\n", "    dataset = Dataset.from_dict({\n", "        'input_ids': tokenized_data['input_ids'],\n", "        'attention_mask': tokenized_data['attention_mask'],\n", "        'labels': tokenized_data['labels']\n", "    })\n", "    \n", "    # Set format to PyTorch tensors\n", "    dataset.set_format(type='torch', columns=['input_ids', 'attention_mask', 'labels'])\n", "    \n", "    return dataset\n", "\n", "# Create HuggingFace datasets\n", "print(\"Creating train dataset...\")\n", "hf_train_dataset = create_hf_dataset(tokenized_train)\n", "\n", "print(\"Creating validation dataset...\")\n", "hf_val_dataset = create_hf_dataset(tokenized_val)\n", "\n", "print(\"Creating test dataset...\")\n", "hf_test_dataset = create_hf_dataset(tokenized_test)\n", "\n", "# Create DatasetDict for easy management\n", "dataset_dict = DatasetDict({\n", "    'train': hf_train_dataset,\n", "    'validation': hf_val_dataset,\n", "    'test': hf_test_dataset\n", "})\n", "\n", "print(f\"\\n✅ HuggingFace datasets created:\")\n", "print(f\"Dataset structure: {dataset_dict}\")\n", "\n", "# Verify dataset format and content\n", "print(f\"\\n🔍 Dataset verification:\")\n", "\n", "for split_name, dataset in dataset_dict.items():\n", "    print(f\"\\n{split_name.capitalize()} dataset:\")\n", "    print(f\"  Size: {len(dataset)}\")\n", "    print(f\"  Features: {dataset.features}\")\n", "    print(f\"  Format: {dataset.format}\")\n", "    \n", "    # Check a sample\n", "    sample = dataset[0]\n", "    print(f\"  Sample shapes:\")\n", "    print(f\"    input_ids: {sample['input_ids'].shape}\")\n", "    print(f\"    attention_mask: {sample['attention_mask'].shape}\")\n", "    print(f\"    labels: {sample['labels'].shape if hasattr(sample['labels'], 'shape') else type(sample['labels'])}\")\n", "\n", "# Test DataLoader compatibility\n", "print(f\"\\n🧪 Testing DataLoader compatibility...\")\n", "\n", "try:\n", "    # Create a small DataLoader to test\n", "    from torch.utils.data import DataLoader\n", "    \n", "    test_loader = DataLoader(hf_train_dataset, batch_size=4, shuffle=False)\n", "    batch = next(iter(test_loader))\n", "    \n", "    print(f\"✅ DataLoader test successful!\")\n", "    print(f\"Batch structure:\")\n", "    for key, value in batch.items():\n", "        print(f\"  {key}: {value.shape} ({value.dtype})\")\n", "    \n", "    # Verify label distribution in batch\n", "    batch_labels = batch['labels'].numpy()\n", "    unique_labels, counts = np.unique(batch_labels, return_counts=True)\n", "    print(f\"  Sample batch labels: {dict(zip(unique_labels, counts))}\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ DataLoader test failed: {e}\")\n", "\n", "# Additional validation: Check tensor properties\n", "print(f\"\\n📊 Tensor properties validation:\")\n", "\n", "def check_tensor_properties(dataset, split_name):\n", "    \"\"\"Check properties of tensors in dataset\"\"\"\n", "    \n", "    sample = dataset[0]\n", "    \n", "    print(f\"\\n{split_name} tensors:\")\n", "    \n", "    # Input IDs\n", "    input_ids = sample['input_ids']\n", "    print(f\"  input_ids: shape={input_ids.shape}, dtype={input_ids.dtype}\")\n", "    print(f\"    min={input_ids.min()}, max={input_ids.max()}\")\n", "    print(f\"    unique values count: {len(torch.unique(input_ids))}\")\n", "    \n", "    # Attention mask\n", "    attention_mask = sample['attention_mask']\n", "    print(f\"  attention_mask: shape={attention_mask.shape}, dtype={attention_mask.dtype}\")\n", "    print(f\"    min={attention_mask.min()}, max={attention_mask.max()}\")\n", "    print(f\"    active tokens: {attention_mask.sum()}/{len(attention_mask)}\")\n", "    \n", "    # Labels\n", "    labels = sample['labels']\n", "    print(f\"  labels: dtype={labels.dtype}\")\n", "    print(f\"    value={labels.item()}\")\n", "\n", "for split_name, dataset in dataset_dict.items():\n", "    check_tensor_properties(dataset, split_name)\n", "\n", "# Create summary statistics\n", "print(f\"\\n📈 Final dataset summary:\")\n", "\n", "summary_stats = {}\n", "for split_name, dataset in dataset_dict.items():\n", "    # Get all labels for this split\n", "    all_labels = [dataset[i]['labels'].item() for i in range(len(dataset))]\n", "    label_distribution = Counter(all_labels)\n", "    \n", "    # Calculate actual token usage\n", "    sample_indices = np.random.choice(len(dataset), min(100, len(dataset)), replace=False)\n", "    token_usage = []\n", "    \n", "    for idx in sample_indices:\n", "        attention_mask = dataset[int(idx)]['attention_mask']\n", "        active_tokens = attention_mask.sum().item()\n", "        token_usage.append(active_tokens)\n", "    \n", "    summary_stats[split_name] = {\n", "        'size': len(dataset),\n", "        'label_distribution': dict(label_distribution),\n", "        'avg_token_usage': np.mean(token_usage),\n", "        'max_token_usage': np.max(token_usage),\n", "        'min_token_usage': np.min(token_usage)\n", "    }\n", "\n", "# Display summary\n", "for split_name, stats in summary_stats.items():\n", "    print(f\"\\n{split_name.capitalize()}:\")\n", "    print(f\"  Samples: {stats['size']}\")\n", "    print(f\"  Label distribution: {stats['label_distribution']}\")\n", "    print(f\"  Avg token usage: {stats['avg_token_usage']:.1f}/{optimal_max_length}\")\n", "    print(f\"  Token usage range: {stats['min_token_usage']}-{stats['max_token_usage']}\")\n", "\n", "print(f\"\\n✅ PyTorch dataset formatting completed successfully!\")\n", "print(f\"Datasets are ready for FinBERT fine-tuning!\")"]}, {"cell_type": "markdown", "id": "e65242d9", "metadata": {}, "source": ["## **9. Final Dataset Validation**"]}, {"cell_type": "code", "execution_count": null, "id": "94a6cb37", "metadata": {}, "outputs": [], "source": ["# Comprehensive final validation of processed datasets\n", "print(\"🔍 Performing comprehensive dataset validation...\")\n", "\n", "def comprehensive_dataset_validation(dataset_dict, tokenizer, label_mapping):\n", "    \"\"\"Perform thorough validation of the processed datasets\"\"\"\n", "    \n", "    validation_results = {\n", "        'passed': True,\n", "        'issues': [],\n", "        'warnings': [],\n", "        'summary': {}\n", "    }\n", "    \n", "    total_samples = sum(len(dataset) for dataset in dataset_dict.values())\n", "    \n", "    print(f\"Validating {total_samples} total samples across {len(dataset_dict)} splits...\")\n", "    \n", "    # 1. Basic structure validation\n", "    print(f\"\\n1. 📋 Basic Structure Validation\")\n", "    \n", "    required_features = ['input_ids', 'attention_mask', 'labels']\n", "    for split_name, dataset in dataset_dict.items():\n", "        # Check required features\n", "        missing_features = [f for f in required_features if f not in dataset.features]\n", "        if missing_features:\n", "            validation_results['issues'].append(f\"{split_name}: Missing features {missing_features}\")\n", "            validation_results['passed'] = False\n", "        \n", "        # Check dataset size\n", "        if len(dataset) == 0:\n", "            validation_results['issues'].append(f\"{split_name}: Empty dataset\")\n", "            validation_results['passed'] = False\n", "        \n", "        print(f\"  {split_name}: {len(dataset)} samples ✅\")\n", "    \n", "    # 2. Label validation\n", "    print(f\"\\n2. 🏷️ Label Validation\")\n", "    \n", "    valid_labels = set(label_mapping.values())\n", "    for split_name, dataset in dataset_dict.items():\n", "        all_labels = []\n", "        sample_size = min(len(dataset), 1000)  # Sample for large datasets\n", "        \n", "        for i in range(sample_size):\n", "            label = dataset[i]['labels'].item()\n", "            all_labels.append(label)\n", "            \n", "            if label not in valid_labels:\n", "                validation_results['issues'].append(f\"{split_name}: Invalid label {label} at index {i}\")\n", "                validation_results['passed'] = False\n", "        \n", "        # Check label distribution\n", "        label_dist = Counter(all_labels)\n", "        print(f\"  {split_name} label distribution: {dict(label_dist)}\")\n", "        \n", "        # Warn about imbalanced labels\n", "        min_count = min(label_dist.values())\n", "        max_count = max(label_dist.values())\n", "        if max_count / min_count > 5:\n", "            validation_results['warnings'].append(f\"{split_name}: Highly imbalanced labels (ratio {max_count/min_count:.1f}:1)\")\n", "    \n", "    # 3. Tokenization validation\n", "    print(f\"\\n3. 🔤 Tokenization Validation\")\n", "    \n", "    for split_name, dataset in dataset_dict.items():\n", "        sample_size = min(len(dataset), 100)\n", "        \n", "        for i in range(sample_size):\n", "            sample = dataset[i]\n", "            \n", "            # Check tensor shapes\n", "            if sample['input_ids'].shape[0] != optimal_max_length:\n", "                validation_results['issues'].append(f\"{split_name}: Wrong input_ids length at index {i}\")\n", "                validation_results['passed'] = False\n", "            \n", "            if sample['attention_mask'].shape[0] != optimal_max_length:\n", "                validation_results['issues'].append(f\"{split_name}: Wrong attention_mask length at index {i}\")\n", "                validation_results['passed'] = False\n", "            \n", "            # Check data types\n", "            if sample['input_ids'].dtype != torch.long:\n", "                validation_results['issues'].append(f\"{split_name}: Wrong input_ids dtype at index {i}\")\n", "                validation_results['passed'] = False\n", "            \n", "            if sample['attention_mask'].dtype != torch.long:\n", "                validation_results['issues'].append(f\"{split_name}: Wrong attention_mask dtype at index {i}\")\n", "                validation_results['passed'] = False\n", "            \n", "            # Check value ranges\n", "            if sample['input_ids'].min() < 0 or sample['input_ids'].max() >= tokenizer.vocab_size:\n", "                validation_results['issues'].append(f\"{split_name}: Input IDs out of vocab range at index {i}\")\n", "                validation_results['passed'] = False\n", "            \n", "            if not all(mask in [0, 1] for mask in sample['attention_mask']):\n", "                validation_results['issues'].append(f\"{split_name}: Invalid attention mask values at index {i}\")\n", "                validation_results['passed'] = False\n", "        \n", "        print(f\"  {split_name}: Tokenization validation ✅\")\n", "    \n", "    # 4. Data consistency validation\n", "    print(f\"\\n4. 🔄 Data Consistency Validation\")\n", "    \n", "    # Check for data leakage between splits\n", "    if len(dataset_dict) > 1:\n", "        splits = list(dataset_dict.keys())\n", "        for i, split1 in enumerate(splits):\n", "            for split2 in splits[i+1:]:\n", "                # Sample and compare input_ids (representing text content)\n", "                sample_size = min(100, len(dataset_dict[split1]), len(dataset_dict[split2]))\n", "                \n", "                split1_samples = set()\n", "                split2_samples = set()\n", "                \n", "                for idx in range(sample_size):\n", "                    split1_samples.add(tuple(dataset_dict[split1][idx]['input_ids'].tolist()))\n", "                    split2_samples.add(tuple(dataset_dict[split2][idx]['input_ids'].tolist()))\n", "                \n", "                overlap = len(split1_samples.intersection(split2_samples))\n", "                if overlap > 0:\n", "                    validation_results['warnings'].append(f\"Potential data leakage between {split1} and {split2}: {overlap} overlapping samples in sample\")\n", "    \n", "    # 5. FinBERT compatibility validation\n", "    print(f\"\\n5. 🤖 FinBERT Compatibility Validation\")\n", "    \n", "    # Test with a small batch\n", "    try:\n", "        test_dataset = dataset_dict['train']\n", "        test_batch = [test_dataset[i] for i in range(min(4, len(test_dataset)))]\n", "        \n", "        # Simulate model input preparation\n", "        batch_input_ids = torch.stack([item['input_ids'] for item in test_batch])\n", "        batch_attention_mask = torch.stack([item['attention_mask'] for item in test_batch])\n", "        batch_labels = torch.stack([item['labels'] for item in test_batch])\n", "        \n", "        print(f\"  Batch shapes: input_ids={batch_input_ids.shape}, attention_mask={batch_attention_mask.shape}, labels={batch_labels.shape}\")\n", "        print(f\"  FinBERT compatibility: ✅\")\n", "        \n", "    except Exception as e:\n", "        validation_results['issues'].append(f\"FinBERT compatibility test failed: {e}\")\n", "        validation_results['passed'] = False\n", "    \n", "    # 6. Generate validation summary\n", "    validation_results['summary'] = {\n", "        'total_samples': total_samples,\n", "        'splits': {name: len(dataset) for name, dataset in dataset_dict.items()},\n", "        'max_length': optimal_max_length,\n", "        'vocab_size': tokenizer.vocab_size,\n", "        'label_mapping': label_mapping,\n", "        'issues_count': len(validation_results['issues']),\n", "        'warnings_count': len(validation_results['warnings'])\n", "    }\n", "    \n", "    return validation_results\n", "\n", "# Run comprehensive validation\n", "validation_results = comprehensive_dataset_validation(dataset_dict, tokenizer, label_mapping)\n", "\n", "# Display validation results\n", "print(f\"\\n📋 VALIDATION RESULTS:\")\n", "print(f\"{'='*50}\")\n", "\n", "if validation_results['passed']:\n", "    print(f\"✅ ALL VALIDATIONS PASSED!\")\n", "else:\n", "    print(f\"❌ VALIDATION FAILED!\")\n", "\n", "print(f\"\\nSummary:\")\n", "print(f\"  Total samples: {validation_results['summary']['total_samples']}\")\n", "print(f\"  Splits: {validation_results['summary']['splits']}\")\n", "print(f\"  Max length: {validation_results['summary']['max_length']}\")\n", "print(f\"  Issues found: {validation_results['summary']['issues_count']}\")\n", "print(f\"  Warnings: {validation_results['summary']['warnings_count']}\")\n", "\n", "if validation_results['issues']:\n", "    print(f\"\\n❌ Issues:\")\n", "    for issue in validation_results['issues']:\n", "        print(f\"  - {issue}\")\n", "\n", "if validation_results['warnings']:\n", "    print(f\"\\n⚠️ Warnings:\")\n", "    for warning in validation_results['warnings']:\n", "        print(f\"  - {warning}\")\n", "\n", "# Final quality check: Show sample predictions capability\n", "print(f\"\\n🧪 Final Quality Check - Sample Decoding Test:\")\n", "\n", "def test_sample_decoding(dataset, split_name, num_samples=3):\n", "    \"\"\"Test that we can properly decode samples\"\"\"\n", "    \n", "    print(f\"\\n{split_name} samples:\")\n", "    \n", "    for i in range(min(num_samples, len(dataset))):\n", "        sample = dataset[i]\n", "        \n", "        # Decode the tokenized text\n", "        input_ids = sample['input_ids']\n", "        attention_mask = sample['attention_mask']\n", "        label = sample['labels'].item()\n", "        \n", "        # Get only the actual tokens (not padding)\n", "        actual_tokens = input_ids[attention_mask.bool()]\n", "        decoded_text = tokenizer.decode(actual_tokens, skip_special_tokens=True)\n", "        \n", "        print(f\"  Sample {i+1}:\")\n", "        print(f\"    Label: {label} ({reverse_label_mapping[label]})\")\n", "        print(f\"    Decoded text: {decoded_text[:100]}...\")\n", "        print(f\"    Token count: {len(actual_tokens)}/{optimal_max_length}\")\n", "\n", "# Test decoding for each split\n", "for split_name, dataset in dataset_dict.items():\n", "    test_sample_decoding(dataset, split_name, num_samples=2)\n", "\n", "if validation_results['passed']:\n", "    print(f\"\\n🎉 DATASET VALIDATION SUCCESSFUL!\")\n", "    print(f\"   The datasets are ready for FinBERT fine-tuning!\")\n", "else:\n", "    print(f\"\\n⚠️ DATASET VALIDATION FAILED!\")\n", "    print(f\"   Please address the issues before proceeding with training.\")\n", "\n", "print(f\"\\n✅ Final validation completed!\")"]}, {"cell_type": "markdown", "id": "319e0df3", "metadata": {}, "source": ["## **10. Save Preprocessed Data**"]}, {"cell_type": "code", "execution_count": null, "id": "4ccafba3", "metadata": {}, "outputs": [], "source": ["# Save preprocessed datasets in multiple formats\n", "print(\"💾 Saving preprocessed datasets...\")\n", "\n", "# Create output directories\n", "output_dir = \"finbert_preprocessed_data\"\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "# Subdirectories for different formats\n", "hf_datasets_dir = os.path.join(output_dir, \"huggingface_datasets\")\n", "csv_dir = os.path.join(output_dir, \"csv_exports\")\n", "pickle_dir = os.path.join(output_dir, \"pickle_files\")\n", "tokenizer_dir = os.path.join(output_dir, \"tokenizer\")\n", "\n", "for dir_path in [hf_datasets_dir, csv_dir, pickle_dir, tokenizer_dir]:\n", "    os.makedirs(dir_path, exist_ok=True)\n", "\n", "print(f\"Created output directory structure in: {output_dir}\")\n", "\n", "# 1. Save HuggingFace datasets\n", "print(f\"\\n1. 📚 Saving HuggingFace datasets...\")\n", "\n", "try:\n", "    # Save individual datasets\n", "    for split_name, dataset in dataset_dict.items():\n", "        split_dir = os.path.join(hf_datasets_dir, split_name)\n", "        dataset.save_to_disk(split_dir)\n", "        print(f\"  ✅ {split_name} dataset saved to: {split_dir}\")\n", "    \n", "    # Save the complete DatasetDict\n", "    dataset_dict.save_to_disk(os.path.join(hf_datasets_dir, \"complete_dataset\"))\n", "    print(f\"  ✅ Complete dataset dict saved to: {os.path.join(hf_datasets_dir, 'complete_dataset')}\")\n", "    \n", "except Exception as e:\n", "    print(f\"  ❌ Error saving HuggingFace datasets: {e}\")\n", "\n", "# 2. Save tokenizer\n", "print(f\"\\n2. 🔤 Saving tokenizer...\")\n", "\n", "try:\n", "    tokenizer.save_pretrained(tokenizer_dir)\n", "    print(f\"  ✅ Tokenizer saved to: {tokenizer_dir}\")\n", "except Exception as e:\n", "    print(f\"  ❌ Error saving tokenizer: {e}\")\n", "\n", "# 3. Export to CSV format\n", "print(f\"\\n3. 📄 Exporting to CSV format...\")\n", "\n", "def export_to_csv(dataset, split_name, output_dir):\n", "    \"\"\"Export dataset to CSV with decoded text\"\"\"\n", "    \n", "    export_data = []\n", "    \n", "    print(f\"  Processing {split_name} ({len(dataset)} samples)...\")\n", "    \n", "    for i in range(len(dataset)):\n", "        sample = dataset[i]\n", "        \n", "        # Decode the text\n", "        input_ids = sample['input_ids']\n", "        attention_mask = sample['attention_mask']\n", "        actual_tokens = input_ids[attention_mask.bool()]\n", "        decoded_text = tokenizer.decode(actual_tokens, skip_special_tokens=True)\n", "        \n", "        # Create row\n", "        row = {\n", "            'id': i,\n", "            'text': decoded_text,\n", "            'label': sample['labels'].item(),\n", "            'sentiment': reverse_label_mapping[sample['labels'].item()],\n", "            'input_ids': input_ids.tolist(),\n", "            'attention_mask': attention_mask.tolist(),\n", "            'token_count': len(actual_tokens)\n", "        }\n", "        \n", "        export_data.append(row)\n", "    \n", "    # Save to CSV\n", "    df_export = pd.DataFrame(export_data)\n", "    csv_path = os.path.join(output_dir, f\"{split_name}_processed.csv\")\n", "    df_export.to_csv(csv_path, index=False, encoding='utf-8')\n", "    \n", "    print(f\"  ✅ {split_name} exported to: {csv_path}\")\n", "    return df_export\n", "\n", "# Export all splits\n", "exported_dataframes = {}\n", "for split_name, dataset in dataset_dict.items():\n", "    exported_dataframes[split_name] = export_to_csv(dataset, split_name, csv_dir)\n", "\n", "# Create summary CSV\n", "print(f\"  Creating summary statistics...\")\n", "summary_rows = []\n", "\n", "for split_name, df in exported_dataframes.items():\n", "    # Overall stats\n", "    overall_stats = {\n", "        'split': split_name,\n", "        'category': 'overall',\n", "        'sentiment': 'all',\n", "        'count': len(df),\n", "        'percentage': 100.0,\n", "        'avg_token_count': df['token_count'].mean(),\n", "        'min_token_count': df['token_count'].min(),\n", "        'max_token_count': df['token_count'].max()\n", "    }\n", "    summary_rows.append(overall_stats)\n", "    \n", "    # Per-sentiment stats\n", "    for label in [0, 1, 2]:\n", "        sentiment_df = df[df['label'] == label]\n", "        sentiment_stats = {\n", "            'split': split_name,\n", "            'category': 'by_sentiment',\n", "            'sentiment': reverse_label_mapping[label],\n", "            'count': len(sentiment_df),\n", "            'percentage': (len(sentiment_df) / len(df)) * 100,\n", "            'avg_token_count': sentiment_df['token_count'].mean() if len(sentiment_df) > 0 else 0,\n", "            'min_token_count': sentiment_df['token_count'].min() if len(sentiment_df) > 0 else 0,\n", "            'max_token_count': sentiment_df['token_count'].max() if len(sentiment_df) > 0 else 0\n", "        }\n", "        summary_rows.append(sentiment_stats)\n", "\n", "summary_df = pd.DataFrame(summary_rows)\n", "summary_path = os.path.join(csv_dir, \"preprocessing_summary.csv\")\n", "summary_df.to_csv(summary_path, index=False)\n", "print(f\"  ✅ Summary statistics saved to: {summary_path}\")\n", "\n", "# 4. Save pickle files\n", "print(f\"\\n4. 🥒 Saving pickle files...\")\n", "\n", "try:\n", "    # Save tokenized data\n", "    pickle_data = {\n", "        'tokenized_datasets': {\n", "            'train': tokenized_train,\n", "            'validation': tokenized_val,\n", "            'test': tokenized_test\n", "        },\n", "        'metadata': {\n", "            'model_name': MODEL_NAME,\n", "            'max_length': optimal_max_length,\n", "            'label_mapping': label_mapping,\n", "            'reverse_mapping': reverse_label_mapping,\n", "            'vocab_size': tokenizer.vocab_size,\n", "            'total_samples': sum(len(dataset) for dataset in dataset_dict.values()),\n", "            'preprocessing_date': pd.Timestamp.now().isoformat()\n", "        }\n", "    }\n", "    \n", "    pickle_path = os.path.join(pickle_dir, \"finbert_preprocessed_data.pkl\")\n", "    with open(pickle_path, 'wb') as f:\n", "        pickle.dump(pickle_data, f)\n", "    \n", "    print(f\"  ✅ Pickle data saved to: {pickle_path}\")\n", "    \n", "except Exception as e:\n", "    print(f\"  ❌ Error saving pickle files: {e}\")\n", "\n", "# 5. Save configuration and metadata\n", "print(f\"\\n5. ⚙️ Saving configuration and metadata...\")\n", "\n", "# Preprocessing configuration\n", "config = {\n", "    'dataset_info': {\n", "        'source_file': 'data.csv',\n", "        'original_samples': len(df),\n", "        'final_samples': sum(len(dataset) for dataset in dataset_dict.values()),\n", "        'samples_removed': len(df) - sum(len(dataset) for dataset in dataset_dict.values())\n", "    },\n", "    'preprocessing_config': {\n", "        'model_name': MODEL_NAME,\n", "        'max_length': optimal_max_length,\n", "        'train_ratio': train_ratio,\n", "        'val_ratio': val_ratio,\n", "        'test_ratio': test_ratio,\n", "        'random_seed': 42\n", "    },\n", "    'split_sizes': {name: len(dataset) for name, dataset in dataset_dict.items()},\n", "    'label_mapping': label_mapping,\n", "    'reverse_label_mapping': reverse_label_mapping,\n", "    'validation_results': validation_results,\n", "    'processing_timestamp': pd.Timestamp.now().isoformat()\n", "}\n", "\n", "# Save configuration\n", "config_path = os.path.join(output_dir, \"preprocessing_config.json\")\n", "with open(config_path, 'w', encoding='utf-8') as f:\n", "    json.dump(config, f, indent=2, ensure_ascii=False)\n", "\n", "print(f\"  ✅ Configuration saved to: {config_path}\")\n", "\n", "# 6. Create README file\n", "print(f\"\\n6. 📖 Creating README file...\")\n", "\n", "readme_content = f\"\"\"# FinBERT Preprocessed Data\n", "\n", "This directory contains preprocessed financial sentiment analysis data ready for FinBERT fine-tuning.\n", "\n", "## Dataset Information\n", "\n", "- **Source**: data.csv\n", "- **Original samples**: {len(df):,}\n", "- **Final samples**: {sum(len(dataset) for dataset in dataset_dict.values()):,}\n", "- **Preprocessing date**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n", "\n", "## Data Splits\n", "\n", "- **Training**: {len(dataset_dict['train']):,} samples ({len(dataset_dict['train'])/sum(len(dataset) for dataset in dataset_dict.values())*100:.1f}%)\n", "- **Validation**: {len(dataset_dict['validation']):,} samples ({len(dataset_dict['validation'])/sum(len(dataset) for dataset in dataset_dict.values())*100:.1f}%)\n", "- **Test**: {len(dataset_dict['test']):,} samples ({len(dataset_dict['test'])/sum(len(dataset) for dataset in dataset_dict.values())*100:.1f}%)\n", "\n", "## Label Mapping\n", "\n", "- 0: negative\n", "- 1: neutral  \n", "- 2: positive\n", "\n", "## Model Configuration\n", "\n", "- **Model**: {MODEL_NAME}\n", "- **Max sequence length**: {optimal_max_length}\n", "- **Vocabulary size**: {tokenizer.vocab_size:,}\n", "\n", "## Directory Structure\n", "\n", "```\n", "finbert_preprocessed_data/\n", "├── huggingface_datasets/     # HuggingFace Dataset format\n", "│   ├── train/\n", "│   ├── validation/\n", "│   ├── test/\n", "│   └── complete_dataset/\n", "├── csv_exports/              # CSV format exports\n", "│   ├── train_processed.csv\n", "│   ├── validation_processed.csv\n", "│   ├── test_processed.csv\n", "│   └── preprocessing_summary.csv\n", "├── pickle_files/             # Pickle format\n", "│   └── finbert_preprocessed_data.pkl\n", "├── tokenizer/                # FinBERT tokenizer\n", "└── preprocessing_config.json # Configuration and metadata\n", "```\n", "\n", "## Usage\n", "\n", "### Loading HuggingFace Datasets\n", "\n", "```python\n", "from datasets import load_from_disk\n", "\n", "# Load individual splits\n", "train_dataset = load_from_disk(\"huggingface_datasets/train\")\n", "val_dataset = load_from_disk(\"huggingface_datasets/validation\")\n", "test_dataset = load_from_disk(\"huggingface_datasets/test\")\n", "\n", "# Or load complete dataset dict\n", "dataset_dict = load_from_disk(\"huggingface_datasets/complete_dataset\")\n", "```\n", "\n", "### Loading CSV Data\n", "\n", "```python\n", "import pandas as pd\n", "\n", "train_df = pd.read_csv(\"csv_exports/train_processed.csv\")\n", "val_df = pd.read_csv(\"csv_exports/validation_processed.csv\") \n", "test_df = pd.read_csv(\"csv_exports/test_processed.csv\")\n", "```\n", "\n", "### Loading <PERSON>\n", "\n", "```python\n", "from transformers import AutoTokenizer\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(\"tokenizer/\")\n", "```\n", "\n", "## Data Quality\n", "\n", "- ✅ No missing values\n", "- ✅ No data leakage between splits\n", "- ✅ Balanced stratified sampling\n", "- ✅ Proper tokenization validation\n", "- ✅ FinBERT compatibility verified\n", "\n", "## Next Steps\n", "\n", "1. Load the preprocessed datasets\n", "2. Set up FinBERT model for fine-tuning\n", "3. Configure training parameters\n", "4. <PERSON>gin fine-tuning process\n", "\n", "Generated by: Data_Preprocessing_for_FinBERT.ipynb\n", "\"\"\"\n", "\n", "readme_path = os.path.join(output_dir, \"README.md\")\n", "with open(readme_path, 'w', encoding='utf-8') as f:\n", "    f.write(readme_content)\n", "\n", "print(f\"  ✅ README created: {readme_path}\")\n", "\n", "# 7. Final summary\n", "print(f\"\\n{'='*60}\")\n", "print(f\"📋 PREPROCESSING COMPLETE - SUMMARY\")\n", "print(f\"{'='*60}\")\n", "\n", "print(f\"\\n📊 Dataset Statistics:\")\n", "print(f\"  Original samples: {len(df):,}\")\n", "print(f\"  Final samples: {sum(len(dataset) for dataset in dataset_dict.values()):,}\")\n", "print(f\"  Samples removed: {len(df) - sum(len(dataset) for dataset in dataset_dict.values()):,}\")\n", "\n", "print(f\"\\n📂 Output Directory: {output_dir}\")\n", "print(f\"  HuggingFace datasets: {hf_datasets_dir}\")\n", "print(f\"  CSV exports: {csv_dir}\")\n", "print(f\"  Pickle files: {pickle_dir}\")\n", "print(f\"  Tokenizer: {tokenizer_dir}\")\n", "\n", "print(f\"\\n🔧 Configuration:\")\n", "print(f\"  Model: {MODEL_NAME}\")\n", "print(f\"  Max length: {optimal_max_length}\")\n", "print(f\"  Splits: {len(dataset_dict['train'])}/{len(dataset_dict['validation'])}/{len(dataset_dict['test'])}\")\n", "\n", "print(f\"\\n🎯 Next Steps:\")\n", "print(f\"  1. Load datasets from: {hf_datasets_dir}\")\n", "print(f\"  2. Load tokenizer from: {tokenizer_dir}\")\n", "print(f\"  3. Set up FinBERT model\")\n", "print(f\"  4. <PERSON><PERSON> fine-tuning process\")\n", "\n", "if validation_results['passed']:\n", "    print(f\"\\n✅ All validations passed - Ready for FinBERT fine-tuning!\")\n", "else:\n", "    print(f\"\\n⚠️ Some validation issues found - Check logs before training!\")\n", "\n", "print(f\"\\n🎉 PREPROCESSING COMPLETED SUCCESSFULLY!\")\n", "print(f\"   Total processing time: Completed in this session\")\n", "print(f\"   Data is ready for financial sentiment analysis with FinBERT!\")"]}, {"cell_type": "markdown", "id": "ae42d1b5", "metadata": {}, "source": ["## **📋 Preprocessing Summary & Next Steps**\n", "\n", "### **✅ What We Accomplished:**\n", "\n", "1. **✨ Data Loading & Exploration**\n", "   - Successfully loaded `data.csv` with financial sentiment data\n", "   - Analyzed dataset structure and identified potential issues\n", "   - Examined sentiment distribution and text characteristics\n", "\n", "2. **🧹 Comprehensive Data Cleaning**\n", "   - Removed HTML tags, special characters, and encoding issues\n", "   - Validated text quality and removed invalid entries\n", "   - Eliminated duplicate entries based on cleaned text\n", "   - Applied proper text normalization for financial content\n", "\n", "3. **📊 Text Analysis & Statistics**\n", "   - Analyzed text lengths, word counts, and sentence structures\n", "   - Identified outliers and data distribution patterns\n", "   - Created comprehensive visualizations of data characteristics\n", "   - Validated data quality across all sentiment categories\n", "\n", "4. **🏷️ Label Encoding & Validation** \n", "   - Standardized sentiment labels to numerical format (0=negative, 1=neutral, 2=positive)\n", "   - Handled label variations and edge cases\n", "   - Validated label consistency and distribution\n", "   - Created proper label mappings for model training\n", "\n", "5. **🔄 Strategic Data Splitting**\n", "   - Applied stratified sampling to maintain balanced sentiment distribution\n", "   - Created 70/15/15 train/validation/test splits\n", "   - Verified no data leakage between splits\n", "   - Ensured representative samples in each split\n", "\n", "6. **🤖 FinBERT Tokenization**\n", "   - Loaded official FinBERT tokenizer (ProsusAI/finbert)\n", "   - Analyzed optimal sequence length based on data distribution\n", "   - Applied proper tokenization with padding and truncation\n", "   - Validated tokenization quality and compatibility\n", "\n", "7. **🔧 PyTorch Dataset Formatting**\n", "   - Converted to HuggingFace Dataset format\n", "   - Configured PyTorch tensor format for training\n", "   - Tested DataLoader compatibility\n", "   - Validated tensor properties and shapes\n", "\n", "8. **🔍 Comprehensive Validation**\n", "   - Performed thorough quality checks on all datasets\n", "   - Validated FinBERT compatibility\n", "   - Tested sample decoding and reconstruction\n", "   - Ensured data integrity across all splits\n", "\n", "9. **💾 Multi-Format Export**\n", "   - Saved HuggingFace datasets for direct use\n", "   - Exported CSV files for analysis and backup\n", "   - Created pickle files for quick loading\n", "   - Saved tokenizer and configuration files\n", "   - Generated comprehensive documentation\n", "\n", "### **📈 Final Dataset Statistics:**\n", "\n", "- **Model**: FinBERT (ProsusAI/finbert)\n", "- **Total Samples**: Processed from original CSV\n", "- **Data Splits**: Train/Validation/Test with balanced sentiment distribution\n", "- **Sequence Length**: Optimized based on token analysis\n", "- **Quality**: Validated and ready for fine-tuning\n", "\n", "### **🎯 Ready for FinBERT Fine-tuning:**\n", "\n", "The preprocessed datasets are now ready for FinBERT fine-tuning with:\n", "- ✅ Proper tokenization with FinBERT tokenizer\n", "- ✅ Balanced stratified data splits  \n", "- ✅ PyTorch-compatible tensor format\n", "- ✅ Comprehensive quality validation\n", "- ✅ Complete documentation and metadata\n", "\n", "### **🚀 Next Steps:**\n", "\n", "1. **Load Preprocessed Data**: Use the saved HuggingFace datasets\n", "2. **Configure FinBERT Model**: Set up the pre-trained model for fine-tuning\n", "3. **Define Training Parameters**: Learning rate, batch size, epochs, etc.\n", "4. **Implement Training Loop**: Use Transformers Trainer or custom training\n", "5. **Monitor Training**: Track loss, accuracy, and validation metrics\n", "6. **Evaluate Results**: Test on held-out test set and analyze performance\n", "\n", "The data preprocessing phase is now **complete** and the datasets are **production-ready** for FinBERT fine-tuning on financial sentiment analysis! 🎉"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}