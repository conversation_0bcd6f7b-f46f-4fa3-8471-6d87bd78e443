"""
Logging utilities for FinBERT training.
"""

import logging
import os
import sys
from datetime import datetime
from typing import Optional

def setup_logging(log_dir: str = "logs",
                 log_level: str = "INFO",
                 log_to_file: bool = True,
                 log_to_console: bool = True) -> logging.Logger:
    """
    Set up logging configuration.
    
    Args:
        log_dir: Directory to save log files
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_to_file: Whether to log to file
        log_to_console: Whether to log to console
        
    Returns:
        Configured logger
    """
    # Create log directory if it doesn't exist
    if log_to_file and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Create logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    logger.handlers = []
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    if log_to_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, log_level.upper()))
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # File handler
    if log_to_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = os.path.join(log_dir, f"finbert_training_{timestamp}.log")
        
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(getattr(logging, log_level.upper()))
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        logger.info(f"Logging to file: {log_file}")
    
    return logger

class TrainingLogger:
    """
    Custom logger for training progress and metrics.
    """
    
    def __init__(self, log_dir: str = "logs"):
        """
        Initialize training logger.
        
        Args:
            log_dir: Directory to save training logs
        """
        self.log_dir = log_dir
        self.logger = logging.getLogger(self.__class__.__name__)
        
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # Training metrics file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.metrics_file = os.path.join(log_dir, f"training_metrics_{timestamp}.txt")
        
    def log_epoch_start(self, epoch: int, total_epochs: int):
        """Log the start of an epoch."""
        self.logger.info(f"Starting Epoch {epoch + 1}/{total_epochs}")
        with open(self.metrics_file, 'a') as f:
            f.write(f"\n{'='*50}\n")
            f.write(f"EPOCH {epoch + 1}/{total_epochs} - {datetime.now()}\n")
            f.write(f"{'='*50}\n")
    
    def log_training_step(self, step: int, loss: float, lr: float):
        """Log training step metrics."""
        message = f"Step {step}: Loss = {loss:.4f}, LR = {lr:.2e}"
        self.logger.debug(message)
        
        with open(self.metrics_file, 'a') as f:
            f.write(f"Step {step}: Loss = {loss:.4f}, LR = {lr:.2e}\n")
    
    def log_epoch_end(self, epoch: int, train_loss: float, val_loss: Optional[float] = None, 
                     val_metrics: Optional[dict] = None):
        """Log the end of an epoch with metrics."""
        message = f"Epoch {epoch + 1} Complete - Train Loss: {train_loss:.4f}"
        if val_loss is not None:
            message += f", Val Loss: {val_loss:.4f}"
        if val_metrics:
            message += f", Val Accuracy: {val_metrics.get('accuracy', 0):.4f}"
            message += f", Val F1: {val_metrics.get('f1', 0):.4f}"
        
        self.logger.info(message)
        
        with open(self.metrics_file, 'a') as f:
            f.write(f"\nEpoch {epoch + 1} Summary:\n")
            f.write(f"  Train Loss: {train_loss:.4f}\n")
            if val_loss is not None:
                f.write(f"  Validation Loss: {val_loss:.4f}\n")
            if val_metrics:
                for metric, value in val_metrics.items():
                    f.write(f"  Val {metric.capitalize()}: {value:.4f}\n")
            f.write(f"  Timestamp: {datetime.now()}\n")
    
    def log_training_complete(self, total_time: float, best_metrics: dict):
        """Log training completion."""
        message = f"Training Complete! Total time: {total_time:.2f}s"
        self.logger.info(message)
        
        with open(self.metrics_file, 'a') as f:
            f.write(f"\n{'='*50}\n")
            f.write(f"TRAINING COMPLETE - {datetime.now()}\n")
            f.write(f"{'='*50}\n")
            f.write(f"Total Training Time: {total_time:.2f} seconds\n")
            f.write(f"\nBest Metrics:\n")
            for metric, value in best_metrics.items():
                f.write(f"  {metric.capitalize()}: {value:.4f}\n")
    
    def log_model_save(self, save_path: str, metrics: dict):
        """Log model saving."""
        message = f"Model saved to: {save_path}"
        self.logger.info(message)
        
        with open(self.metrics_file, 'a') as f:
            f.write(f"\nModel Saved: {save_path}\n")
            f.write(f"Final Metrics:\n")
            for metric, value in metrics.items():
                f.write(f"  {metric}: {value}\n")
            f.write(f"Save Timestamp: {datetime.now()}\n")

def log_system_info():
    """Log system information for debugging."""
    import platform
    import torch
    
    logger = logging.getLogger(__name__)
    
    logger.info("System Information:")
    logger.info(f"  Platform: {platform.platform()}")
    logger.info(f"  Python: {platform.python_version()}")
    logger.info(f"  PyTorch: {torch.__version__}")
    logger.info(f"  CUDA Available: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        logger.info(f"  CUDA Version: {torch.version.cuda}")
        logger.info(f"  GPU Count: {torch.cuda.device_count()}")
        logger.info(f"  Current GPU: {torch.cuda.current_device()}")
        logger.info(f"  GPU Name: {torch.cuda.get_device_name()}")

def log_model_info(model, tokenizer):
    """Log model and tokenizer information."""
    logger = logging.getLogger(__name__)
    
    logger.info("Model Information:")
    logger.info(f"  Model Type: {type(model).__name__}")
    logger.info(f"  Model Config: {model.config}")
    logger.info(f"  Tokenizer: {type(tokenizer).__name__}")
    logger.info(f"  Vocab Size: {len(tokenizer.vocab)}")
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    logger.info(f"  Total Parameters: {total_params:,}")
    logger.info(f"  Trainable Parameters: {trainable_params:,}")

def log_data_info(train_size: int, val_size: int, test_size: int, label_distribution: dict):
    """Log dataset information."""
    logger = logging.getLogger(__name__)
    
    logger.info("Dataset Information:")
    logger.info(f"  Training Samples: {train_size:,}")
    logger.info(f"  Validation Samples: {val_size:,}")
    logger.info(f"  Test Samples: {test_size:,}")
    logger.info(f"  Total Samples: {train_size + val_size + test_size:,}")
    
    logger.info("Label Distribution:")
    for label, count in label_distribution.items():
        percentage = count / (train_size + val_size + test_size) * 100
        logger.info(f"  {label}: {count:,} ({percentage:.1f}%)")
