"""
Evaluation script for FinBERT sentiment analysis model.
"""

import os
import sys
import argparse
import torch
import pandas as pd
import numpy as np
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import logging
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config.training_config import TrainingConfig
from src.data.preprocessing import preprocess_dataframe
from src.model.finbert_classifier import FinBERTClassifier
from src.utils.metrics import compute_metrics, print_metrics_summary, save_metrics
from src.utils.logger import setup_logging
from src.utils.helpers import load_model_package, plot_confusion_matrix, plot_label_distribution

def evaluate_model(model_path: str,
                  test_data_path: str,
                  text_column: str = 'cleaned_text',
                  label_column: str = 'labels',
                  output_dir: str = 'results') -> dict:
    """
    Evaluate a trained FinBERT model.
    
    Args:
        model_path: Path to the saved model
        test_data_path: Path to test data CSV
        text_column: Name of text column
        label_column: Name of label column
        output_dir: Directory to save evaluation results
        
    Returns:
        Dictionary with evaluation results
    """
    
    # Setup logging
    logger = logging.getLogger(__name__)
    
    logger.info(f"Loading model from {model_path}")
    
    # Load model package
    try:
        model, tokenizer, metadata = load_model_package(model_path)
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)
        model.eval()
        
        logger.info(f"Model loaded successfully on {device}")
        
        # Get label mapping from metadata
        label_mapping = metadata.get('label_mapping', {0: "Negative", 1: "Neutral", 2: "Positive"})
        max_length = metadata.get('max_length', 512)
        
    except Exception as e:
        logger.error(f"Failed to load model: {e}")
        # Fallback: try to load as standard Hugging Face model
        model = AutoModelForSequenceClassification.from_pretrained(model_path)
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)
        model.eval()
        
        label_mapping = {0: "Negative", 1: "Neutral", 2: "Positive"}
        max_length = 512
        metadata = {}
    
    # Load test data
    logger.info(f"Loading test data from {test_data_path}")
    
    if test_data_path.endswith('.csv'):
        test_df = pd.read_csv(test_data_path)
    else:
        raise ValueError("Only CSV files are supported for test data")
    
    # Check if preprocessing is needed
    if text_column not in test_df.columns and 'summary_detail_with_title' in test_df.columns:
        logger.info("Preprocessing test data...")
        test_df = preprocess_dataframe(test_df, 'summary_detail_with_title', label_column)
        text_column = 'cleaned_text'
    
    logger.info(f"Loaded {len(test_df)} test samples")
    
    # Make predictions
    logger.info("Making predictions...")
    predictions = []
    probabilities = []
    
    with torch.no_grad():
        for i, text in enumerate(test_df[text_column]):
            if i % 100 == 0:
                logger.info(f"Processing sample {i+1}/{len(test_df)}")
            
            # Tokenize
            inputs = tokenizer(
                str(text),
                padding=True,
                truncation=True,
                max_length=max_length,
                return_tensors="pt"
            )
            
            # Move to device
            inputs = {k: v.to(device) for k, v in inputs.items()}
            
            # Forward pass
            outputs = model(**inputs)
            logits = outputs.logits
            
            # Get prediction
            predicted_class = torch.argmax(logits, dim=-1).cpu().numpy()[0]
            predictions.append(predicted_class)
            
            # Get probabilities
            probs = torch.nn.functional.softmax(logits, dim=-1).cpu().numpy()[0]
            probabilities.append(probs)
    
    # Convert to numpy arrays
    predictions = np.array(predictions)
    probabilities = np.array(probabilities)
    true_labels = test_df[label_column].values
    
    logger.info("Computing evaluation metrics...")
    
    # Compute metrics
    metrics = compute_metrics(
        predictions=predictions,
        labels=true_labels,
        label_names=list(label_mapping.values())
    )
    
    # Print metrics summary
    print_metrics_summary(metrics, title="Model Evaluation Results")
    
    # Create results directory
    os.makedirs(output_dir, exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Save detailed results
    detailed_results = {
        "model_path": model_path,
        "test_data_path": test_data_path,
        "evaluation_timestamp": timestamp,
        "model_metadata": metadata,
        "metrics": metrics,
        "sample_predictions": {
            "true_labels": true_labels[:10].tolist(),
            "predictions": predictions[:10].tolist(),
            "probabilities": probabilities[:10].tolist(),
            "texts": test_df[text_column].iloc[:10].tolist()
        }
    }
    
    results_file = os.path.join(output_dir, "metrics", f"evaluation_results_{timestamp}.json")
    os.makedirs(os.path.dirname(results_file), exist_ok=True)
    save_metrics(detailed_results, results_file)
    
    # Plot confusion matrix
    cm_path = os.path.join(output_dir, "plots", f"confusion_matrix_{timestamp}.png")
    os.makedirs(os.path.dirname(cm_path), exist_ok=True)
    plot_confusion_matrix(
        confusion_matrix=np.array(metrics['confusion_matrix']),
        class_names=list(label_mapping.values()),
        title="Test Set Confusion Matrix",
        save_path=cm_path
    )
    
    # Plot prediction distribution
    pred_counts = {label_mapping[i]: np.sum(predictions == i) for i in range(len(label_mapping))}
    dist_path = os.path.join(output_dir, "plots", f"prediction_distribution_{timestamp}.png")
    plot_label_distribution(
        label_counts=pred_counts,
        title="Prediction Distribution",
        save_path=dist_path
    )
    
    # Create prediction comparison DataFrame
    results_df = test_df.copy()
    results_df['predicted_label'] = predictions
    results_df['predicted_label_name'] = [label_mapping[pred] for pred in predictions]
    results_df['confidence'] = np.max(probabilities, axis=1)
    results_df['correct'] = (predictions == true_labels)
    
    # Add probability columns
    for i, label_name in label_mapping.items():
        results_df[f'prob_{label_name.lower()}'] = probabilities[:, i]
    
    # Save predictions
    predictions_file = os.path.join(output_dir, f"predictions_{timestamp}.csv")
    results_df.to_csv(predictions_file, index=False)
    
    logger.info(f"Evaluation completed!")
    logger.info(f"Results saved to: {results_file}")
    logger.info(f"Predictions saved to: {predictions_file}")
    logger.info(f"Plots saved to: {output_dir}/plots/")
    
    return metrics

def main():
    """Main evaluation function."""
    
    parser = argparse.ArgumentParser(description='Evaluate FinBERT sentiment analysis model')
    parser.add_argument('--model_path', type=str, required=True, help='Path to trained model')
    parser.add_argument('--test_data', type=str, required=True, help='Path to test data CSV')
    parser.add_argument('--text_column', type=str, default='cleaned_text', help='Name of text column')
    parser.add_argument('--label_column', type=str, default='labels', help='Name of label column')
    parser.add_argument('--output_dir', type=str, default='results', help='Output directory for results')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(log_dir="logs", log_level="INFO")
    logger = logging.getLogger(__name__)
    
    logger.info("Starting FinBERT model evaluation")
    logger.info(f"Arguments: {args}")
    
    try:
        # Run evaluation
        metrics = evaluate_model(
            model_path=args.model_path,
            test_data_path=args.test_data,
            text_column=args.text_column,
            label_column=args.label_column,
            output_dir=args.output_dir
        )
        
        logger.info("Evaluation completed successfully!")
        
        return metrics
        
    except Exception as e:
        logger.error(f"Evaluation failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
