# Step 1: Set Up the Environment
# Make sure you have the required libraries installed
# !pip install transformers datasets torch

import os
import torch
from datasets import load_from_disk
from transformers import AutoTokenizer, AutoModelForSequenceClassification, Trainer, TrainingArguments

# Step 2: Load the Dataset
# Load the preprocessed datasets
train_dataset = load_from_disk("preprocessed_datasets_original_splits/train")
test_dataset = load_from_disk("preprocessed_datasets_original_splits/test")

# Step 3: Load the FinBERT Model
model_name = "ProsusAI/finbert"
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForSequenceClassification.from_pretrained(model_name, num_labels=3)

# Step 4: Configure Training Parameters
training_args = TrainingArguments(
    output_dir='./results',          # output directory
    num_train_epochs=3,              # total number of training epochs
    per_device_train_batch_size=16,  # batch size per device during training
    per_device_eval_batch_size=64,   # batch size for evaluation
    warmup_steps=500,                 # number of warmup steps for learning rate scheduler
    weight_decay=0.01,                # strength of weight decay
    logging_dir='./logs',            # directory for storing logs
    logging_steps=10,
    evaluation_strategy="epoch",      # evaluate every epoch
    save_strategy="epoch",            # save model every epoch
    load_best_model_at_end=True,      # load the best model when finished training
)

# Step 5: Create a Trainer
trainer = Trainer(
    model=model,                         # the instantiated 🤗 Transformers model to be trained
    args=training_args,                  # training arguments, defined above
    train_dataset=train_dataset,         # training dataset
    eval_dataset=test_dataset,           # evaluation dataset
)

# Step 6: Train the Model
trainer.train()

# Step 7: Evaluate the Model
results = trainer.evaluate()
print(f"Evaluation results: {results}")

# Save the model
model.save_pretrained("finbert_sequence_classification_model")
tokenizer.save_pretrained("finbert_sequence_classification_model")