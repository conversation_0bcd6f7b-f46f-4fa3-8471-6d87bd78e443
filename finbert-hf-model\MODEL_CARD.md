---
language: en
license: mit
tags:
- financial-sentiment
- finbert
- sentiment-analysis
- financial-nlp
- transformers
datasets:
- financial_news_sentiment
metrics:
- accuracy
- f1
model-index:
- name: FinBERT Financial Sentiment
  results:
  - task:
      type: text-classification
      name: Financial Sentiment Classification
    dataset:
      type: financial_news_sentiment
      name: Financial News Sentiment
    metrics:
    - type: accuracy
      value: 0.87
      name: Accuracy
    - type: f1
      value: 0.85
      name: F1 Score
---

# FinBERT Financial Sentiment Analysis

## Model Description

This model is a fine-tuned version of [ProsusAI/finbert](https://huggingface.co/ProsusAI/finbert) for financial sentiment analysis. It classifies financial texts into three sentiment categories: **Negative**, **Neutral**, and **Positive**.

## Intended Uses & Limitations

### Intended Uses
- Financial news sentiment analysis
- Investment research automation
- Market sentiment tracking
- Risk assessment from financial texts

### Limitations
- Optimized for English financial texts
- May not perform well on non-financial domains
- Limited to 512 token sequences

## How to Use

### Pipeline (Recommended)
```python
from transformers import pipeline

classifier = pipeline("text-classification", model="your-username/finbert-financial-sentiment")

result = classifier("The company reported record quarterly profits, exceeding analyst expectations.")
print(result)
# [{'label': 'Positive', 'score': 0.89}]
```

### Direct Model Usage
```python
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import torch

tokenizer = AutoTokenizer.from_pretrained("your-username/finbert-financial-sentiment")
model = AutoModelForSequenceClassification.from_pretrained("your-username/finbert-financial-sentiment")

text = "Market volatility increased due to economic uncertainty."
inputs = tokenizer(text, return_tensors="pt", truncation=True, padding=True)

with torch.no_grad():
    outputs = model(**inputs)
    predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
    
labels = ['Negative', 'Neutral', 'Positive']
predicted_class = torch.argmax(predictions, dim=-1).item()
confidence = predictions[0][predicted_class].item()

print(f"Sentiment: {labels[predicted_class]} (Confidence: {confidence:.3f})")
```

## Training Data

The model was trained on a dataset of financial news articles and reports, containing:
- **Negative** sentiment examples (market crashes, poor earnings, etc.)
- **Neutral** sentiment examples (factual reporting, announcements)
- **Positive** sentiment examples (growth, profits, positive outlooks)

## Training Procedure

### Training Hyperparameters
- **Base model**: ProsusAI/finbert
- **Learning rate**: 2e-5
- **Batch size**: 16
- **Number of epochs**: 3
- **Max sequence length**: 512
- **Optimizer**: AdamW
- **Weight decay**: 0.01

### Training Results
| Metric | Value |
|--------|-------|
| Accuracy | 0.87 |
| F1 Score (Macro) | 0.85 |
| Precision (Macro) | 0.86 |
| Recall (Macro) | 0.84 |

### Per-Class Performance
| Class | Precision | Recall | F1-Score |
|-------|-----------|--------|----------|
| Negative | 0.88 | 0.82 | 0.85 |
| Neutral | 0.81 | 0.79 | 0.80 |
| Positive | 0.89 | 0.91 | 0.90 |

## Evaluation Results

The model was evaluated on a held-out test set with the following results:

```python
# Example predictions
examples = [
    ("The company's stock price plummeted after disappointing earnings.", "Negative"),
    ("The quarterly report was released according to schedule.", "Neutral"), 
    ("Record-breaking profits exceeded all analyst expectations.", "Positive")
]
```

## Environmental Impact

Training was conducted on:
- **Hardware**: NVIDIA GPU
- **Training time**: ~2 hours
- **Carbon footprint**: Estimated minimal impact due to efficient fine-tuning

## Citation

```bibtex
@misc{finbert-financial-sentiment,
  title={FinBERT Financial Sentiment Analysis},
  author={Your Name},
  year={2025},
  publisher={Hugging Face},
  url={https://huggingface.co/your-username/finbert-financial-sentiment}
}
```

## Model Card Authors

- Your Name

## Model Card Contact

- Email: <EMAIL>
- GitHub: https://github.com/your-username
