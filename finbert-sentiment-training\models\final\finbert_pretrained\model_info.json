{"model_name": "FinBERT-Financial-Sentiment", "base_model": "ProsusAI/finbert", "num_labels": 3, "label_mapping": {"0": "Negative", "1": "Neutral", "2": "Positive"}, "training_dataset": "test", "training_date": "2025-07-16T14:14:22.973198", "final_metrics": {"eval_loss": 0.49527400732040405, "eval_model_preparation_time": 0.0022, "eval_accuracy": 0.8383458646616542, "eval_f1": 0.8240544807963432, "eval_precision": 0.8456400435049889, "eval_recall": 0.8383458646616542, "eval_f1_negative": 0.15384615384615385, "eval_precision_negative": 1.0, "eval_recall_negative": 0.08333333333333333, "eval_f1_neutral": 0.8726114649681529, "eval_precision_neutral": 0.8616352201257862, "eval_recall_neutral": 0.8838709677419355, "eval_f1_positive": 0.8292682926829268, "eval_precision_positive": 0.8018867924528302, "eval_recall_positive": 0.8585858585858586, "eval_runtime": 78.8174, "eval_samples_per_second": 3.375, "eval_steps_per_second": 0.216, "epoch": 3.0}, "training_args": {"output_dir": "./finbert_results", "overwrite_output_dir": false, "do_train": false, "do_eval": true, "do_predict": false, "eval_strategy": "epoch", "prediction_loss_only": false, "per_device_train_batch_size": 16, "per_device_eval_batch_size": 16, "per_gpu_train_batch_size": null, "per_gpu_eval_batch_size": null, "gradient_accumulation_steps": 1, "eval_accumulation_steps": null, "eval_delay": 0, "torch_empty_cache_steps": null, "learning_rate": 2e-05, "weight_decay": 0.01, "adam_beta1": 0.9, "adam_beta2": 0.999, "adam_epsilon": 1e-08, "max_grad_norm": 1.0, "num_train_epochs": 3, "max_steps": -1, "lr_scheduler_type": "linear", "lr_scheduler_kwargs": {}, "warmup_ratio": 0.0, "warmup_steps": 100, "log_level": "passive", "log_level_replica": "warning", "log_on_each_node": true, "logging_dir": "./finbert_results/logs", "logging_strategy": "steps", "logging_first_step": false, "logging_steps": 50, "logging_nan_inf_filter": true, "save_strategy": "epoch", "save_steps": 500, "save_total_limit": 2, "save_safetensors": true, "save_on_each_node": false, "save_only_model": false, "restore_callback_states_from_checkpoint": false, "no_cuda": false, "use_cpu": false, "use_mps_device": false, "seed": 42, "data_seed": null, "jit_mode_eval": false, "use_ipex": false, "bf16": false, "fp16": false, "fp16_opt_level": "O1", "half_precision_backend": "auto", "bf16_full_eval": false, "fp16_full_eval": false, "tf32": null, "local_rank": 0, "ddp_backend": null, "tpu_num_cores": null, "tpu_metrics_debug": false, "debug": [], "dataloader_drop_last": false, "eval_steps": null, "dataloader_num_workers": 0, "dataloader_prefetch_factor": null, "past_index": -1, "run_name": "./finbert_results", "disable_tqdm": false, "remove_unused_columns": false, "label_names": null, "load_best_model_at_end": true, "metric_for_best_model": "f1", "greater_is_better": true, "ignore_data_skip": false, "fsdp": [], "fsdp_min_num_params": 0, "fsdp_config": {"min_num_params": 0, "xla": false, "xla_fsdp_v2": false, "xla_fsdp_grad_ckpt": false}, "fsdp_transformer_layer_cls_to_wrap": null, "accelerator_config": {"split_batches": false, "dispatch_batches": null, "even_batches": true, "use_seedable_sampler": true, "non_blocking": false, "gradient_accumulation_kwargs": null}, "deepspeed": null, "label_smoothing_factor": 0.0, "optim": "adamw_torch", "optim_args": null, "adafactor": false, "group_by_length": false, "length_column_name": "length", "report_to": [], "ddp_find_unused_parameters": null, "ddp_bucket_cap_mb": null, "ddp_broadcast_buffers": null, "dataloader_pin_memory": true, "dataloader_persistent_workers": false, "skip_memory_metrics": true, "use_legacy_prediction_loop": false, "push_to_hub": false, "resume_from_checkpoint": null, "hub_model_id": null, "hub_strategy": "every_save", "hub_token": "<HUB_TOKEN>", "hub_private_repo": null, "hub_always_push": false, "hub_revision": null, "gradient_checkpointing": false, "gradient_checkpointing_kwargs": null, "include_inputs_for_metrics": false, "include_for_metrics": [], "eval_do_concat_batches": true, "fp16_backend": "auto", "push_to_hub_model_id": null, "push_to_hub_organization": null, "push_to_hub_token": "<PUSH_TO_HUB_TOKEN>", "mp_parameters": "", "auto_find_batch_size": false, "full_determinism": false, "torchdynamo": null, "ray_scope": "last", "ddp_timeout": 1800, "torch_compile": false, "torch_compile_backend": null, "torch_compile_mode": null, "include_tokens_per_second": false, "include_num_input_tokens_seen": false, "neftune_noise_alpha": null, "optim_target_modules": null, "batch_eval_metrics": false, "eval_on_start": false, "use_liger_kernel": false, "liger_kernel_config": null, "eval_use_gather_object": false, "average_tokens_across_devices": false}, "model_size_mb": 418.58076763153076}