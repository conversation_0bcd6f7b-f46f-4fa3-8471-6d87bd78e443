#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to inspect the contents of the finbert_model_only.pkl file
"""

import pickle
import torch

def inspect_pickle_file(file_path):
    """Inspect the contents of the pickle file."""
    print(f"🔍 Inspecting: {file_path}")
    print("=" * 50)
    
    try:
        with open(file_path, 'rb') as f:
            model_data = pickle.load(f)
        
        print("📦 Pickle file contents:")
        print(f"   Type: {type(model_data)}")
        
        if isinstance(model_data, dict):
            print(f"   Keys: {list(model_data.keys())}")
            
            for key, value in model_data.items():
                print(f"\n🔑 Key: '{key}'")
                print(f"   Type: {type(value)}")
                
                if key == 'model_state_dict':
                    print(f"   State dict keys: {len(value)} layers")
                    # Show first few layer names
                    layer_names = list(value.keys())[:5]
                    print(f"   First layers: {layer_names}")
                    
                elif key == 'model_config':
                    print(f"   Config type: {type(value)}")
                    if hasattr(value, 'to_dict'):
                        config_dict = value.to_dict()
                        print(f"   Model type: {config_dict.get('model_type', 'unknown')}")
                        print(f"   Num labels: {config_dict.get('num_labels', 'unknown')}")
                        print(f"   Hidden size: {config_dict.get('hidden_size', 'unknown')}")
                    
                elif key == 'tokenizer':
                    print(f"   Tokenizer type: {type(value)}")
                    if hasattr(value, 'vocab_size'):
                        print(f"   Vocab size: {value.vocab_size}")
                    
                elif key == 'label_mapping':
                    print(f"   Label mapping: {value}")
                    
                elif key == 'model':
                    print(f"   Model type: {type(value)}")
                    if hasattr(value, 'config'):
                        print(f"   Model config: {value.config}")
        
        print(f"\n✅ File inspection complete")
        return True
        
    except Exception as e:
        print(f"❌ Error inspecting file: {e}")
        return False

def test_model_loading():
    """Test loading the model using different methods."""
    file_path = 'finbert_results/model_export/finbert_model_only.pkl'
    
    print(f"\n🧪 Testing model loading from: {file_path}")
    print("=" * 50)
    
    try:
        with open(file_path, 'rb') as f:
            model_data = pickle.load(f)
        
        if 'model' in model_data:
            print("✅ Complete model found in pickle file")
            model = model_data['model']
            tokenizer = model_data['tokenizer']
            
        elif 'model_state_dict' in model_data and 'model_config' in model_data:
            print("✅ Model weights and config found - reconstructing model")
            from transformers import BertForSequenceClassification
            
            # Create model from config
            model_config = model_data['model_config']
            model = BertForSequenceClassification(model_config)
            
            # Load weights
            model.load_state_dict(model_data['model_state_dict'])
            tokenizer = model_data['tokenizer']
            
        else:
            print("❌ Insufficient data in pickle file")
            return False
        
        # Test the model
        model.eval()
        test_text = "Apple reported strong quarterly earnings with revenue growth of 25%."
        
        inputs = tokenizer(test_text, return_tensors="pt", truncation=True, padding=True, max_length=512)
        
        with torch.no_grad():
            outputs = model(**inputs)
            predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
        
        probs = predictions.cpu().numpy()[0]
        predicted_class = torch.argmax(predictions, dim=-1).item()
        
        label_mapping = model_data.get('label_mapping', {0: 'Negative', 1: 'Neutral', 2: 'Positive'})
        
        print(f"\n🎯 Test prediction:")
        print(f"   Text: {test_text}")
        print(f"   Predicted class: {predicted_class}")
        print(f"   Sentiment: {label_mapping[predicted_class]}")
        print(f"   Probabilities: {probs}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing model: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    file_path = 'finbert_results/model_export/finbert_model_only.pkl'
    
    # Inspect the pickle file
    if inspect_pickle_file(file_path):
        # Test model loading
        test_model_loading()
    else:
        print("❌ Cannot proceed with testing due to inspection failure")
