# Installation of necessary libraries
# Uncomment if packages are not installed
# !pip install transformers datasets torch scikit-learn accelerate

# Library imports
import os
import json
import numpy as np
import pandas as pd
import torch
from datetime import datetime

# Transformers and Datasets
from transformers import (
    AutoModelForSequenceClassification,
    AutoTokenizer,
    TrainingArguments,
    Trainer,
    EarlyStoppingCallback
)
from datasets import load_from_disk

# Metrics
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix

# Visualization
import matplotlib.pyplot as plt
import seaborn as sns

# Environment configuration
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Device used: {device}")
print(f"Available GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB" if torch.cuda.is_available() else "GPU not available")

print("All libraries imported successfully")

# Step 4.1: Loading preprocessed data
print("=== STEP 4.1: LOADING PREPROCESSED DATA ===")

# Path to preprocessed data
data_dir = "preprocessed_datasets_original_splits"

# Check that data exists
if not os.path.exists(data_dir):
    print(f"Error: The folder {data_dir} does not exist.")
    print(f"Please run the preprocessing notebook first.")
else:
    print(f"Data folder found: {data_dir}")

try:
    # Load preprocessed datasets
    print("\nLoading datasets...")
    train_dataset = load_from_disk(os.path.join(data_dir, "train"))
    test_dataset = load_from_disk(os.path.join(data_dir, "test"))
    
    print(f"Train dataset loaded: {len(train_dataset)} examples")
    print(f"Test dataset loaded: {len(test_dataset)} examples")
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(os.path.join(data_dir, "tokenizer"))
    print(f"Tokenizer loaded: {tokenizer.__class__.__name__}")
    
    # Load metadata
    with open(os.path.join(data_dir, "metadata.json"), 'r') as f:
        metadata = json.load(f)
    
    print(f"\nPreprocessing information:")
    print(f"  - Original dataset: {metadata['dataset_name']}")
    print(f"  - Tokenizer: {metadata['tokenizer_name']}")
    print(f"  - Max length: {metadata['max_length']}")
    print(f"  - Preprocessing date: {metadata['preprocessing_date']}")
    
    # Check data structure
    print(f"\nData structure:")
    print(f"  Train columns: {train_dataset.column_names}")
    print(f"  Test columns: {test_dataset.column_names}")
    print(f"  Format: {train_dataset.format}")
    
    # Data example
    example = train_dataset[0]
    print(f"\nData example:")
    print(f"  Input IDs shape: {example['input_ids'].shape}")
    print(f"  Attention mask shape: {example['attention_mask'].shape}")
    print(f"  Label: {example['labels']} ({['Negative', 'Neutral', 'Positive'][example['labels']]})")
    
except Exception as e:
    print(f"Error during loading: {e}")
    print(f"Please check that preprocessing was performed correctly.")

# Step 4.2: Loading FinBERT model
print("\n=== STEP 4.2: LOADING FINBERT MODEL ===")

# Model configuration
model_name = "ProsusAI/finbert"
num_labels = 3  # Negative, Neutral, Positive

try:
    # Load FinBERT model for sequence classification
    print(f"Loading model {model_name}...")
    model = AutoModelForSequenceClassification.from_pretrained(
        model_name,
        num_labels=num_labels,
        problem_type="single_label_classification"
    )
    
    print(f"FinBERT model loaded successfully")
    print(f"  - Number of labels: {num_labels}")
    print(f"  - Number of parameters: {model.num_parameters():,}")
    
    # Label configuration
    label_names = ['Negative', 'Neutral', 'Positive']
    id2label = {i: label for i, label in enumerate(label_names)}
    label2id = {label: i for i, label in enumerate(label_names)}
    
    model.config.id2label = id2label
    model.config.label2id = label2id
    
    print(f"  - Label mapping: {id2label}")
    
    # Move model to appropriate device
    model = model.to(device)
    print(f"  - Model moved to: {device}")
    
    # Display model architecture
    print(f"\nModel architecture:")
    print(f"  - Type: {model.__class__.__name__}")
    print(f"  - Classification layer: {model.classifier}")
    
except Exception as e:
    print(f"Error loading model: {e}")

# Step 4.3: Evaluation metrics configuration
print("\n=== STEP 4.3: METRICS CONFIGURATION ===")

def compute_metrics(eval_pred):
    """Calculate evaluation metrics"""
    predictions, labels = eval_pred
    predictions = np.argmax(predictions, axis=1)
    
    # Calculate basic metrics
    accuracy = accuracy_score(labels, predictions)
    precision, recall, f1, _ = precision_recall_fscore_support(labels, predictions, average='weighted')
    
    # Per-class metrics
    precision_per_class, recall_per_class, f1_per_class, support = precision_recall_fscore_support(
        labels, predictions, average=None
    )
    
    # Create metrics dictionary
    metrics = {
        'accuracy': accuracy,
        'f1': f1,
        'precision': precision,
        'recall': recall,
    }
    
    # Add per-class metrics
    for i, label_name in enumerate(['negative', 'neutral', 'positive']):
        metrics[f'f1_{label_name}'] = f1_per_class[i]
        metrics[f'precision_{label_name}'] = precision_per_class[i]
        metrics[f'recall_{label_name}'] = recall_per_class[i]
    
    return metrics

print("Metrics function configured")
print("Metrics to be calculated:")
print("  - Global accuracy")
print("  - F1-score (weighted and per class)")
print("  - Precision (weighted and per class)")
print("  - Recall (weighted and per class)")

# Step 4.4: Training hyperparameters configuration
print("\n=== STEP 4.4: HYPERPARAMETERS CONFIGURATION ===")

# Create output folder
output_dir = "./finbert_results"
os.makedirs(output_dir, exist_ok=True)

# Training arguments configuration
training_args = TrainingArguments(
    # Output folders
    output_dir=output_dir,
    logging_dir=f"{output_dir}/logs",
    
    # Evaluation strategy (eval_strategy replaces evaluation_strategy)
    eval_strategy="epoch",
    save_strategy="epoch",
    logging_strategy="steps",
    
    # Training hyperparameters
    learning_rate=2e-5,
    per_device_train_batch_size=16,
    per_device_eval_batch_size=16,
    num_train_epochs=3,
    weight_decay=0.01,
    
    # Optimization and stability
    warmup_steps=100,
    logging_steps=50,
    save_total_limit=2,
    
    # Best model selection
    load_best_model_at_end=True,
    metric_for_best_model="f1",
    greater_is_better=True,
    
    # Other configurations
    seed=42,
    fp16=torch.cuda.is_available(),  # Use mixed precision if GPU available
    dataloader_num_workers=0,  # Avoid multiprocessing issues
    remove_unused_columns=False,
    
    # Reporting and saving
    report_to="none",  # No external logging
    push_to_hub=False
)

print("Training configuration:")
print(f"  Output folder: {output_dir}")
print(f"  Learning rate: {training_args.learning_rate}")
print(f"  Batch size (train/eval): {training_args.per_device_train_batch_size}/{training_args.per_device_eval_batch_size}")
print(f"  Number of epochs: {training_args.num_train_epochs}")
print(f"  Weight decay: {training_args.weight_decay}")
print(f"  Warmup steps: {training_args.warmup_steps}")
print(f"  Evaluation: every epoch")
print(f"  Saving: best model according to F1-score")
print(f"  FP16: {training_args.fp16}")

# Calculate total number of steps
total_steps = (len(train_dataset) // training_args.per_device_train_batch_size) * training_args.num_train_epochs
print(f"\nTraining estimation:")
print(f"  - Steps per epoch: {len(train_dataset) // training_args.per_device_train_batch_size}")
print(f"  - Total steps: {total_steps}")
print(f"  - Estimated duration: ~{total_steps * 2 / 60:.1f} minutes (estimation)")

# Step 4.5: Trainer creation
print("\n=== STEP 4.5: TRAINER CREATION ===")

# Early stopping callback (optional)
early_stopping = EarlyStoppingCallback(
    early_stopping_patience=2,
    early_stopping_threshold=0.001
)

try:
    # Create trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=test_dataset,
        tokenizer=tokenizer,
        compute_metrics=compute_metrics,
        callbacks=[early_stopping]
    )
    
    print("Trainer created successfully")
    print(f"Configuration:")
    print(f"  - Training dataset: {len(train_dataset)} examples")
    print(f"  - Evaluation dataset: {len(test_dataset)} examples")
    print(f"  - Tokenizer: {tokenizer.__class__.__name__}")
    print(f"  - Early stopping: enabled (patience=2)")
    print(f"  - Custom metrics: enabled")
    
    # Check configuration
    print(f"\nConfiguration verification:")
    print(f"  - Model on device: {next(model.parameters()).device}")
    print(f"  - Number of trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
except Exception as e:
    print(f"Error creating trainer: {e}")

# Step 4.6: Initial evaluation (before training)
print("\n=== STEP 4.6: INITIAL EVALUATION ===")

print("Evaluating model before fine-tuning...")
try:
    # Evaluate pre-trained model
    initial_eval = trainer.evaluate()
    
    print("\nInitial metrics (pre-trained model):")
    print(f"  Accuracy: {initial_eval['eval_accuracy']:.4f} ({initial_eval['eval_accuracy']*100:.2f}%)")
    print(f"  F1-score: {initial_eval['eval_f1']:.4f}")
    print(f"  Precision: {initial_eval['eval_precision']:.4f}")
    print(f"  Recall: {initial_eval['eval_recall']:.4f}")
    
    # Per-class metrics
    print(f"\nPer-sentiment metrics:")
    for sentiment in ['negative', 'neutral', 'positive']:
        f1 = initial_eval.get(f'eval_f1_{sentiment}', 0)
        precision = initial_eval.get(f'eval_precision_{sentiment}', 0)
        recall = initial_eval.get(f'eval_recall_{sentiment}', 0)
        print(f"  {sentiment.capitalize()}:")
        print(f"    F1: {f1:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}")
    
    # Save initial metrics
    initial_metrics = {
        'evaluation_type': 'initial (pre-trained)',
        'timestamp': datetime.now().isoformat(),
        'metrics': initial_eval
    }
    
    with open(os.path.join(output_dir, 'initial_evaluation.json'), 'w') as f:
        json.dump(initial_metrics, f, indent=2)
    
    print(f"\nInitial metrics saved in: {output_dir}/initial_evaluation.json")
    
except Exception as e:
    print(f"Error during initial evaluation: {e}")
    initial_eval = None

# Step 4.7: Model training launch
print("\n=== STEP 4.7: MODEL TRAINING ===")

print("Starting FinBERT training...")
print(f"Start time: {datetime.now().strftime('%H:%M:%S')}")
print("\n" + "="*60)

try:
    # Start training
    start_time = datetime.now()
    
    training_result = trainer.train()
    
    end_time = datetime.now()
    training_duration = end_time - start_time
    
    print("\n" + "="*60)
    print("TRAINING COMPLETED SUCCESSFULLY!")
    print(f"Total duration: {training_duration}")
    print(f"End time: {end_time.strftime('%H:%M:%S')}")
    
    # Display training results
    print(f"\nTraining results:")
    print(f"  - Completed epochs: {training_result.global_step / (len(train_dataset) // training_args.per_device_train_batch_size):.1f}")
    print(f"  - Total steps: {training_result.global_step}")
    print(f"  - Final loss: {training_result.training_loss:.4f}")
    
    # Save training information
    training_info = {
        'start_time': start_time.isoformat(),
        'end_time': end_time.isoformat(),
        'duration_seconds': training_duration.total_seconds(),
        'duration_formatted': str(training_duration),
        'global_step': training_result.global_step,
        'training_loss': training_result.training_loss,
        'epochs_completed': training_result.global_step / (len(train_dataset) // training_args.per_device_train_batch_size),
        'training_args': training_args.to_dict()
    }
    
    with open(os.path.join(output_dir, 'training_info.json'), 'w') as f:
        json.dump(training_info, f, indent=2, default=str)
    
    print(f"\nTraining information saved")
    
except Exception as e:
    print(f"\nError during training: {e}")
    print(f"Check logs in {output_dir}/logs for more details")

# Step 4.8: Final evaluation of trained model
print("\n=== STEP 4.8: FINAL EVALUATION ===")

print("Evaluating model after fine-tuning...")

try:
    # Evaluate final model
    final_eval = trainer.evaluate()
    
    print("\nFINAL METRICS (after fine-tuning):")
    print("="*50)
    print(f"Accuracy: {final_eval['eval_accuracy']:.4f} ({final_eval['eval_accuracy']*100:.2f}%)")
    print(f"F1-score: {final_eval['eval_f1']:.4f}")
    print(f"Precision: {final_eval['eval_precision']:.4f}")
    print(f"Recall: {final_eval['eval_recall']:.4f}")
    
    # Detailed per-class metrics
    print(f"\nPER-SENTIMENT METRICS:")
    print("-"*40)
    for sentiment in ['negative', 'neutral', 'positive']:
        f1 = final_eval.get(f'eval_f1_{sentiment}', 0)
        precision = final_eval.get(f'eval_precision_{sentiment}', 0)
        recall = final_eval.get(f'eval_recall_{sentiment}', 0)
        print(f"{sentiment.upper()}:")
        print(f"   F1: {f1:.4f} | Precision: {precision:.4f} | Recall: {recall:.4f}")
    
    # Comparison with initial evaluation
    if 'initial_eval' in locals() and initial_eval is not None:
        print(f"\nIMPROVEMENT AFTER FINE-TUNING:")
        print("-"*45)
        acc_improvement = final_eval['eval_accuracy'] - initial_eval['eval_accuracy']
        f1_improvement = final_eval['eval_f1'] - initial_eval['eval_f1']
        
        print(f"Accuracy: {acc_improvement:+.4f} ({acc_improvement*100:+.2f} percentage points)")
        print(f"F1-score: {f1_improvement:+.4f} ({f1_improvement*100:+.2f}%)")
        
        if acc_improvement > 0:
            print(f"Fine-tuning improved performance!")
        else:
            print(f"Fine-tuning did not improve performance")
    
    # Save final metrics
    final_metrics = {
        'evaluation_type': 'final (after fine-tuning)',
        'timestamp': datetime.now().isoformat(),
        'metrics': final_eval,
        'improvement_over_initial': {
            'accuracy': acc_improvement if 'acc_improvement' in locals() else None,
            'f1': f1_improvement if 'f1_improvement' in locals() else None
        } if 'initial_eval' in locals() and initial_eval is not None else None
    }
    
    with open(os.path.join(output_dir, 'final_evaluation.json'), 'w') as f:
        json.dump(final_metrics, f, indent=2)
    
    print(f"\nFinal metrics saved")
    
except Exception as e:
    print(f"Error during final evaluation: {e}")

# Step 4.9: Detailed prediction analysis and advanced metrics
print("\n=== STEP 4.9: DETAILED PREDICTION ANALYSIS ===")

try:
    # Get predictions on test dataset
    print("Generating predictions on test dataset...")
    predictions = trainer.predict(test_dataset)
    
    # Extract predictions and true labels
    y_pred = np.argmax(predictions.predictions, axis=1)
    y_true = predictions.label_ids
    prediction_probabilities = torch.nn.functional.softmax(torch.tensor(predictions.predictions), dim=-1).numpy()
    
    # Create confusion matrix
    cm = confusion_matrix(y_true, y_pred)
    
    # === MULTIPLE VISUALIZATIONS ===
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. Normalized confusion matrix
    cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
    sns.heatmap(cm_normalized, 
                annot=True, 
                fmt='.3f', 
                cmap='Blues',
                xticklabels=['Negative', 'Neutral', 'Positive'],
                yticklabels=['Negative', 'Neutral', 'Positive'],
                ax=axes[0,0])
    axes[0,0].set_title('Normalized Confusion Matrix', fontweight='bold')
    axes[0,0].set_xlabel('Predictions')
    axes[0,0].set_ylabel('True Labels')
    
    # 2. Raw confusion matrix
    sns.heatmap(cm, 
                annot=True, 
                fmt='d', 
                cmap='Oranges',
                xticklabels=['Negative', 'Neutral', 'Positive'],
                yticklabels=['Negative', 'Neutral', 'Positive'],
                ax=axes[0,1])
    axes[0,1].set_title('Confusion Matrix (Absolute Numbers)', fontweight='bold')
    axes[0,1].set_xlabel('Predictions')
    axes[0,1].set_ylabel('True Labels')
    
    # 3. Probability correlation matrix
    correlation_matrix = np.corrcoef(prediction_probabilities.T)
    sns.heatmap(correlation_matrix,
                annot=True,
                fmt='.3f',
                cmap='RdBu_r',
                center=0,
                xticklabels=['Negative', 'Neutral', 'Positive'],
                yticklabels=['Negative', 'Neutral', 'Positive'],
                ax=axes[1,0])
    axes[1,0].set_title('Probability Correlation Matrix', fontweight='bold')
    
    # 4. Prediction confidence distribution
    max_probs = np.max(prediction_probabilities, axis=1)
    axes[1,1].hist(max_probs, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    axes[1,1].axvline(np.mean(max_probs), color='red', linestyle='--', 
                      label=f'Mean: {np.mean(max_probs):.3f}')
    axes[1,1].set_title('Prediction Confidence Distribution', fontweight='bold')
    axes[1,1].set_xlabel('Maximum Probability')
    axes[1,1].set_ylabel('Frequency')
    axes[1,1].legend()
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'comprehensive_analysis.png'), dpi=300, bbox_inches='tight')
    plt.show()
    
    # === DETAILED PERFORMANCE METRICS ===
    print(f"\nDETAILED PERFORMANCE METRICS:")
    print("="*60)
    
    # Global and per-class accuracy
    overall_accuracy = accuracy_score(y_true, y_pred)
    print(f"Global Accuracy: {overall_accuracy:.4f} ({overall_accuracy*100:.2f}%)")
    
    # Per-class accuracy
    print(f"\nAccuracy per Class:")
    label_names = ['Negative', 'Neutral', 'Positive']
    for i, label in enumerate(label_names):
        class_mask = (y_true == i)
        if np.sum(class_mask) > 0:
            class_accuracy = accuracy_score(y_true[class_mask], y_pred[class_mask])
            print(f"  {label}: {class_accuracy:.4f} ({class_accuracy*100:.2f}%) - {np.sum(class_mask)} samples")
    
    # Confidence metrics
    print(f"\nConfidence Metrics:")
    print(f"  Mean confidence: {np.mean(max_probs):.4f}")
    print(f"  Median confidence: {np.median(max_probs):.4f}")
    print(f"  Confidence std dev: {np.std(max_probs):.4f}")
    print(f"  High confidence predictions (>0.9): {np.sum(max_probs > 0.9)} ({np.sum(max_probs > 0.9)/len(max_probs)*100:.1f}%)")
    print(f"  Low confidence predictions (<0.6): {np.sum(max_probs < 0.6)} ({np.sum(max_probs < 0.6)/len(max_probs)*100:.1f}%)")
    
    # === ERROR ANALYSIS ===
    print(f"\nDETAILED ERROR ANALYSIS:")
    print("-"*50)
    
    total_errors = len(y_true) - np.trace(cm)
    print(f"Total errors: {total_errors} out of {len(y_true)} ({total_errors/len(y_true)*100:.1f}%)")
    
    # Analyze errors by confidence
    error_mask = (y_true != y_pred)
    if np.sum(error_mask) > 0:
        error_confidences = max_probs[error_mask]
        correct_confidences = max_probs[~error_mask]
        
        print(f"\nConfidence in errors vs correct predictions:")
        print(f"  Mean confidence (errors): {np.mean(error_confidences):.4f}")
        print(f"  Mean confidence (correct): {np.mean(correct_confidences):.4f}")
        print(f"  Difference: {np.mean(correct_confidences) - np.mean(error_confidences):.4f}")
    
    # Most frequent confusions
    print(f"\nMost frequent confusions:")
    confusions = [
        ("Negative -> Neutral", cm[0,1]),
        ("Negative -> Positive", cm[0,2]),
        ("Neutral -> Negative", cm[1,0]),
        ("Neutral -> Positive", cm[1,2]),
        ("Positive -> Negative", cm[2,0]),
        ("Positive -> Neutral", cm[2,1])
    ]
    
    confusions.sort(key=lambda x: x[1], reverse=True)
    for confusion_type, count in confusions[:3]:  # Top 3
        if count > 0:
            percentage = count / total_errors * 100 if total_errors > 0 else 0
            print(f"  {confusion_type}: {count} errors ({percentage:.1f}% of errors)")
    
    # === DETAILED MODEL INFORMATION ===
    print(f"\nDETAILED MODEL INFORMATION:")
    print("-"*50)
    print(f"  Model name: {model_name}")
    print(f"  Architecture: {model.__class__.__name__}")
    print(f"  Total parameters: {model.num_parameters():,}")
    print(f"  Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    print(f"  Vocabulary size: {tokenizer.vocab_size:,}")
    print(f"  Max sequence length: {tokenizer.model_max_length}")
    print(f"  Device: {next(model.parameters()).device}")
    
    # Save all analyses
    comprehensive_analysis = {
        'timestamp': datetime.now().isoformat(),
        'model_info': {
            'name': model_name,
            'architecture': model.__class__.__name__,
            'total_parameters': int(model.num_parameters()),
            'trainable_parameters': int(sum(p.numel() for p in model.parameters() if p.requires_grad)),
            'vocab_size': int(tokenizer.vocab_size),
            'max_length': int(tokenizer.model_max_length)
        },
        'performance_metrics': {
            'overall_accuracy': float(overall_accuracy),
            'total_predictions': int(len(y_true)),
            'total_errors': int(total_errors),
            'error_rate': float(total_errors / len(y_true)),
            'confidence_metrics': {
                'mean_confidence': float(np.mean(max_probs)),
                'median_confidence': float(np.median(max_probs)),
                'std_confidence': float(np.std(max_probs)),
                'high_confidence_predictions': int(np.sum(max_probs > 0.9)),
                'low_confidence_predictions': int(np.sum(max_probs < 0.6))
            }
        },
        'confusion_matrix': cm.tolist(),
        'confusion_matrix_normalized': cm_normalized.tolist(),
        'correlation_matrix': correlation_matrix.tolist(),
        'predictions': y_pred.tolist(),
        'true_labels': y_true.tolist(),
        'prediction_probabilities': prediction_probabilities.tolist(),
        'top_confusions': confusions
    }
    
    with open(os.path.join(output_dir, 'comprehensive_analysis.json'), 'w', encoding='utf-8') as f:
        json.dump(comprehensive_analysis, f, indent=2, ensure_ascii=False)
    
    print(f"\nComprehensive analysis saved in: {output_dir}/comprehensive_analysis.json")
    
except Exception as e:
    print(f"Error during analysis: {e}")
    import traceback
    traceback.print_exc()

# Step 4.9.1: Advanced correlation analysis and performance summary
print("\n=== STEP 4.9.1: ADVANCED CORRELATION ANALYSIS ===")

try:
    # === CORRELATION ANALYSIS BETWEEN FEATURES ===
    print("Correlation analysis between features...")
    
    # Calculate statistics per class
    class_stats = {}
    for class_idx, class_name in enumerate(['Negative', 'Neutral', 'Positive']):
        class_mask = (y_true == class_idx)
        class_probs = prediction_probabilities[class_mask]
        class_predictions = y_pred[class_mask]
        
        if len(class_probs) > 0:
            class_stats[class_name] = {
                'count': int(np.sum(class_mask)),
                'mean_confidence': float(np.mean(np.max(class_probs, axis=1))),
                'correct_predictions': int(np.sum(class_predictions == class_idx)),
                'accuracy': float(np.sum(class_predictions == class_idx) / len(class_predictions)),
                'mean_prob_own_class': float(np.mean(class_probs[:, class_idx])),
                'std_prob_own_class': float(np.std(class_probs[:, class_idx]))
            }
    
    # Display per-class statistics
    print(f"\nDETAILED STATISTICS PER CLASS:")
    print("="*65)
    for class_name, stats in class_stats.items():
        print(f"\n{class_name.upper()}:")
        print(f"  Samples: {stats['count']}")
        print(f"  Accuracy: {stats['accuracy']:.4f} ({stats['accuracy']*100:.2f}%)")
        print(f"  Mean confidence: {stats['mean_confidence']:.4f}")
        print(f"  Mean prob (own class): {stats['mean_prob_own_class']:.4f} ± {stats['std_prob_own_class']:.4f}")
    
    # === EXTENDED CORRELATION MATRIX ===
    print(f"\nEXTENDED CORRELATION ANALYSIS:")
    print("-"*45)
    
    # Create figure for extended correlations
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. Probability correlation heatmap by true class
    true_class_probs = []
    for i in range(3):
        class_mask = (y_true == i)
        if np.sum(class_mask) > 0:
            true_class_probs.append(np.mean(prediction_probabilities[class_mask], axis=0))
    
    true_class_corr = np.corrcoef(true_class_probs)
    sns.heatmap(true_class_corr,
                annot=True,
                fmt='.3f',
                cmap='RdBu_r',
                center=0,
                xticklabels=['Negative', 'Neutral', 'Positive'],
                yticklabels=['Negative', 'Neutral', 'Positive'],
                ax=axes[0,0])
    axes[0,0].set_title('Probability Correlation by True Class', fontweight='bold')
    
    # 2. Correlation between confidence and accuracy
    confidence_scores = np.max(prediction_probabilities, axis=1)
    correct_predictions = (y_true == y_pred).astype(int)
    
    # Create confidence bins for analysis
    confidence_bins = np.linspace(0.3, 1.0, 8)
    bin_centers = (confidence_bins[:-1] + confidence_bins[1:]) / 2
    bin_accuracies = []
    
    for i in range(len(confidence_bins)-1):
        mask = (confidence_scores >= confidence_bins[i]) & (confidence_scores < confidence_bins[i+1])
        if np.sum(mask) > 0:
            bin_accuracies.append(np.mean(correct_predictions[mask]))
        else:
            bin_accuracies.append(0)
    
    axes[0,1].bar(bin_centers, bin_accuracies, width=0.08, alpha=0.7, color='lightcoral')
    axes[0,1].plot(bin_centers, bin_accuracies, 'ro-', linewidth=2)
    axes[0,1].set_xlabel('Confidence Level')
    axes[0,1].set_ylabel('Accuracy')
    axes[0,1].set_title('Confidence vs Accuracy Correlation', fontweight='bold')
    axes[0,1].grid(True, alpha=0.3)
    
    # 3. Error distribution by confidence level
    error_mask = (y_true != y_pred)
    error_confidences = confidence_scores[error_mask]
    correct_confidences = confidence_scores[~error_mask]
    
    axes[1,0].hist(correct_confidences, bins=20, alpha=0.7, label='Correct Predictions', 
                   color='lightgreen', density=True)
    axes[1,0].hist(error_confidences, bins=20, alpha=0.7, label='Errors', 
                   color='lightcoral', density=True)
    axes[1,0].set_xlabel('Confidence Level')
    axes[1,0].set_ylabel('Density')
    axes[1,0].set_title('Confidence Distribution: Correct vs Errors', fontweight='bold')
    axes[1,0].legend()
    
    # 4. Probability transition matrix
    prob_transitions = np.zeros((3, 3))
    for true_idx in range(3):
        for pred_idx in range(3):
            mask = (y_true == true_idx) & (y_pred == pred_idx)
            if np.sum(mask) > 0:
                prob_transitions[true_idx, pred_idx] = np.mean(prediction_probabilities[mask, pred_idx])
    
    sns.heatmap(prob_transitions,
                annot=True,
                fmt='.3f',
                cmap='YlOrRd',
                xticklabels=['Pred: Negative', 'Pred: Neutral', 'Pred: Positive'],
                yticklabels=['True: Negative', 'True: Neutral', 'True: Positive'],
                ax=axes[1,1])
    axes[1,1].set_title('Mean Probabilities by Transition', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'correlation_analysis.png'), dpi=300, bbox_inches='tight')
    plt.show()
    
    # === FINAL PERFORMANCE SUMMARY ===
    print(f"\nFINAL MODEL PERFORMANCE SUMMARY:")
    print("="*55)
    
    # Calculate robustness metrics
    confidence_accuracy_corr = np.corrcoef(confidence_scores, correct_predictions)[0,1]
    calibration_error = np.mean(np.abs(confidence_scores - correct_predictions))
    
    print(f"Global Performance Metrics:")
    print(f"  Accuracy: {overall_accuracy:.4f} ({overall_accuracy*100:.2f}%)")
    print(f"  Confidence-Accuracy Correlation: {confidence_accuracy_corr:.4f}")
    print(f"  Calibration Error: {calibration_error:.4f}")
    print(f"  Mean Confidence: {np.mean(confidence_scores):.4f}")
    
    print(f"\nRobustness Metrics:")
    print(f"  High Confidence Predictions (>0.9): {np.sum(confidence_scores > 0.9)} ({np.sum(confidence_scores > 0.9)/len(confidence_scores)*100:.1f}%)")
    print(f"  Uncertain Predictions (0.4-0.6): {np.sum((confidence_scores > 0.4) & (confidence_scores < 0.6))}")
    print(f"  Confident Errors (>0.8): {np.sum((confidence_scores > 0.8) & error_mask)}")
    
    # Determine overall model quality
    if overall_accuracy > 0.85:
        quality = "EXCELLENT"
    elif overall_accuracy > 0.75:
        quality = "GOOD" 
    elif overall_accuracy > 0.65:
        quality = "AVERAGE"
    else:
        quality = "POOR"
    
    print(f"\nOVERALL MODEL EVALUATION: {quality}")
    print(f"   Performance: {overall_accuracy*100:.1f}% accuracy")
    print(f"   Calibration: {'Well calibrated' if calibration_error < 0.1 else 'Poorly calibrated'}")
    print(f"   Confidence: {'Appropriate' if 0.7 < np.mean(confidence_scores) < 0.9 else 'Needs adjustment'}")
    
    # Save correlation analysis
    correlation_analysis = {
        'timestamp': datetime.now().isoformat(),
        'class_statistics': class_stats,
        'correlation_metrics': {
            'confidence_accuracy_correlation': float(confidence_accuracy_corr),
            'calibration_error': float(calibration_error),
            'mean_confidence': float(np.mean(confidence_scores))
        },
        'robustness_metrics': {
            'high_confidence_predictions': int(np.sum(confidence_scores > 0.9)),
            'uncertain_predictions': int(np.sum((confidence_scores > 0.4) & (confidence_scores < 0.6))),
            'confident_errors': int(np.sum((confidence_scores > 0.8) & error_mask))
        },
        'correlation_matrices': {
            'true_class_correlation': true_class_corr.tolist(),
            'probability_transitions': prob_transitions.tolist()
        },
        'model_quality_assessment': quality
    }
    
    with open(os.path.join(output_dir, 'correlation_analysis.json'), 'w') as f:
        json.dump(correlation_analysis, f, indent=2)
    
    print(f"\nCorrelation analysis saved in: {output_dir}/correlation_analysis.json")
    
except Exception as e:
    print(f"Error during correlation analysis: {e}")
    import traceback
    traceback.print_exc()

# Step 4.10: Final model saving
print("\n=== STEP 4.10: FINAL MODEL SAVING ===")

try:
    # Create folder for final model
    final_model_dir = os.path.join(output_dir, "final_model")
    os.makedirs(final_model_dir, exist_ok=True)
    
    print(f"Saving trained model...")
    
    # Save model and tokenizer
    trainer.save_model(final_model_dir)
    tokenizer.save_pretrained(final_model_dir)
    
    print(f"Model saved in: {final_model_dir}")
    
    # Create model configuration file
    model_config = {
        'model_name': 'FinBERT-Financial-Sentiment',
        'base_model': model_name,
        'num_labels': num_labels,
        'label_mapping': {
            '0': 'Negative',
            '1': 'Neutral', 
            '2': 'Positive'
        },
        'training_dataset': metadata['dataset_name'],
        'training_date': datetime.now().isoformat(),
        'final_metrics': final_eval if 'final_eval' in locals() else None,
        'training_args': training_args.to_dict(),
        'model_size_mb': sum(os.path.getsize(os.path.join(final_model_dir, f)) 
                            for f in os.listdir(final_model_dir) 
                            if os.path.isfile(os.path.join(final_model_dir, f))) / (1024*1024)
    }
    
    with open(os.path.join(final_model_dir, 'model_info.json'), 'w', encoding='utf-8') as f:
        json.dump(model_config, f, indent=2, default=str, ensure_ascii=False)
    
    # List saved files
    saved_files = os.listdir(final_model_dir)
    print(f"\nSaved files:")
    for file in saved_files:
        file_path = os.path.join(final_model_dir, file)
        if os.path.isfile(file_path):
            size_mb = os.path.getsize(file_path) / (1024*1024)
            print(f"  - {file} ({size_mb:.1f} MB)")
    
    print(f"\nTotal model size: {model_config['model_size_mb']:.1f} MB")
    
    # Instructions to load the model later
    print(f"\nTo load this model later:")
    print(f"```python")
    print(f"from transformers import AutoModelForSequenceClassification, AutoTokenizer")
    print(f"")
    print(f"model = AutoModelForSequenceClassification.from_pretrained('{final_model_dir}')")
    print(f"tokenizer = AutoTokenizer.from_pretrained('{final_model_dir}')")
    print(f"```")
    
except Exception as e:
    print(f"Error during saving: {e}")

# Step 4.11: Quick inference test
print("\n=== STEP 4.11: INFERENCE TEST ===")

def predict_sentiment(text, model, tokenizer, device):
    """Predict sentiment of a text"""
    # Tokenize text
    inputs = tokenizer(text, return_tensors="pt", truncation=True, padding=True, max_length=512)
    inputs = {k: v.to(device) for k, v in inputs.items()}
    
    # Prediction
    model.eval()
    with torch.no_grad():
        outputs = model(**inputs)
        predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
    
    # Convert to probabilities
    probs = predictions.cpu().numpy()[0]
    predicted_class = np.argmax(probs)
    
    return predicted_class, probs

# Test examples
test_texts = [
    "Apple stock reaches new all-time high after strong quarterly earnings",
    "Company faces major losses due to market volatility", 
    "Quarterly results meet analyst expectations",
    "Stock market shows mixed performance today",
    "Strong revenue growth drives investor confidence"
]

print("Inference test on examples:")
print("-"*70)

sentiment_names = ['Negative', 'Neutral', 'Positive']

for i, text in enumerate(test_texts, 1):
    try:
        predicted_class, probs = predict_sentiment(text, model, tokenizer, device)
        
        print(f"\nExample {i}:")
        print(f"Text: \"{text}\"")
        print(f"Prediction: {sentiment_names[predicted_class]} ({probs[predicted_class]:.3f})")
        print(f"Probabilities: Negative={probs[0]:.3f}, Neutral={probs[1]:.3f}, Positive={probs[2]:.3f}")
        
    except Exception as e:
        print(f"Error for example {i}: {e}")

print(f"\nInference test completed")

# Step 4.12: Export model as pickle file
print("\n=== STEP 4.12: EXPORT MODEL AS PICKLE ===")

import pickle
import joblib

try:
    # Create export directory
    export_dir = os.path.join(output_dir, "model_export")
    os.makedirs(export_dir, exist_ok=True)
    
    print("Preparing model for pickle export...")
    
    # Move model to CPU for serialization (pickle works better with CPU models)
    model_cpu = model.cpu()
    tokenizer_cpu = tokenizer  # Tokenizer doesn't need device movement
    
    # Create a complete model package
    model_package = {
        'model': model_cpu,
        'tokenizer': tokenizer_cpu,
        'label_mapping': {
            0: 'Negative',
            1: 'Neutral', 
            2: 'Positive'
        },
        'model_info': {
            'model_name': model_name,
            'num_labels': num_labels,
            'training_date': datetime.now().isoformat(),
            'final_metrics': final_eval if 'final_eval' in locals() else None
        },
        'predict_function': predict_sentiment
    }
    
    # Export using pickle
    pickle_path = os.path.join(export_dir, 'finbert_model.pkl')
    print(f"Saving model with pickle...")
    with open(pickle_path, 'wb') as f:
        pickle.dump(model_package, f, protocol=pickle.HIGHEST_PROTOCOL)
    
    # Also export using joblib (often more efficient for large models)
    joblib_path = os.path.join(export_dir, 'finbert_model.joblib')
    print(f"Saving model with joblib...")
    joblib.dump(model_package, joblib_path, compress=3)
    
    # Export just the model weights (smaller file)
    model_only_path = os.path.join(export_dir, 'finbert_model_only.pkl')
    print(f"Saving model weights only...")
    with open(model_only_path, 'wb') as f:
        pickle.dump({
            'model_state_dict': model_cpu.state_dict(),
            'model_config': model_cpu.config,
            'tokenizer': tokenizer_cpu,
            'label_mapping': model_package['label_mapping']
        }, f)
    
    # Get file sizes
    pickle_size = os.path.getsize(pickle_path) / (1024*1024)
    joblib_size = os.path.getsize(joblib_path) / (1024*1024)
    model_only_size = os.path.getsize(model_only_path) / (1024*1024)
    
    print(f"\nModel export completed successfully!")
    print(f"Export files created:")
    print(f"  - Complete model (pickle): {pickle_path} ({pickle_size:.1f} MB)")
    print(f"  - Complete model (joblib): {joblib_path} ({joblib_size:.1f} MB)")
    print(f"  - Model weights only: {model_only_path} ({model_only_size:.1f} MB)")
    
    # Create usage instructions
    usage_instructions = """
# HOW TO LOAD THE EXPORTED MODEL:

## Method 1: Load complete model with pickle
import pickle
import torch

# Load the complete model package
with open('finbert_model.pkl', 'rb') as f:
    model_package = pickle.load(f)

model = model_package['model']
tokenizer = model_package['tokenizer']
label_mapping = model_package['label_mapping']

# Use the model
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = model.to(device)

def predict_sentiment(text):
    inputs = tokenizer(text, return_tensors="pt", truncation=True, padding=True, max_length=512)
    inputs = {k: v.to(device) for k, v in inputs.items()}
    
    model.eval()
    with torch.no_grad():
        outputs = model(**inputs)
        predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
    
    probs = predictions.cpu().numpy()[0]
    predicted_class = torch.argmax(predictions, dim=-1).item()
    
    return label_mapping[predicted_class], probs

## Method 2: Load with joblib (often faster)
import joblib

model_package = joblib.load('finbert_model.joblib')
# Then use same as Method 1

## Method 3: Load only model weights (smallest file)
import pickle
from transformers import AutoModelForSequenceClassification, AutoTokenizer

with open('finbert_model_only.pkl', 'rb') as f:
    model_data = pickle.load(f)

# Recreate model and load weights
model = AutoModelForSequenceClassification.from_pretrained('ProsusAI/finbert', num_labels=3)
model.load_state_dict(model_data['model_state_dict'])
tokenizer = model_data['tokenizer']
    """
    
    # Save usage instructions
    instructions_path = os.path.join(export_dir, 'usage_instructions.txt')
    with open(instructions_path, 'w', encoding='utf-8') as f:
        f.write(usage_instructions)
    
    print(f"  - Usage instructions: {instructions_path}")
    
    # Move model back to original device if it was on GPU
    if device.type == 'cuda':
        model = model.to(device)
        print(f"\nModel moved back to {device}")
    
    print(f"\nExport directory: {export_dir}")
    print(f"Your model is now ready for deployment!")
    
except Exception as e:
    print(f"Error during model export: {e}")
    import traceback
    traceback.print_exc()
    
    # Make sure model is back on original device in case of error
    if device.type == 'cuda':
        model = model.to(device)