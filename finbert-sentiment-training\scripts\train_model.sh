#!/bin/bash

# FinBERT Training Script
# Usage: ./scripts/train_model.sh [data_file] [text_column] [label_column]

set -e  # Exit on any error

# Default parameters
DATA_FILE=${1:-"data/raw/data.csv"}
TEXT_COLUMN=${2:-"summary_detail_with_title"}
LABEL_COLUMN=${3:-"labels"}
OUTPUT_DIR=${4:-"models"}
SAMPLE_SIZE=${5:-""}

echo "========================================="
echo "FinBERT Sentiment Analysis Training"
echo "========================================="
echo "Data file: $DATA_FILE"
echo "Text column: $TEXT_COLUMN"
echo "Label column: $LABEL_COLUMN"
echo "Output directory: $OUTPUT_DIR"
if [ ! -z "$SAMPLE_SIZE" ]; then
    echo "Sample size: $SAMPLE_SIZE"
fi
echo "========================================="

# Check if data file exists
if [ ! -f "$DATA_FILE" ]; then
    echo "Error: Data file '$DATA_FILE' not found!"
    exit 1
fi

# Create necessary directories
mkdir -p logs
mkdir -p models/checkpoints
mkdir -p models/final
mkdir -p results/metrics
mkdir -p results/plots

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    echo "Activating virtual environment..."
    source venv/bin/activate
fi

# Check if required packages are installed
python -c "import torch, transformers, pandas" 2>/dev/null || {
    echo "Error: Required packages not installed. Run: pip install -r requirements.txt"
    exit 1
}

# Run training
echo "Starting FinBERT training..."
echo "Timestamp: $(date)"

if [ ! -z "$SAMPLE_SIZE" ]; then
    python src/train.py \
        --data_file "$DATA_FILE" \
        --text_column "$TEXT_COLUMN" \
        --label_column "$LABEL_COLUMN" \
        --output_dir "$OUTPUT_DIR" \
        --sample_size "$SAMPLE_SIZE"
else
    python src/train.py \
        --data_file "$DATA_FILE" \
        --text_column "$TEXT_COLUMN" \
        --label_column "$LABEL_COLUMN" \
        --output_dir "$OUTPUT_DIR"
fi

echo "========================================="
echo "Training completed successfully!"
echo "Timestamp: $(date)"
echo "Check logs/ directory for training logs"
echo "Check models/ directory for saved models"
echo "Check results/ directory for metrics and plots"
echo "========================================="
