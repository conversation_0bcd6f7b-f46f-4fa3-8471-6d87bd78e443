"""
Simple FinBERT Training Script - Hugging Face Optimized
Train FinBERT for financial sentiment analysis using Hugging Face ecosystem.
"""

import os
import pandas as pd
import numpy as np
from datasets import Dataset
from transformers import (
    AutoTokenizer, 
    AutoModelForSequenceClassification, 
    TrainingArguments, 
    Trainer,
    EarlyStoppingCallback
)
from sklearn.metrics import accuracy_score, precision_recall_fscore_support
import torch
import argparse
from datetime import datetime

def preprocess_text(text):
    """Simple text preprocessing for financial texts."""
    if pd.isna(text):
        return ""
    # Basic cleaning while preserving financial terms
    text = str(text).strip()
    return text

def compute_metrics(eval_pred):
    """Compute metrics for evaluation."""
    predictions, labels = eval_pred
    predictions = np.argmax(predictions, axis=1)
    
    accuracy = accuracy_score(labels, predictions)
    precision, recall, f1, _ = precision_recall_fscore_support(labels, predictions, average='macro')
    
    return {
        'accuracy': accuracy,
        'f1': f1,
        'precision': precision,
        'recall': recall
    }

def main():
    parser = argparse.ArgumentParser(description='Train FinBERT for sentiment analysis')
    parser.add_argument('--data_file', type=str, default='data/raw_data.csv', help='Path to training data')
    parser.add_argument('--text_column', type=str, default='summary_detail_with_title', help='Text column name')
    parser.add_argument('--label_column', type=str, default='labels', help='Label column name')
    parser.add_argument('--output_dir', type=str, default='./model_files', help='Output directory')
    parser.add_argument('--max_length', type=int, default=512, help='Max sequence length')
    parser.add_argument('--batch_size', type=int, default=16, help='Batch size')
    parser.add_argument('--num_epochs', type=int, default=3, help='Number of epochs')
    parser.add_argument('--learning_rate', type=float, default=2e-5, help='Learning rate')
    parser.add_argument('--sample_size', type=int, help='Sample size for testing')
    
    args = parser.parse_args()
    
    print("🚀 Starting FinBERT Training (Hugging Face Optimized)")
    print(f"📁 Data file: {args.data_file}")
    print(f"🎯 Output: {args.output_dir}")
    
    # Load data
    print("📊 Loading data...")
    df = pd.read_csv(args.data_file)
    
    if args.sample_size:
        df = df.sample(n=args.sample_size, random_state=42)
        print(f"🔬 Using sample of {args.sample_size} rows for testing")
    
    # Basic preprocessing
    df['text'] = df[args.text_column].apply(preprocess_text)
    df = df.dropna(subset=['text', args.label_column])
    
    print(f"📋 Dataset size: {len(df)} samples")
    print(f"📊 Label distribution:\n{df[args.label_column].value_counts()}")
    
    # Split data
    from sklearn.model_selection import train_test_split
    train_df, eval_df = train_test_split(df, test_size=0.2, random_state=42, stratify=df[args.label_column])
    
    # Load model and tokenizer
    print("🤗 Loading FinBERT model and tokenizer...")
    model_name = "ProsusAI/finbert"
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForSequenceClassification.from_pretrained(
        model_name, 
        num_labels=3,
        problem_type="single_label_classification"
    )
    
    # Tokenization function
    def tokenize_function(examples):
        return tokenizer(
            examples['text'],
            truncation=True,
            padding='max_length',
            max_length=args.max_length,
            return_tensors="pt"
        )
    
    # Create datasets
    print("🔄 Creating datasets...")
    train_dataset = Dataset.from_pandas(train_df[['text', args.label_column]])
    eval_dataset = Dataset.from_pandas(eval_df[['text', args.label_column]])
    
    # Rename label column
    train_dataset = train_dataset.rename_column(args.label_column, 'labels')
    eval_dataset = eval_dataset.rename_column(args.label_column, 'labels')
    
    # Tokenize
    train_dataset = train_dataset.map(tokenize_function, batched=True)
    eval_dataset = eval_dataset.map(tokenize_function, batched=True)
    
    # Set format
    train_dataset.set_format('torch', columns=['input_ids', 'attention_mask', 'labels'])
    eval_dataset.set_format('torch', columns=['input_ids', 'attention_mask', 'labels'])
    
    # Training arguments
    training_args = TrainingArguments(
        output_dir='./training_output',
        num_train_epochs=args.num_epochs,
        per_device_train_batch_size=args.batch_size,
        per_device_eval_batch_size=args.batch_size,
        learning_rate=args.learning_rate,
        weight_decay=0.01,
        logging_dir='./logs',
        logging_steps=50,
        evaluation_strategy="epoch",
        save_strategy="epoch",
        load_best_model_at_end=True,
        metric_for_best_model="f1",
        greater_is_better=True,
        save_total_limit=2,
        seed=42,
        fp16=torch.cuda.is_available(),
    )
    
    # Create trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        tokenizer=tokenizer,
        compute_metrics=compute_metrics,
        callbacks=[EarlyStoppingCallback(early_stopping_patience=2)]
    )
    
    # Train
    print("🎯 Starting training...")
    start_time = datetime.now()
    
    train_result = trainer.train()
    
    end_time = datetime.now()
    training_time = (end_time - start_time).total_seconds()
    
    print(f"✅ Training completed in {training_time:.2f} seconds")
    
    # Evaluate
    print("📊 Evaluating model...")
    eval_result = trainer.evaluate()
    
    print(f"🎯 Final Results:")
    print(f"   Accuracy: {eval_result['eval_accuracy']:.4f}")
    print(f"   F1 Score: {eval_result['eval_f1']:.4f}")
    print(f"   Precision: {eval_result['eval_precision']:.4f}")
    print(f"   Recall: {eval_result['eval_recall']:.4f}")
    
    # Save model in Hugging Face format
    print(f"💾 Saving model to {args.output_dir}")
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Save model and tokenizer
    model.save_pretrained(args.output_dir)
    tokenizer.save_pretrained(args.output_dir)
    
    # Save training info
    training_info = {
        "model_name": "FinBERT Financial Sentiment",
        "base_model": model_name,
        "training_time_seconds": training_time,
        "final_metrics": eval_result,
        "training_args": {
            "num_epochs": args.num_epochs,
            "batch_size": args.batch_size,
            "learning_rate": args.learning_rate,
            "max_length": args.max_length
        },
        "data_info": {
            "total_samples": len(df),
            "train_samples": len(train_df),
            "eval_samples": len(eval_df),
            "label_distribution": df[args.label_column].value_counts().to_dict()
        }
    }
    
    import json
    with open(os.path.join(args.output_dir, 'training_info.json'), 'w') as f:
        json.dump(training_info, f, indent=2, default=str)
    
    print("🎉 Training complete! Model saved in Hugging Face format.")
    print(f"📁 Model location: {args.output_dir}")
    print("\n🚀 You can now use your model with:")
    print(f"   from transformers import AutoModelForSequenceClassification, AutoTokenizer")
    print(f"   model = AutoModelForSequenceClassification.from_pretrained('{args.output_dir}')")
    print(f"   tokenizer = AutoTokenizer.from_pretrained('{args.output_dir}')")

if __name__ == "__main__":
    main()
