from flask import Flask, render_template, request, jsonify
import pickle
import torch
import numpy as np
from transformers import AutoModelForSequenceClassification, AutoTokenizer
import re
import os
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

class FinBERTAnalyzer:
    def __init__(self, model_path):
        """Initialize the FinBERT analyzer with the trained model."""
        self.model = None
        self.tokenizer = None
        self.label_mapping = {0: 'Negative', 1: 'Neutral', 2: 'Positive'}
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.load_model(model_path)
    
    def load_model(self, model_path):
        """Load the FinBERT model from pickle file."""
        try:
            logger.info(f"Loading model from {model_path}")
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            # Recreate model and load weights
            self.model = AutoModelForSequenceClassification.from_pretrained(
                'ProsusAI/finbert', 
                num_labels=3
            )
            self.model.load_state_dict(model_data['model_state_dict'])
            self.tokenizer = model_data['tokenizer']
            self.label_mapping = model_data.get('label_mapping', self.label_mapping)
            
            # Move model to device
            self.model = self.model.to(self.device)
            self.model.eval()
            
            logger.info(f"Model loaded successfully on {self.device}")
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise
    
    def predict_sentiment(self, text):
        """Predict sentiment of the given text."""
        try:
            # Tokenize text
            inputs = self.tokenizer(
                text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=512
            )
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

            # Get predictions
            with torch.no_grad():
                outputs = self.model(**inputs)
                predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)

            # Convert to probabilities
            probs = predictions.cpu().numpy()[0]
            predicted_class = np.argmax(probs)

            return {
                'sentiment': self.label_mapping[predicted_class],
                'confidence': float(probs[predicted_class]),
                'probabilities': {
                    'Negative': float(probs[0]),
                    'Neutral': float(probs[1]),
                    'Positive': float(probs[2])
                }
            }

        except Exception as e:
            logger.error(f"Error in prediction: {e}")
            raise

    def generate_summary(self, text, max_sentences=3):
        """Generate a summary of the text using extractive summarization."""
        try:
            # Split text into sentences
            sentences = re.split(r'[.!?]+', text)
            sentences = [s.strip() for s in sentences if s.strip()]

            if len(sentences) <= max_sentences:
                return text.strip()

            # Simple extractive summarization based on sentence length and position
            # Prioritize first and last sentences, and longer sentences
            sentence_scores = []

            for i, sentence in enumerate(sentences):
                score = 0
                # Position score (first and last sentences are important)
                if i == 0 or i == len(sentences) - 1:
                    score += 2
                elif i < len(sentences) * 0.3:  # First third
                    score += 1

                # Length score (longer sentences often contain more information)
                word_count = len(sentence.split())
                if word_count > 10:
                    score += 1
                if word_count > 20:
                    score += 1

                # Financial keywords score
                financial_keywords = [
                    'revenue', 'profit', 'earnings', 'stock', 'market', 'growth',
                    'sales', 'investment', 'financial', 'company', 'business',
                    'quarter', 'annual', 'percent', '%', 'million', 'billion'
                ]

                sentence_lower = sentence.lower()
                keyword_count = sum(1 for keyword in financial_keywords if keyword in sentence_lower)
                score += keyword_count * 0.5

                sentence_scores.append((score, i, sentence))

            # Sort by score and select top sentences
            sentence_scores.sort(reverse=True, key=lambda x: x[0])
            selected_sentences = sorted(
                sentence_scores[:max_sentences],
                key=lambda x: x[1]  # Sort by original position
            )

            summary = '. '.join([s[2] for s in selected_sentences])
            if not summary.endswith('.'):
                summary += '.'

            return summary

        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return text[:500] + "..." if len(text) > 500 else text

    def generate_justification(self, text, sentiment, confidence, probabilities):
        """Generate justification for the sentiment prediction."""
        try:
            justifications = []

            # Confidence-based justification
            if confidence > 0.8:
                justifications.append({
                    'type': 'confidence',
                    'text': f"The model is highly confident ({confidence:.1%}) in this {sentiment.lower()} classification.",
                    'icon': 'fas fa-check-circle',
                    'class': f'justification-{sentiment.lower()}'
                })
            elif confidence > 0.6:
                justifications.append({
                    'type': 'confidence',
                    'text': f"The model shows moderate confidence ({confidence:.1%}) in this {sentiment.lower()} classification.",
                    'icon': 'fas fa-info-circle',
                    'class': f'justification-{sentiment.lower()}'
                })
            else:
                justifications.append({
                    'type': 'confidence',
                    'text': f"The model has lower confidence ({confidence:.1%}) in this classification, suggesting mixed or ambiguous sentiment.",
                    'icon': 'fas fa-exclamation-triangle',
                    'class': 'justification-neutral'
                })

            # Keyword-based justification
            positive_keywords = [
                'profit', 'growth', 'increase', 'rise', 'gain', 'success', 'strong',
                'beat', 'exceed', 'outperform', 'record', 'high', 'surge', 'boost',
                'positive', 'good', 'excellent', 'impressive', 'robust'
            ]

            negative_keywords = [
                'loss', 'decline', 'fall', 'drop', 'decrease', 'weak', 'poor',
                'miss', 'disappoint', 'concern', 'risk', 'problem', 'challenge',
                'negative', 'bad', 'terrible', 'plummet', 'crash', 'crisis'
            ]

            neutral_keywords = [
                'stable', 'maintain', 'steady', 'unchanged', 'flat', 'mixed',
                'moderate', 'cautious', 'wait', 'monitor', 'expect', 'forecast'
            ]

            text_lower = text.lower()

            # Find relevant keywords
            found_positive = [kw for kw in positive_keywords if kw in text_lower]
            found_negative = [kw for kw in negative_keywords if kw in text_lower]
            found_neutral = [kw for kw in neutral_keywords if kw in text_lower]

            if sentiment == 'Positive' and found_positive:
                justifications.append({
                    'type': 'keywords',
                    'text': f"Positive indicators found: {', '.join(found_positive[:5])}",
                    'icon': 'fas fa-arrow-up',
                    'class': 'justification-positive'
                })
            elif sentiment == 'Negative' and found_negative:
                justifications.append({
                    'type': 'keywords',
                    'text': f"Negative indicators found: {', '.join(found_negative[:5])}",
                    'icon': 'fas fa-arrow-down',
                    'class': 'justification-negative'
                })
            elif sentiment == 'Neutral' and found_neutral:
                justifications.append({
                    'type': 'keywords',
                    'text': f"Neutral indicators found: {', '.join(found_neutral[:5])}",
                    'icon': 'fas fa-minus',
                    'class': 'justification-neutral'
                })

            # Probability distribution justification
            prob_diff = max(probabilities.values()) - sorted(probabilities.values())[-2]
            if prob_diff < 0.2:
                justifications.append({
                    'type': 'distribution',
                    'text': "The sentiment probabilities are close, indicating mixed or ambiguous sentiment in the text.",
                    'icon': 'fas fa-balance-scale',
                    'class': 'justification-neutral'
                })
            elif prob_diff > 0.5:
                justifications.append({
                    'type': 'distribution',
                    'text': f"Clear sentiment distinction with {sentiment.lower()} sentiment significantly higher than alternatives.",
                    'icon': 'fas fa-chart-bar',
                    'class': f'justification-{sentiment.lower()}'
                })

            return justifications

        except Exception as e:
            logger.error(f"Error generating justification: {e}")
            return [{
                'type': 'error',
                'text': 'Unable to generate detailed justification.',
                'icon': 'fas fa-exclamation-triangle',
                'class': 'justification-neutral'
            }]

# Initialize the analyzer
MODEL_PATH = 'finbert_results/model_export/finbert_model_only.pkl'
analyzer = FinBERTAnalyzer(MODEL_PATH)

@app.route('/')
def index():
    """Main page with input form."""
    return render_template('index.html')

@app.route('/analyze', methods=['POST'])
def analyze():
    """Analyze the submitted text."""
    try:
        data = request.get_json()
        text = data.get('text', '').strip()

        if not text:
            return jsonify({'error': 'No text provided'}), 400

        if len(text) < 10:
            return jsonify({'error': 'Text too short for meaningful analysis'}), 400

        # Get sentiment prediction
        result = analyzer.predict_sentiment(text)

        # Generate summary
        summary = analyzer.generate_summary(text)

        # Generate justification
        justifications = analyzer.generate_justification(
            text,
            result['sentiment'],
            result['confidence'],
            result['probabilities']
        )

        # Add additional information
        result.update({
            'timestamp': datetime.now().isoformat(),
            'text_length': len(text),
            'word_count': len(text.split()),
            'summary': summary,
            'justifications': justifications,
            'analysis_metadata': {
                'model_name': 'FinBERT',
                'model_version': 'ProsusAI/finbert',
                'processing_time': 'Real-time'
            }
        })

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in analysis: {e}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
