# Step 1: Set Up the Environment
# Make sure you have the necessary libraries installed
# !pip install transformers datasets torch

# Step 2: Load the Dataset
from datasets import load_from_disk

# Load the preprocessed datasets
train_dataset = load_from_disk("preprocessed_datasets_original_splits/train")
test_dataset = load_from_disk("preprocessed_datasets_original_splits/test")

# Step 3: Load the FinBERT Model
from transformers import AutoModelForSequenceClassification, AutoTokenizer

# Load the FinBERT tokenizer and model
model_name = "ProsusAI/finbert"
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForSequenceClassification.from_pretrained(model_name, num_labels=3)

# Step 4: Configure Training Parameters
from transformers import TrainingArguments

training_args = TrainingArguments(
    output_dir='./results',          # output directory
    num_train_epochs=3,              # total number of training epochs
    per_device_train_batch_size=16,  # batch size per device during training
    per_device_eval_batch_size=64,   # batch size for evaluation
    warmup_steps=500,                 # number of warmup steps for learning rate scheduler
    weight_decay=0.01,               # strength of weight decay
    logging_dir='./logs',            # directory for storing logs
    logging_steps=10,
    evaluation_strategy="epoch",      # evaluate every epoch
    save_strategy="epoch",            # save model every epoch
)

# Step 5: Create a Trainer
from transformers import Trainer, TrainingArguments

trainer = Trainer(
    model=model,                         # the instantiated 🤗 Transformers model to be trained
    args=training_args,                  # training arguments, defined above
    train_dataset=train_dataset,         # training dataset
    eval_dataset=test_dataset             # evaluation dataset
)

# Step 6: Train the Model
trainer.train()

# Step 7: Evaluate the Model
results = trainer.evaluate()

# Print the evaluation results
print("Evaluation results:", results)