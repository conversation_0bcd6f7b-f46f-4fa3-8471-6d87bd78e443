{"cells": [{"cell_type": "markdown", "id": "1b889c04", "metadata": {}, "source": ["# **Prétraitement des Données - Splits Originaux**\n", "\n", "Ce notebook implémente le prétraitement des données en utilisant uniquement les splits originaux fournis par Hugging Face (train et test), sans créer de split de validation supplémentaire.\n", "\n", "## **Étapes du prétraitement :**\n", "1. **Chargement du Dataset** : Utilisation des splits originaux train/test\n", "2. **Vérification des Caractéristiques** : Examen des colonnes et analyse des données\n", "3. **Nettoyage des Données** : Suppression des caractères spéciaux et gestion des valeurs nulles\n", "4. **Tokenisation** : Utilisation du tokenizer FinBERT avec longueur maximale de 512 tokens\n", "5. **Formatage pour PyTorch** : Configuration des datasets pour l'entraînement\n", "6. **Sauvegarde** : Datasets préprocessés prêts pour l'entraînement"]}, {"cell_type": "code", "execution_count": 7, "id": "b77adfe3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Toutes les bibliothèques ont été importées avec succès\n"]}], "source": ["# Installation des bibliothèques nécessaires\n", "# Décommentez la ligne suivante si les packages ne sont pas installés\n", "# !pip install datasets transformers pandas numpy torch\n", "\n", "# Imports des bibliothèques\n", "from datasets import load_dataset, Dataset\n", "from transformers import AutoTokenizer\n", "import pandas as pd\n", "import numpy as np\n", "import re\n", "from html import unescape\n", "import os\n", "import json\n", "\n", "print(\"Toutes les bibliothèques ont été importées avec succès\")"]}, {"cell_type": "code", "execution_count": 8, "id": "4be2c8cb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ÉTAPE 1: CHARGEMENT DU DATASET - SPLITS ORIGINAUX ===\n", "Dataset '<PERSON><PERSON><PERSON>/financial_news_sentiment_mixte_with_phrasebank_75' chargé avec succès\n", "Structure du dataset: DatasetDict({\n", "    train: Dataset({\n", "        features: ['summary_detail', 'title', 'summary_detail_with_title', 'topic', 'labels', '__index_level_0__', 'score_topic'],\n", "        num_rows: 4446\n", "    })\n", "    test: Dataset({\n", "        features: ['summary_detail', 'title', 'summary_detail_with_title', 'topic', 'labels', '__index_level_0__', 'score_topic'],\n", "        num_rows: 785\n", "    })\n", "})\n", "\n", "Splits originaux:\n", "  - Train: 4446 exemples\n", "  - Test: 785 exemples\n", "  - Total: 5231 exemples\n", "Dataset '<PERSON><PERSON><PERSON>/financial_news_sentiment_mixte_with_phrasebank_75' chargé avec succès\n", "Structure du dataset: DatasetDict({\n", "    train: Dataset({\n", "        features: ['summary_detail', 'title', 'summary_detail_with_title', 'topic', 'labels', '__index_level_0__', 'score_topic'],\n", "        num_rows: 4446\n", "    })\n", "    test: Dataset({\n", "        features: ['summary_detail', 'title', 'summary_detail_with_title', 'topic', 'labels', '__index_level_0__', 'score_topic'],\n", "        num_rows: 785\n", "    })\n", "})\n", "\n", "Splits originaux:\n", "  - Train: 4446 exemples\n", "  - Test: 785 exemples\n", "  - Total: 5231 exemples\n"]}], "source": ["# Étape 1: Chargement du Dataset avec les splits originaux\n", "print(\"=== ÉTAPE 1: CHARGEMENT DU DATASET - SPLITS ORIGINAUX ===\")\n", "dataset_name = \"<PERSON><PERSON><PERSON>/financial_news_sentiment_mixte_with_phrasebank_75\"\n", "\n", "try:\n", "    dataset = load_dataset(dataset_name)\n", "    print(f\"Dataset '{dataset_name}' chargé avec succès\")\n", "    print(f\"Structure du dataset: {dataset}\")\n", "    \n", "    # Utiliser directement les splits originaux\n", "    train_dataset = dataset['train']\n", "    test_dataset = dataset['test']\n", "    \n", "    print(f\"\\nSplits originaux:\")\n", "    print(f\"  - Train: {len(train_dataset)} exemples\")\n", "    print(f\"  - Test: {len(test_dataset)} exemples\")\n", "    print(f\"  - Total: {len(train_dataset) + len(test_dataset)} exemples\")\n", "    \n", "except Exception as e:\n", "    print(f\"Erreur lors du chargement: {e}\")"]}, {"cell_type": "code", "execution_count": 9, "id": "7909391d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== ÉTAPE 2: VÉRIFICATION DES CARACTÉRISTIQUES ===\n", "Caractéristiques du dataset:\n", "Train features: {'summary_detail': Value('string'), 'title': Value('string'), 'summary_detail_with_title': Value('string'), 'topic': Value('int64'), 'labels': Value('int64'), '__index_level_0__': Value('int64'), 'score_topic': Value('float64')}\n", "Test features: {'summary_detail': Value('string'), 'title': Value('string'), 'summary_detail_with_title': Value('string'), 'topic': Value('int64'), 'labels': Value('int64'), '__index_level_0__': Value('int64'), 'score_topic': Value('float64')}\n", "\n", "Exemples du dataset train (3 premiers):\n", "\n", "--- Exemple 1 ---\n", "Title: N/A...\n", "Summary_detail: N/A...\n", "Summary_detail_with_title: Operating profit improved by 27 % to EUR 579.8 mn from EUR 457.2 mn in 2006 ....\n", "Labels: 2\n", "Topic: 2\n", "\n", "--- Exemple 2 ---\n", "Title: N/A...\n", "Summary_detail: N/A...\n", "Summary_detail_with_title: Finnish construction group Lemminkainen Oyj HEL : LEM1S said today it has won a contract to provide ...\n", "Labels: 2\n", "Topic: 5\n", "\n", "--- Exemple 3 ---\n", "Title: N/A...\n", "Summary_detail: N/A...\n", "Summary_detail_with_title: HELSINKI ( AFX ) - Nokian Tyres reported a fourth quarter pretax profit of 61.5 mln eur , up from 48...\n", "Labels: 2\n", "Topic: 2\n", "\n", "Statistiques des longueurs de texte pour Train:\n", "  summary_detail - Min: 1.0, Max: 2531.0, <PERSON><PERSON><PERSON>: 384.4\n", "  title - Min: 23.0, <PERSON>: 327.0, <PERSON><PERSON><PERSON>: 86.6\n", "  summary_detail_with_title - Min: 9, <PERSON>: 2530, <PERSON><PERSON><PERSON>: 230.6\n", "\n", "Statistiques des longueurs de texte pour Test:\n", "  summary_detail - Min: 2.0, Max: 1304.0, <PERSON><PERSON><PERSON>: 397.6\n", "  title - Min: 22.0, <PERSON>: 221.0, <PERSON><PERSON><PERSON>: 82.5\n", "  summary_detail_with_title - Min: 21, <PERSON>: 1327, <PERSON><PERSON><PERSON>: 236.0\n"]}], "source": ["print(\"\\n=== ÉTAPE 2: VÉRIFICATION DES CARACTÉRISTIQUES ===\")\n", "\n", "# Examiner les caractéristiques du dataset\n", "print(\"Caractéristiques du dataset:\")\n", "print(f\"Train features: {train_dataset.features}\")\n", "print(f\"Test features: {test_dataset.features}\")\n", "\n", "# Examiner quelques exemples pour comprendre la structure\n", "print(\"\\nExemples du dataset train (3 premiers):\")\n", "for i in range(min(3, len(train_dataset))):\n", "    example = train_dataset[i]\n", "    print(f\"\\n--- Exemple {i+1} ---\")\n", "    title = example.get('title', 'N/A')\n", "    summary_detail = example.get('summary_detail', 'N/A')\n", "    summary_with_title = example.get('summary_detail_with_title', 'N/A')\n", "    print(f\"Title: {title[:100] if title else 'N/A'}...\")\n", "    print(f\"Summary_detail: {summary_detail[:100] if summary_detail else 'N/A'}...\")\n", "    print(f\"Summary_detail_with_title: {summary_with_title[:100] if summary_with_title else 'N/A'}...\")\n", "    print(f\"Labels: {example.get('labels', 'N/A')}\")\n", "    print(f\"Topic: {example.get('topic', 'N/A')}\")\n", "\n", "# Analyser les longueurs des textes pour les deux splits\n", "def analyze_text_lengths(dataset_split, split_name):\n", "    df = dataset_split.to_pandas()\n", "    print(f\"\\nStatistiques des longueurs de texte pour {split_name}:\")\n", "    print(f\"  summary_detail - Min: {df['summary_detail'].str.len().min()}, Max: {df['summary_detail'].str.len().max()}, Moyenne: {df['summary_detail'].str.len().mean():.1f}\")\n", "    print(f\"  title - Min: {df['title'].str.len().min()}, <PERSON>: {df['title'].str.len().max()}, Moyenne: {df['title'].str.len().mean():.1f}\")\n", "    print(f\"  summary_detail_with_title - Min: {df['summary_detail_with_title'].str.len().min()}, Max: {df['summary_detail_with_title'].str.len().max()}, Moyenne: {df['summary_detail_with_title'].str.len().mean():.1f}\")\n", "\n", "analyze_text_lengths(train_dataset, \"Train\")\n", "analyze_text_lengths(test_dataset, \"Test\")"]}, {"cell_type": "code", "execution_count": 10, "id": "764c0099", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== VÉRIFICATION DE LA DISTRIBUTION DES LABELS ===\n", "\n", "Distribution des labels dans Train:\n", "  Négatif (label 0): 402 (9.0%)\n", "  Neutre (label 1): 2721 (61.2%)\n", "  Positif (label 2): 1323 (29.8%)\n", "\n", "Distribution des labels dans Test:\n", "  Négatif (label 0): 79 (10.1%)\n", "  Neutre (label 1): 481 (61.3%)\n", "  Positif (label 2): 225 (28.7%)\n", "\n", "Comparaison des distributions:\n", "  Négatif: Train 9.0% vs Test 10.1% (diff: 1.0%)\n", "  Neutre: Train 61.2% vs Test 61.3% (diff: 0.1%)\n", "  Positif: Train 29.8% vs Test 28.7% (diff: 1.1%)\n"]}], "source": ["print(\"\\n=== VÉRIFICATION DE LA DISTRIBUTION DES LABELS ===\")\n", "\n", "# Vérifier la distribution des labels dans chaque split\n", "def check_label_distribution(dataset_split, split_name):\n", "    df = dataset_split.to_pandas()\n", "    label_dist = df['labels'].value_counts().sort_index()\n", "    print(f\"\\nDistribution des labels dans {split_name}:\")\n", "    total = len(df)\n", "    for label, count in label_dist.items():\n", "        percentage = (count / total) * 100\n", "        sentiment_name = ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>ti<PERSON>'][label]\n", "        print(f\"  {sentiment_name} (label {label}): {count} ({percentage:.1f}%)\")\n", "    \n", "    return label_dist\n", "\n", "train_dist = check_label_distribution(train_dataset, \"Train\")\n", "test_dist = check_label_distribution(test_dataset, \"Test\")\n", "\n", "# Comparaison des distributions\n", "print(f\"\\nComparaison des distributions:\")\n", "for label in [0, 1, 2]:\n", "    sentiment_name = ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>ti<PERSON>'][label]\n", "    train_pct = (train_dist.get(label, 0) / len(train_dataset)) * 100\n", "    test_pct = (test_dist.get(label, 0) / len(test_dataset)) * 100\n", "    diff = abs(train_pct - test_pct)\n", "    print(f\"  {sentiment_name}: Train {train_pct:.1f}% vs Test {test_pct:.1f}% (diff: {diff:.1f}%)\")"]}, {"cell_type": "code", "execution_count": 11, "id": "397d37ed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== ÉTAPE 3: NETTOYAGE DES DONNÉES ===\n", "\n", "Nettoyage du split Train...\n", "État avant nettoyage:\n", "  Nombre total de lignes: 4446\n", "  summary_detail: 2934 nulls, 4 vides\n", "  title: 2934 nulls, 0 vides\n", "  summary_detail_with_title: 0 nulls, 0 vides\n", "\n", "Suppression des valeurs nulles...\n", "  2934 lignes supprimées (valeurs nulles)\n", "Nettoyage du texte...\n", "État avant nettoyage:\n", "  Nombre total de lignes: 4446\n", "  summary_detail: 2934 nulls, 4 vides\n", "  title: 2934 nulls, 0 vides\n", "  summary_detail_with_title: 0 nulls, 0 vides\n", "\n", "Suppression des valeurs nulles...\n", "  2934 lignes supprimées (valeurs nulles)\n", "Nettoyage du texte...\n", "Suppression des textes vides après nettoyage...\n", "  4 lignes supprimées (textes trop courts)\n", "Suppression des doublons...\n", "  0 doublons supprimés\n", "\n", "État après nettoyage:\n", "  Lignes initiales: 4446\n", "  Lignes finales: 1508\n", "  Total supprimé: 2938 (66.1%)\n", "\n", "Vérification finale - valeurs nulles restantes:\n", "  summary_detail: 0 nulls\n", "  title: 0 nulls\n", "  summary_detail_with_title: 0 nulls\n", "  labels: 0 nulls\n", "\n", "Distribution des labels après nettoyage:\n", "  Négatif (label 0): 49 (3.2%)\n", "  Neutre (label 1): 898 (59.5%)\n", "  Positif (label 2): 561 (37.2%)\n", "Suppression des textes vides après nettoyage...\n", "  4 lignes supprimées (textes trop courts)\n", "Suppression des doublons...\n", "  0 doublons supprimés\n", "\n", "État après nettoyage:\n", "  Lignes initiales: 4446\n", "  Lignes finales: 1508\n", "  Total supprimé: 2938 (66.1%)\n", "\n", "Vérification finale - valeurs nulles restantes:\n", "  summary_detail: 0 nulls\n", "  title: 0 nulls\n", "  summary_detail_with_title: 0 nulls\n", "  labels: 0 nulls\n", "\n", "Distribution des labels après nettoyage:\n", "  Négatif (label 0): 49 (3.2%)\n", "  Neutre (label 1): 898 (59.5%)\n", "  Positif (label 2): 561 (37.2%)\n", "Train entièrement nettoyé: 1508 exemples de qualité\n", "\n", "Nettoyage du split Test...\n", "État avant nettoyage:\n", "  Nombre total de lignes: 785\n", "  summary_detail: 518 nulls, 1 vides\n", "  title: 518 nulls, 0 vides\n", "  summary_detail_with_title: 0 nulls, 0 vides\n", "\n", "Suppression des valeurs nulles...\n", "  518 lignes supprimées (valeurs nulles)\n", "Nettoyage du texte...\n", "Suppression des textes vides après nettoyage...\n", "  1 lignes supprimées (textes trop courts)\n", "Suppression des doublons...\n", "  0 doublons supprimés\n", "\n", "État après nettoyage:\n", "  Lignes initiales: 785\n", "  Lignes finales: 266\n", "  Total supprimé: 519 (66.1%)\n", "\n", "Vérification finale - valeurs nulles restantes:\n", "  summary_detail: 0 nulls\n", "  title: 0 nulls\n", "  summary_detail_with_title: 0 nulls\n", "  labels: 0 nulls\n", "\n", "Distribution des labels après nettoyage:\n", "  Négatif (label 0): 12 (4.5%)\n", "  Neutre (label 1): 155 (58.3%)\n", "  Positif (label 2): 99 (37.2%)\n", "Test entièrement nettoyé: 266 exemples de qualité\n", "\n", "Résumé après nettoyage:\n", "  - Train: 1508 exemples\n", "  - Test: 266 exemples\n", "  - Total: 1774 exemples\n", "Train entièrement nettoyé: 1508 exemples de qualité\n", "\n", "Nettoyage du split Test...\n", "État avant nettoyage:\n", "  Nombre total de lignes: 785\n", "  summary_detail: 518 nulls, 1 vides\n", "  title: 518 nulls, 0 vides\n", "  summary_detail_with_title: 0 nulls, 0 vides\n", "\n", "Suppression des valeurs nulles...\n", "  518 lignes supprimées (valeurs nulles)\n", "Nettoyage du texte...\n", "Suppression des textes vides après nettoyage...\n", "  1 lignes supprimées (textes trop courts)\n", "Suppression des doublons...\n", "  0 doublons supprimés\n", "\n", "État après nettoyage:\n", "  Lignes initiales: 785\n", "  Lignes finales: 266\n", "  Total supprimé: 519 (66.1%)\n", "\n", "Vérification finale - valeurs nulles restantes:\n", "  summary_detail: 0 nulls\n", "  title: 0 nulls\n", "  summary_detail_with_title: 0 nulls\n", "  labels: 0 nulls\n", "\n", "Distribution des labels après nettoyage:\n", "  Négatif (label 0): 12 (4.5%)\n", "  Neutre (label 1): 155 (58.3%)\n", "  Positif (label 2): 99 (37.2%)\n", "Test entièrement nettoyé: 266 exemples de qualité\n", "\n", "Résumé après nettoyage:\n", "  - Train: 1508 exemples\n", "  - Test: 266 exemples\n", "  - Total: 1774 exemples\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27892\\1920286622.py:50: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_no_nulls['summary_detail'] = df_no_nulls['summary_detail'].apply(clean_text)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27892\\1920286622.py:51: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_no_nulls['title'] = df_no_nulls['title'].apply(clean_text)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27892\\1920286622.py:52: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_no_nulls['summary_detail_with_title'] = df_no_nulls['summary_detail_with_title'].apply(clean_text)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27892\\1920286622.py:50: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_no_nulls['summary_detail'] = df_no_nulls['summary_detail'].apply(clean_text)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27892\\1920286622.py:51: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_no_nulls['title'] = df_no_nulls['title'].apply(clean_text)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27892\\1920286622.py:52: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_no_nulls['summary_detail_with_title'] = df_no_nulls['summary_detail_with_title'].apply(clean_text)\n"]}], "source": ["print(\"\\n=== ÉTAPE 3: NETTOYAGE DES DONNÉES ===\")\n", "\n", "def clean_text(text):\n", "    \"\"\"Fonction de nettoyage du texte\"\"\"\n", "    if pd.isna(text) or text is None:\n", "        return \"\"\n", "    \n", "    # Convertir en string si ce n'est pas déjà le cas\n", "    text = str(text)\n", "    \n", "    # Décoder les entités HTML\n", "    text = unescape(text)\n", "    \n", "    # Supprimer les balises HTML\n", "    text = re.sub(r'<[^>]+>', '', text)\n", "    \n", "    # Supprimer les caractères de contrôle et les espaces multiples\n", "    text = re.sub(r'[\\x00-\\x1f\\x7f-\\x9f]', ' ', text)\n", "    text = re.sub(r'\\s+', ' ', text)\n", "    \n", "    # Supprimer les espaces en début et fin\n", "    text = text.strip()\n", "    \n", "    return text\n", "\n", "def clean_dataset(dataset_split, split_name):\n", "    \"\"\"Nettoyer un split du dataset en supprimant toutes les valeurs nulles\"\"\"\n", "    print(f\"\\nNettoyage du split {split_name}...\")\n", "    \n", "    # Convertir en pandas pour faciliter le nettoyage\n", "    df = dataset_split.to_pandas()\n", "    initial_length = len(df)\n", "    \n", "    # Vérifier les valeurs nulles avant nettoyage\n", "    print(f\"État avant nettoyage:\")\n", "    print(f\"  Nombre total de lignes: {initial_length}\")\n", "    for col in ['summary_detail', 'title', 'summary_detail_with_title']:\n", "        null_count = df[col].isna().sum()\n", "        empty_count = (df[col].astype(str).str.strip() == '').sum()\n", "        print(f\"  {col}: {null_count} nulls, {empty_count} vides\")\n", "    \n", "    # 1. <PERSON><PERSON><PERSON><PERSON> toutes les lignes avec des valeurs nulles dans les colonnes importantes\n", "    print(f\"\\nSuppression des valeurs nulles...\")\n", "    df_no_nulls = df.dropna(subset=['summary_detail', 'title', 'summary_detail_with_title', 'labels'])\n", "    removed_nulls = initial_length - len(df_no_nulls)\n", "    print(f\"  {removed_nulls} lignes supprimées (valeurs nulles)\")\n", "    \n", "    # 2. <PERSON><PERSON><PERSON> les colonnes textuelles\n", "    print(f\"Nettoyage du texte...\")\n", "    df_no_nulls['summary_detail'] = df_no_nulls['summary_detail'].apply(clean_text)\n", "    df_no_nulls['title'] = df_no_nulls['title'].apply(clean_text)\n", "    df_no_nulls['summary_detail_with_title'] = df_no_nulls['summary_detail_with_title'].apply(clean_text)\n", "    \n", "    # 3. <PERSON><PERSON><PERSON><PERSON> les lignes où le texte nettoyé est vide\n", "    print(f\"Suppression des textes vides après nettoyage...\")\n", "    before_empty_removal = len(df_no_nulls)\n", "    \n", "    # Filtrer les lignes avec des textes non vides\n", "    df_clean = df_no_nulls[\n", "        (df_no_nulls['summary_detail'].str.len() > 5) &  # Au moins 5 caractères\n", "        (df_no_nulls['title'].str.len() > 3) &  # Au moins 3 caractères\n", "        (df_no_nulls['summary_detail_with_title'].str.len() > 10)  # Au moins 10 caractères\n", "    ].copy()\n", "    \n", "    removed_empty = before_empty_removal - len(df_clean)\n", "    print(f\"  {removed_empty} lignes supprimées (textes trop courts)\")\n", "    \n", "    # 4. Vérifier les labels valides (0, 1, 2)\n", "    valid_labels = df_clean['labels'].isin([0, 1, 2])\n", "    df_clean = df_clean[valid_labels].copy()\n", "    removed_invalid_labels = len(df_no_nulls) - removed_empty - len(df_clean)\n", "    if removed_invalid_labels > 0:\n", "        print(f\"  {removed_invalid_labels} lignes supprimées (labels invalides)\")\n", "    \n", "    # 5. <PERSON><PERSON><PERSON><PERSON> les doublons basés sur le texte principal\n", "    print(f\"Suppression des doublons...\")\n", "    before_duplicates = len(df_clean)\n", "    df_clean = df_clean.drop_duplicates(subset=['summary_detail_with_title'], keep='first')\n", "    removed_duplicates = before_duplicates - len(df_clean)\n", "    print(f\"  {removed_duplicates} doublons supprimés\")\n", "    \n", "    # 6. État final après nettoyage\n", "    final_length = len(df_clean)\n", "    total_removed = initial_length - final_length\n", "    \n", "    print(f\"\\nÉtat après nettoyage:\")\n", "    print(f\"  Lignes initiales: {initial_length}\")\n", "    print(f\"  Lignes finales: {final_length}\")\n", "    print(f\"  Total supprimé: {total_removed} ({(total_removed/initial_length)*100:.1f}%)\")\n", "    \n", "    # Vérifier qu'il ne reste aucune valeur nulle\n", "    print(f\"\\nVérification finale - valeurs nulles restantes:\")\n", "    for col in ['summary_detail', 'title', 'summary_detail_with_title', 'labels']:\n", "        null_count = df_clean[col].isna().sum()\n", "        print(f\"  {col}: {null_count} nulls\")\n", "    \n", "    # Vérifier la distribution des labels après nettoyage\n", "    label_dist = df_clean['labels'].value_counts().sort_index()\n", "    print(f\"\\nDistribution des labels après nettoyage:\")\n", "    for label, count in label_dist.items():\n", "        percentage = (count / len(df_clean)) * 100\n", "        sentiment_name = ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>ti<PERSON>'][label]\n", "        print(f\"  {sentiment_name} (label {label}): {count} ({percentage:.1f}%)\")\n", "    \n", "    # Reconvertir en dataset\n", "    # Supprimer les colonnes d'index en double avant la conversion\n", "    if '__index_level_0__' in df_clean.columns:\n", "        df_clean = df_clean.drop(columns=['__index_level_0__'])\n", "    \n", "    # Reset index pour éviter les problèmes\n", "    df_clean = df_clean.reset_index(drop=True)\n", "    \n", "    cleaned_dataset = Dataset.from_pandas(df_clean)\n", "    \n", "    print(f\"{split_name} entièrement nettoyé: {len(cleaned_dataset)} exemples de qualité\")\n", "    return cleaned_dataset\n", "\n", "# Nettoyer les deux splits\n", "train_dataset_clean = clean_dataset(train_dataset, \"Train\")\n", "test_dataset_clean = clean_dataset(test_dataset, \"Test\")\n", "\n", "print(f\"\\nRésumé après nettoyage:\")\n", "print(f\"  - Train: {len(train_dataset_clean)} exemples\")\n", "print(f\"  - Test: {len(test_dataset_clean)} exemples\")\n", "print(f\"  - Total: {len(train_dataset_clean) + len(test_dataset_clean)} exemples\")"]}, {"cell_type": "code", "execution_count": 12, "id": "5371036b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== ÉTAPE 4: TOKENISATION ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Token indices sequence length is longer than the specified maximum sequence length for this model (597 > 512). Running this sequence through the model will result in indexing errors\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Tokenizer FinBERT chargé: BertTokenizerFast\n", "\n", "Analyse des longueurs en tokens pour Train:\n", "  Échantillon: 1500 textes\n", "  Longueur min: 15 tokens\n", "  Longueur max: 597 tokens\n", "  <PERSON><PERSON><PERSON> moyenne: 104.3 tokens\n", "  Longueur médiane: 97.0 tokens\n", "  95e percentile: 208.1 tokens\n", "  99e percentile: 284.0 tokens\n", "  Textes tronqués avec max_length=512: 1 (0.1%)\n", "  Textes tronqués avec max_length=256: 26 (1.7%)\n", "\n", "Analyse des longueurs en tokens pour Test:\n", "  Échantillon: 266 textes\n", "  Longueur min: 19 tokens\n", "  Longueur max: 313 tokens\n", "  <PERSON><PERSON>ur moyenne: 106.3 tokens\n", "  Longueur médiane: 102.0 tokens\n", "  95e percentile: 203.8 tokens\n", "  99e percentile: 264.1 tokens\n", "  Textes tronqués avec max_length=512: 0 (0.0%)\n", "  Textes tronqués avec max_length=256: 4 (1.5%)\n", "\n", "Longueur optimale recommandée: 283 tokens (99e percentile)\n", "Nous utiliserons max_length=512 pour être conservatifs.\n", "  Échantillon: 1500 textes\n", "  Longueur min: 15 tokens\n", "  Longueur max: 597 tokens\n", "  <PERSON><PERSON><PERSON> moyenne: 104.3 tokens\n", "  Longueur médiane: 97.0 tokens\n", "  95e percentile: 208.1 tokens\n", "  99e percentile: 284.0 tokens\n", "  Textes tronqués avec max_length=512: 1 (0.1%)\n", "  Textes tronqués avec max_length=256: 26 (1.7%)\n", "\n", "Analyse des longueurs en tokens pour Test:\n", "  Échantillon: 266 textes\n", "  Longueur min: 19 tokens\n", "  Longueur max: 313 tokens\n", "  <PERSON><PERSON>ur moyenne: 106.3 tokens\n", "  Longueur médiane: 102.0 tokens\n", "  95e percentile: 203.8 tokens\n", "  99e percentile: 264.1 tokens\n", "  Textes tronqués avec max_length=512: 0 (0.0%)\n", "  Textes tronqués avec max_length=256: 4 (1.5%)\n", "\n", "Longueur optimale recommandée: 283 tokens (99e percentile)\n", "Nous utiliserons max_length=512 pour être conservatifs.\n"]}], "source": ["print(\"\\n=== ÉTAPE 4: TOKENISATION ===\")\n", "\n", "# Charger le tokenizer FinBERT\n", "tokenizer = AutoTokenizer.from_pretrained(\"ProsusAI/finbert\")\n", "print(f\"Tokenizer FinBERT chargé: {tokenizer.__class__.__name__}\")\n", "\n", "# Analyser les longueurs en tokens avant tokenisation\n", "def analyze_token_lengths(dataset_split, split_name, sample_size=1000):\n", "    \"\"\"Analyser les longueurs en tokens pour déterminer max_length optimal\"\"\"\n", "    print(f\"\\nAnalyse des longueurs en tokens pour {split_name}:\")\n", "    \n", "    # Prendre un échantillon pour l'analyse\n", "    sample_size = min(sample_size, len(dataset_split))\n", "    sample_indices = np.random.choice(len(dataset_split), sample_size, replace=False)\n", "    sample_texts = [dataset_split[int(i)]['summary_detail_with_title'] for i in sample_indices]\n", "    \n", "    # Token<PERSON> sans padding pour mesurer les longueurs réelles\n", "    token_lengths = []\n", "    for text in sample_texts:\n", "        tokens = tokenizer(text, truncation=False, return_attention_mask=False)\n", "        token_lengths.append(len(tokens['input_ids']))\n", "    \n", "    token_lengths = np.array(token_lengths)\n", "    \n", "    print(f\"  Échantillon: {len(token_lengths)} textes\")\n", "    print(f\"  Longueur min: {token_lengths.min()} tokens\")\n", "    print(f\"  Longueur max: {token_lengths.max()} tokens\")\n", "    print(f\"  <PERSON><PERSON>ur moyenne: {token_lengths.mean():.1f} tokens\")\n", "    print(f\"  Longueur médiane: {np.median(token_lengths):.1f} tokens\")\n", "    print(f\"  95e percentile: {np.percentile(token_lengths, 95):.1f} tokens\")\n", "    print(f\"  99e percentile: {np.percentile(token_lengths, 99):.1f} tokens\")\n", "    \n", "    # Calculer le pourcentage de textes qui seront tronqués\n", "    truncated_512 = (token_lengths > 512).sum()\n", "    truncated_256 = (token_lengths > 256).sum()\n", "    print(f\"  Textes tronqués avec max_length=512: {truncated_512} ({truncated_512/len(token_lengths)*100:.1f}%)\")\n", "    print(f\"  Textes tronqués avec max_length=256: {truncated_256} ({truncated_256/len(token_lengths)*100:.1f}%)\")\n", "    \n", "    return token_lengths\n", "\n", "# Analyser les longueurs pour les deux splits\n", "train_token_lengths = analyze_token_lengths(train_dataset_clean, \"Train\", sample_size=1500)\n", "test_token_lengths = analyze_token_lengths(test_dataset_clean, \"Test\", sample_size=500)\n", "\n", "# Déterminer la longueur optimale\n", "all_lengths = np.concatenate([train_token_lengths, test_token_lengths])\n", "optimal_length = int(np.percentile(all_lengths, 99))\n", "print(f\"\\nLongueur optimale recommandée: {optimal_length} tokens (99e percentile)\")\n", "print(f\"Nous utiliserons max_length=512 pour être conservatifs.\")"]}, {"cell_type": "code", "execution_count": 13, "id": "26787f26", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Tokenisation en cours...\n", "Tokenisation du dataset d'entraînement...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Tokenizing train dataset:   0%|          | 0/1508 [00:00<?, ? examples/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["Tokenizing train dataset: 100%|██████████| 1508/1508 [00:00<00:00, 3509.83 examples/s]\n", "Tokenizing train dataset: 100%|██████████| 1508/1508 [00:00<00:00, 3509.83 examples/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Tokenisation du dataset de test...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Tokenizing test dataset: 100%|██████████| 266/266 [00:00<00:00, 3818.65 examples/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Tokenisation terminée pour les deux splits\n", "\n", "Structure des données tokenisées:\n", "Train - Colonnes: ['summary_detail', 'title', 'summary_detail_with_title', 'topic', 'labels', 'score_topic', 'input_ids', 'token_type_ids', 'attention_mask']\n", "Test - Colonnes: ['summary_detail', 'title', 'summary_detail_with_title', 'topic', 'labels', 'score_topic', 'input_ids', 'token_type_ids', 'attention_mask']\n", "Exemple train - Token IDs (5 premiers): [101, 7160, 1023, 24255, 2785]\n", "Longueur des input_ids: 512\n", "Longueur des attention_mask: 512\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# Fonction de tokenisation\n", "def tokenize_function(examples):\n", "    \"\"\"Fonction de tokenisation avec padding et truncation\"\"\"\n", "    return tokenizer(\n", "        examples['summary_detail_with_title'], \n", "        padding=\"max_length\", \n", "        truncation=True, \n", "        max_length=512,\n", "        return_tensors=None  # Sera converti en torch plus tard\n", "    )\n", "\n", "print(f\"\\nTokenisation en cours...\")\n", "\n", "# Tokeniser les deux splits\n", "print(\"Tokenisation du dataset d'entraînement...\")\n", "tokenized_train = train_dataset_clean.map(\n", "    tokenize_function, \n", "    batched=True, \n", "    desc=\"Tokenizing train dataset\"\n", ")\n", "\n", "print(\"Tokenisation du dataset de test...\")\n", "tokenized_test = test_dataset_clean.map(\n", "    tokenize_function, \n", "    batched=True,\n", "    desc=\"Tokenizing test dataset\"\n", ")\n", "\n", "print(\"Tokenisation terminée pour les deux splits\")\n", "\n", "# Vérifier la structure des données tokenisées\n", "print(f\"\\nStructure des données tokenisées:\")\n", "print(f\"Train - Colonnes: {tokenized_train.column_names}\")\n", "print(f\"Test - Colonnes: {tokenized_test.column_names}\")\n", "print(f\"Exemple train - Token IDs (5 premiers): {tokenized_train[0]['input_ids'][:5]}\")\n", "print(f\"Longueur des input_ids: {len(tokenized_train[0]['input_ids'])}\")\n", "print(f\"Longueur des attention_mask: {len(tokenized_train[0]['attention_mask'])}\")"]}, {"cell_type": "code", "execution_count": 14, "id": "854640b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== ÉTAPE 5: FORMATAGE POUR PYTORCH ===\n", "Configuration du format PyTorch...\n", "Toutes les colonnes nécessaires présentes dans train\n", "Toutes les colonnes nécessaires présentes dans test\n", "\n", "Préparation de Train pour PyTorch...\n", "  Train formaté: 1508 exemples\n", "  Colonnes: ['labels', 'input_ids', 'attention_mask']\n", "  Format: {'type': 'torch', 'format_kwargs': {}, 'columns': ['input_ids', 'attention_mask', 'labels'], 'output_all_columns': False}\n", "\n", "Préparation de Test pour PyTorch...\n", "  Test formaté: 266 exemples\n", "  Colonnes: ['labels', 'input_ids', 'attention_mask']\n", "  Format: {'type': 'torch', 'format_kwargs': {}, 'columns': ['input_ids', 'attention_mask', 'labels'], 'output_all_columns': False}\n", "\n", "Vérification d'un exemple du dataset d'entraînement:\n", "Type de input_ids: <class 'torch.Tensor'>\n", "Shape de input_ids: torch.Size([512])\n", "Type de attention_mask: <class 'torch.Tensor'>\n", "Shape de attention_mask: torch.Size([512])\n", "Type de labels: <class 'torch.Tensor'>\n", "Valeur de labels: 2\n", "\n", "Vérification d'un exemple du dataset de test:\n", "Type de input_ids: <class 'torch.Tensor'>\n", "Shape de input_ids: torch.Size([512])\n", "Type de labels: <class 'torch.Tensor'>\n", "Valeur de labels: 1\n", "\n", "FORMATAGE PYTORCH TERMINÉ AVEC SUCCÈS!\n", "Datasets finaux - Splits originaux:\n", "  - Train: 1508 exemples\n", "  - Test: 266 exemples\n", "  - Tokenizer: FinBERT (ProsusAI/finbert)\n", "  - Max length: 512 tokens\n", "  - Format: PyTorch tensors\n"]}], "source": ["print(\"\\n=== ÉTAPE 5: FORMATAGE POUR PYTORCH ===\")\n", "\n", "# Configurer le format PyTorch pour tous les datasets\n", "columns_to_keep = [\"input_ids\", \"attention_mask\", \"labels\"]\n", "\n", "print(\"Configuration du format PyTorch...\")\n", "\n", "# Vérifier que toutes les colonnes nécessaires sont présentes\n", "for dataset_name, dataset_split in [(\"train\", tokenized_train), (\"test\", tokenized_test)]:\n", "    missing_cols = [col for col in columns_to_keep if col not in dataset_split.column_names]\n", "    if missing_cols:\n", "        print(f\"Colonnes manquantes dans {dataset_name}: {missing_cols}\")\n", "    else:\n", "        print(f\"Toutes les colonnes nécessaires présentes dans {dataset_name}\")\n", "\n", "# Supprimer les colonnes non nécessaires et définir le format\n", "def prepare_for_pytorch(dataset_split, split_name):\n", "    \"\"\"Préparer un dataset pour PyTorch\"\"\"\n", "    print(f\"\\nPréparation de {split_name} pour PyTorch...\")\n", "    \n", "    # Garder seulement les colonnes nécessaires\n", "    dataset_split = dataset_split.remove_columns([\n", "        col for col in dataset_split.column_names \n", "        if col not in columns_to_keep\n", "    ])\n", "    \n", "    # Définir le format PyTorch\n", "    dataset_split.set_format(\"torch\", columns=columns_to_keep)\n", "    \n", "    print(f\"  {split_name} formaté: {len(dataset_split)} exemples\")\n", "    print(f\"  Colonnes: {dataset_split.column_names}\")\n", "    print(f\"  Format: {dataset_split.format}\")\n", "    \n", "    return dataset_split\n", "\n", "# Préparer les deux datasets\n", "final_train = prepare_for_pytorch(tokenized_train, \"Train\")\n", "final_test = prepare_for_pytorch(tokenized_test, \"Test\")\n", "\n", "# Vérifier un exemple pour s'assurer que tout fonctionne\n", "print(f\"\\nVérification d'un exemple du dataset d'entraînement:\")\n", "example = final_train[0]\n", "print(f\"Type de input_ids: {type(example['input_ids'])}\")\n", "print(f\"Shape de input_ids: {example['input_ids'].shape}\")\n", "print(f\"Type de attention_mask: {type(example['attention_mask'])}\")\n", "print(f\"Shape de attention_mask: {example['attention_mask'].shape}\")\n", "print(f\"Type de labels: {type(example['labels'])}\")\n", "print(f\"Valeur de labels: {example['labels']}\")\n", "\n", "print(f\"\\nVérification d'un exemple du dataset de test:\")\n", "test_example = final_test[0]\n", "print(f\"Type de input_ids: {type(test_example['input_ids'])}\")\n", "print(f\"Shape de input_ids: {test_example['input_ids'].shape}\")\n", "print(f\"Type de labels: {type(test_example['labels'])}\")\n", "print(f\"Valeur de labels: {test_example['labels']}\")\n", "\n", "print(f\"\\nFORMATAGE PYTORCH TERMINÉ AVEC SUCCÈS!\")\n", "print(f\"Datasets finaux - Splits originaux:\")\n", "print(f\"  - Train: {len(final_train)} exemples\")\n", "print(f\"  - Test: {len(final_test)} exemples\")\n", "print(f\"  - Tokenizer: FinBERT (ProsusAI/finbert)\")\n", "print(f\"  - Max length: 512 tokens\")\n", "print(f\"  - Format: PyTorch tensors\")"]}, {"cell_type": "code", "execution_count": 15, "id": "77ed8610", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== ÉTAPE 6: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> DES DATASETS PRÉPROCESSÉS ===\n", "Sauvegarde des datasets dans le dossier 'preprocessed_datasets_original_splits'...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Saving the dataset (0/1 shards):   0%|          | 0/1508 [00:00<?, ? examples/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Erreur lors de la sauvegarde: [Errno 22] Invalid argument: 'c:/Users/<USER>/Desktop/Xcapitale/preprocessed_datasets_original_splits/train/data-00000-of-00001.arrow'\n", "\n", "PREPROCESSING COMPLET - SPLITS ORIGINAUX!\n", "Les datasets sont prêts pour l'entraînement avec la configuration originale train/test.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["print(\"\\n=== ÉTAPE 6: SAUVEGARDE DES DATASETS PRÉPROCESSÉS ===\")\n", "\n", "# Sauvegarder les datasets préprocessés pour utilisation ultérieure\n", "save_dir = \"preprocessed_datasets_original_splits\"\n", "os.makedirs(save_dir, exist_ok=True)\n", "\n", "print(f\"Sauvegarde des datasets dans le dossier '{save_dir}'...\")\n", "\n", "try:\n", "    # Sauvegarder les deux datasets\n", "    final_train.save_to_disk(os.path.join(save_dir, \"train\"))\n", "    final_test.save_to_disk(os.path.join(save_dir, \"test\"))\n", "    \n", "    # <PERSON><PERSON><PERSON>er aussi le tokenizer\n", "    tokenizer.save_pretrained(os.path.join(save_dir, \"tokenizer\"))\n", "    \n", "    print(\"Sauvegarde terminée avec succès!\")\n", "    print(f\"Fichiers sauvegardés:\")\n", "    print(f\"  - {save_dir}/train/\")\n", "    print(f\"  - {save_dir}/test/\")\n", "    print(f\"  - {save_dir}/tokenizer/\")\n", "    \n", "    # <PERSON><PERSON>er un fichier de métadonnées\n", "    metadata = {\n", "        \"dataset_name\": dataset_name,\n", "        \"tokenizer_name\": \"ProsusAI/finbert\",\n", "        \"max_length\": 512,\n", "        \"train_size\": len(final_train),\n", "        \"test_size\": len(final_test),\n", "        \"splits_used\": \"original (train/test only)\",\n", "        \"validation_split\": \"none - using original splits\",\n", "        \"preprocessing_date\": pd.Timestamp.now().strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "        \"notes\": \"Preprocessed using original Hugging Face splits without additional validation split\"\n", "    }\n", "    \n", "    with open(os.path.join(save_dir, \"metadata.json\"), \"w\") as f:\n", "        json.dump(metadata, f, indent=2)\n", "    \n", "    print(f\"  - {save_dir}/metadata.json\")\n", "    \n", "    # Affiche<PERSON> le contenu des métadonnées\n", "    print(f\"Métadonnées sauvegardées:\")\n", "    for key, value in metadata.items():\n", "        print(f\"  {key}: {value}\")\n", "    \n", "except Exception as e:\n", "    print(f\"Erreur lors de la sauvegarde: {e}\")\n", "\n", "print(f\"\\nPREPROCESSING COMPLET - SPLITS ORIGINAUX!\")\n", "print(f\"Les datasets sont prêts pour l'entraînement avec la configuration originale train/test.\")"]}, {"cell_type": "code", "execution_count": 16, "id": "58388fc2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== ÉTAPE 7: VISUALISATION DES DONNÉES PRÉPROCESSÉES ===\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Analyse des longueurs de tokens après preprocessing:\n", "\n", "Train:\n", "  Longueur min: 15 tokens\n", "  Longueur max: 387 tokens\n", "  <PERSON><PERSON><PERSON> moyenne: 104.0 tokens\n", "  Longueur médiane: 96.0 tokens\n", "\n", "Test:\n", "  Longueur min: 19 tokens\n", "  Longueur max: 313 tokens\n", "  <PERSON><PERSON>ur moyenne: 106.3 tokens\n", "  Longueur médiane: 102.0 tokens\n", "\n", "Train:\n", "  Longueur min: 15 tokens\n", "  Longueur max: 387 tokens\n", "  <PERSON><PERSON><PERSON> moyenne: 104.0 tokens\n", "  Longueur médiane: 96.0 tokens\n", "\n", "Test:\n", "  Longueur min: 19 tokens\n", "  Longueur max: 313 tokens\n", "  <PERSON><PERSON>ur moyenne: 106.3 tokens\n", "  Longueur médiane: 102.0 tokens\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27892\\418326094.py:81: MatplotlibDeprecationWarning: The 'labels' parameter of boxplot() has been renamed 'tick_labels' since Matplotlib 3.9; support for the old name will be dropped in 3.11.\n", "  axes[2].boxplot([all_lengths[name] for name, _ in splits],\n"]}, {"data": {"image/png": "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*********************************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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Exemples de données préprocessées:\n", "\n", "--- Exemple 1 du split Train ---\n", "Label: 2 (Positif)\n", "Texte décodé: delta 9 engages kindred for cannabis wholesale brokerage services - - delta 9 cannabis inc. ( tsx : dn ) ( otcqx : dltnf ) ( \" delta 9 \" or the \" company \" ), is pleased to announce that, through its ...\n", "Longueur réelle: 116 tokens\n", "Premiers tokens: [101, 7160, 1023, 24255, 2785, 5596, 2005, 17985, 17264, 20138]\n", "Attention mask (10 premiers): [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]\n", "\n", "--- Exemple 1 du split Test ---\n", "Label: 1 (Neutre)\n", "Texte décodé: pyrogenesis announces closing of a small non - brokered private placement - - pyrogenesis canada inc. ( http : / / pyrogenesis. com ) ( tsx : pyr ) ( nasdaq : pyr ) ( fra : 8py ), a high - tech compan...\n", "Longueur réelle: 218 tokens\n", "Premiers tokens: [101, 1052, 12541, 23924, 19009, 17472, 5494, 1997, 1037, 2235]\n", "Attention mask (10 premiers): [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]\n", "\n", "RÉSUMÉ COMPLET DES DONNÉES PRÉPROCESSÉES:\n", "============================================================\n", "Nombre total d'exemples: 1774\n", "  - Train: 1508 exemples (85.0%)\n", "  - Test: 266 exemples (15.0%)\n", "\n", "Configuration du preprocessing:\n", "  - Tokenizer: ProsusAI/finbert\n", "  - Longueur maximale: 512 tokens\n", "  - Padding: max_length\n", "  - Truncation: True\n", "\n", "Données sauvegardées dans: preprocessed_datasets_original_splits/\n", "  - Formats: PyTorch tensors\n", "  - Colonnes: ['labels', 'input_ids', 'attention_mask']\n", "\n", "VÉRIFICATION DE LA QUALITÉ:\n", "Train: <PERSON><PERSON>n problème détec<PERSON>\n", "Test: <PERSON><PERSON><PERSON> problème détec<PERSON>\n", "\n", "VISUALISATION ET ANALYSE TERMINÉES!\n", "\n", "=== ÉTAPE 8: EXPORT DES DONNÉES VERS CSV ===\n", "\n", "Export de Train vers CSV...\n", "Train exporté: exported_csv_data_original_splits\\train_processed.csv\n", "   - 1508 lignes\n", "   - Colonnes: ['index', 'text', 'label', 'sentiment', 'token_length', 'input_ids', 'attention_mask']\n", "\n", "Export de Test vers CSV...\n", "Test exporté: exported_csv_data_original_splits\\test_processed.csv\n", "   - 266 lignes\n", "   - Colonnes: ['index', 'text', 'label', 'sentiment', 'token_length', 'input_ids', 'attention_mask']\n", "\n", "Création du fichier de résumé...\n", "Résumé exporté: exported_csv_data_original_splits\\summary_statistics.csv\n", "\n", "Aperçu du résumé:\n", "split  label sentiment  count  percentage  avg_token_length  total_examples\n", "Train      0   Négatif     49        3.25             133.3            1508\n", "Train      1    Neutre    898       59.55              94.9            1508\n", "Train      2   Positif    561       37.20             117.0            1508\n", " Test      0   Négatif     12        4.51             128.1             266\n", " Test      1    Neutre    155       58.27              96.3             266\n", " Test      2   Positif     99       37.22             119.3             266\n", "\n", "Fichiers CSV exportés dans: exported_csv_data_original_splits/\n", "Fichiers créés:\n", "  - train_processed.csv\n", "  - test_processed.csv\n", "  - summary_statistics.csv\n", "  - export_metadata.json\n", "\n", "EXPORT CSV TERMINÉ!\n", "Les données des splits originaux sont maintenant disponibles en format CSV.\n", "Train exporté: exported_csv_data_original_splits\\train_processed.csv\n", "   - 1508 lignes\n", "   - Colonnes: ['index', 'text', 'label', 'sentiment', 'token_length', 'input_ids', 'attention_mask']\n", "\n", "Export de Test vers CSV...\n", "Test exporté: exported_csv_data_original_splits\\test_processed.csv\n", "   - 266 lignes\n", "   - Colonnes: ['index', 'text', 'label', 'sentiment', 'token_length', 'input_ids', 'attention_mask']\n", "\n", "Création du fichier de résumé...\n", "Résumé exporté: exported_csv_data_original_splits\\summary_statistics.csv\n", "\n", "Aperçu du résumé:\n", "split  label sentiment  count  percentage  avg_token_length  total_examples\n", "Train      0   Négatif     49        3.25             133.3            1508\n", "Train      1    Neutre    898       59.55              94.9            1508\n", "Train      2   Positif    561       37.20             117.0            1508\n", " Test      0   Négatif     12        4.51             128.1             266\n", " Test      1    Neutre    155       58.27              96.3             266\n", " Test      2   Positif     99       37.22             119.3             266\n", "\n", "Fichiers CSV exportés dans: exported_csv_data_original_splits/\n", "Fichiers créés:\n", "  - train_processed.csv\n", "  - test_processed.csv\n", "  - summary_statistics.csv\n", "  - export_metadata.json\n", "\n", "EXPORT CSV TERMINÉ!\n", "Les données des splits originaux sont maintenant disponibles en format CSV.\n"]}], "source": ["print(\"\\n=== ÉTAPE 7: VISUALISATION DES DONNÉES PRÉPROCESSÉES ===\")\n", "\n", "# Imports pour la visualisation\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from collections import Counter\n", "\n", "# Configuration du style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "# 1. Distribution des labels dans les splits originaux\n", "fig, axes = plt.subplots(1, 2, figsize=(12, 5))\n", "splits = [('Train', final_train), ('Test', final_test)]\n", "\n", "for idx, (split_name, dataset_split) in enumerate(splits):\n", "    labels = [dataset_split[i]['labels'].item() for i in range(len(dataset_split))]\n", "    label_counts = Counter(labels)\n", "    \n", "    # <PERSON><PERSON><PERSON> le graphique\n", "    sentiment_names = ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>']\n", "    colors = ['#D62728', '#FFBF00', '#2CA02C']\n", "    \n", "    bars = axes[idx].bar(sentiment_names, [label_counts[i] for i in range(3)], color=colors)\n", "    axes[idx].set_title(f'Distribution - {split_name}')\n", "    axes[idx].set_ylabel('Nombre d\\'exemples')\n", "    \n", "    # Ajouter les pourcentages\n", "    total = len(dataset_split)\n", "    for i, bar in enumerate(bars):\n", "        height = bar.get_height()\n", "        percentage = (height / total) * 100\n", "        axes[idx].text(bar.get_x() + bar.get_width()/2., height + total*0.01,\n", "                      f'{height}\\n({percentage:.1f}%)', \n", "                      ha='center', va='bottom', fontsize=10)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 2. <PERSON><PERSON><PERSON> des longueurs de tokens\n", "print(f\"\\nAnalyse des longueurs de tokens après preprocessing:\")\n", "\n", "def analyze_token_lengths_final(dataset_split, split_name, max_samples=1000):\n", "    \"\"\"Analyser les longueurs réelles des tokens dans les données préprocessées\"\"\"\n", "    sample_size = min(max_samples, len(dataset_split))\n", "    token_lengths = []\n", "    \n", "    for i in range(sample_size):\n", "        input_ids = dataset_split[i]['input_ids']\n", "        # Compter les tokens non-padding (différents de 0)\n", "        actual_length = (input_ids != 0).sum().item()\n", "        token_lengths.append(actual_length)\n", "    \n", "    return token_lengths\n", "\n", "# Analyser chaque split\n", "fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "\n", "all_lengths = {}\n", "for split_name, dataset_split in splits:\n", "    lengths = analyze_token_lengths_final(dataset_split, split_name)\n", "    all_lengths[split_name] = lengths\n", "    \n", "    print(f\"\\n{split_name}:\")\n", "    print(f\"  Longueur min: {min(lengths)} tokens\")\n", "    print(f\"  Longueur max: {max(lengths)} tokens\")\n", "    print(f\"  Longueur moyenne: {np.mean(lengths):.1f} tokens\")\n", "    print(f\"  Longueur médiane: {np.median(lengths):.1f} tokens\")\n", "\n", "# Histogrammes des longueurs\n", "for idx, (split_name, lengths) in enumerate(all_lengths.items()):\n", "    axes[idx].hist(lengths, bins=30, alpha=0.7, edgecolor='black')\n", "    axes[idx].set_title(f'Distribution des longueurs - {split_name}')\n", "    axes[idx].set_xlabel('Longueur en tokens')\n", "    axes[idx].set_ylabel('Fréquence')\n", "    axes[idx].axvline(np.mean(lengths), color='red', linestyle='--', \n", "                      label=f'Moyenne: {np.mean(lengths):.1f}')\n", "    axes[idx].legend()\n", "\n", "# Boxplot comparatif\n", "axes[2].boxplot([all_lengths[name] for name, _ in splits], \n", "                labels=[name for name, _ in splits])\n", "axes[2].set_title('Comparaison des longueurs entre splits')\n", "axes[2].set_ylabel('Longueur en tokens')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 3. Exemples de données préprocessées\n", "print(f\"\\nExemples de données préprocessées:\")\n", "\n", "def show_example(dataset_split, split_name, idx=0):\n", "    \"\"\"Afficher un exemple préprocessé avec décodage\"\"\"\n", "    example = dataset_split[idx]\n", "    \n", "    print(f\"\\n--- Exemple {idx+1} du split {split_name} ---\")\n", "    label_value = example['labels'].item()\n", "    sentiment_name = ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Positif'][label_value]\n", "    print(f\"Label: {label_value} ({sentiment_name})\")\n", "    \n", "    # Décoder les tokens\n", "    input_ids = example['input_ids']\n", "    attention_mask = example['attention_mask']\n", "    \n", "    # Trouver les tokens non-padding\n", "    actual_tokens = input_ids[attention_mask.bool()]\n", "    decoded_text = tokenizer.decode(actual_tokens, skip_special_tokens=True)\n", "    \n", "    print(f\"Texte décodé: {decoded_text[:200]}...\")\n", "    print(f\"Longueur réelle: {len(actual_tokens)} tokens\")\n", "    print(f\"Premiers tokens: {input_ids[:10].tolist()}\")\n", "    print(f\"Attention mask (10 premiers): {attention_mask[:10].tolist()}\")\n", "\n", "# Afficher des exemples de chaque split\n", "for split_name, dataset_split in splits:\n", "    show_example(dataset_split, split_name, idx=0)\n", "\n", "# 4. Statistiques détaillées\n", "print(f\"\\nR<PERSON><PERSON><PERSON>É COMPLET DES DONNÉES PRÉPROCESSÉES:\")\n", "print(f\"=\" * 60)\n", "\n", "total_examples = sum(len(ds) for _, ds in splits)\n", "print(f\"Nombre total d'exemples: {total_examples}\")\n", "\n", "for split_name, dataset_split in splits:\n", "    percentage = (len(dataset_split) / total_examples) * 100\n", "    print(f\"  - {split_name}: {len(dataset_split)} exemples ({percentage:.1f}%)\")\n", "\n", "print(f\"\\nConfiguration du preprocessing:\")\n", "print(f\"  - Tokenizer: {tokenizer.name_or_path}\")\n", "print(f\"  - <PERSON><PERSON>ur maximale: 512 tokens\")\n", "print(f\"  - Padding: max_length\")\n", "print(f\"  - Truncation: True\")\n", "\n", "print(f\"\\nDonnées sauvegardées dans: {save_dir}/\")\n", "print(f\"  - Formats: PyTorch tensors\")\n", "print(f\"  - <PERSON>onnes: {final_train.column_names}\")\n", "\n", "# 5. Vérification de la qualité des données\n", "print(f\"\\nVÉRIFICATION DE LA QUALITÉ:\")\n", "\n", "def quality_check(dataset_split, split_name):\n", "    \"\"\"Vérifier la qualité des données préprocessées\"\"\"\n", "    issues = []\n", "    \n", "    # Vérifier quelques exemples\n", "    sample_size = min(100, len(dataset_split))\n", "    \n", "    for i in range(sample_size):\n", "        example = dataset_split[i]\n", "        \n", "        # Vérifier les dimensions\n", "        if len(example['input_ids']) != 512:\n", "            issues.append(f\"Longueur input_ids incorrecte: {len(example['input_ids'])}\")\n", "        \n", "        if len(example['attention_mask']) != 512:\n", "            issues.append(f\"Longueur attention_mask incorrecte: {len(example['attention_mask'])}\")\n", "        \n", "        # Vérifier les labels\n", "        label = example['labels'].item()\n", "        if label not in [0, 1, 2]:\n", "            issues.append(f\"Label invalide: {label}\")\n", "        \n", "        # Vérifier que les tokens padding sont cohérents\n", "        input_ids = example['input_ids']\n", "        attention_mask = example['attention_mask']\n", "        \n", "        # Les positions avec attention_mask=0 devraient avoir input_ids=0 (PAD)\n", "        padding_positions = (attention_mask == 0)\n", "        if (input_ids[padding_positions] != 0).any():\n", "            issues.append(\"Incohérence entre padding et attention_mask\")\n", "    \n", "    if issues:\n", "        print(f\"{split_name}: {len(issues)} problèmes détectés\")\n", "        for issue in issues[:5]:  # <PERSON><PERSON><PERSON><PERSON> les 5 premiers\n", "            print(f\"  - {issue}\")\n", "    else:\n", "        print(f\"{split_name}: Aucun problème détecté\")\n", "\n", "for split_name, dataset_split in splits:\n", "    quality_check(dataset_split, split_name)\n", "\n", "print(f\"\\nVISUALISATION ET ANALYSE TERMINÉES!\")\n", "\n", "print(\"\\n=== ÉTAPE 8: EXPORT DES DONNÉES VERS CSV ===\")\n", "\n", "# C<PERSON>er un dossier pour les exports CSV\n", "csv_export_dir = \"exported_csv_data_original_splits\"\n", "os.makedirs(csv_export_dir, exist_ok=True)\n", "\n", "def export_to_csv(dataset_split, split_name, tokenizer):\n", "    \"\"\"Exporter un dataset vers CSV avec texte décodé\"\"\"\n", "    print(f\"\\nExport de {split_name} vers CSV...\")\n", "    \n", "    # Préparer les données pour l'export\n", "    export_data = []\n", "    \n", "    for i in range(len(dataset_split)):\n", "        example = dataset_split[i]\n", "        \n", "        # Dé<PERSON>r le texte\n", "        input_ids = example['input_ids']\n", "        attention_mask = example['attention_mask']\n", "        actual_tokens = input_ids[attention_mask.bool()]\n", "        decoded_text = tokenizer.decode(actual_tokens, skip_special_tokens=True)\n", "        \n", "        # P<PERSON><PERSON>er la ligne de données\n", "        row = {\n", "            'index': i,\n", "            'text': decoded_text,\n", "            'label': example['labels'].item(),\n", "            'sentiment': ['<PERSON><PERSON>gat<PERSON>', '<PERSON>eu<PERSON>', '<PERSON><PERSON>ti<PERSON>'][example['labels'].item()],\n", "            'token_length': len(actual_tokens),\n", "            'input_ids': input_ids.tolist(),\n", "            'attention_mask': attention_mask.tolist()\n", "        }\n", "        export_data.append(row)\n", "    \n", "    # <PERSON><PERSON><PERSON> le DataFrame\n", "    df_export = pd.DataFrame(export_data)\n", "    \n", "    # Sauvegarder en CSV\n", "    csv_path = os.path.join(csv_export_dir, f\"{split_name.lower()}_processed.csv\")\n", "    df_export.to_csv(csv_path, index=False, encoding='utf-8')\n", "    \n", "    print(f\"{split_name} exporté: {csv_path}\")\n", "    print(f\"   - {len(df_export)} lignes\")\n", "    print(f\"   - Colonnes: {list(df_export.columns)}\")\n", "    \n", "    return df_export\n", "\n", "# Exporter tous les splits\n", "exported_dfs = {}\n", "for split_name, dataset_split in splits:\n", "    exported_dfs[split_name] = export_to_csv(dataset_split, split_name, tokenizer)\n", "\n", "# Créer un résumé global\n", "print(f\"\\nCréation du fichier de résumé...\")\n", "summary_data = []\n", "\n", "for split_name, df in exported_dfs.items():\n", "    # Statistiques par split\n", "    label_counts = df['label'].value_counts().sort_index()\n", "    \n", "    for label, count in label_counts.items():\n", "        sentiment = ['<PERSON><PERSON>gat<PERSON>', '<PERSON>eu<PERSON>', 'Positif'][label]\n", "        percentage = (count / len(df)) * 100\n", "        \n", "        summary_data.append({\n", "            'split': split_name,\n", "            'label': label,\n", "            'sentiment': sentiment,\n", "            'count': count,\n", "            'percentage': round(percentage, 2),\n", "            'avg_token_length': round(df[df['label'] == label]['token_length'].mean(), 1),\n", "            'total_examples': len(df)\n", "        })\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> le résumé\n", "summary_df = pd.DataFrame(summary_data)\n", "summary_path = os.path.join(csv_export_dir, \"summary_statistics.csv\")\n", "summary_df.to_csv(summary_path, index=False)\n", "\n", "print(f\"Résumé exporté: {summary_path}\")\n", "\n", "# A<PERSON><PERSON><PERSON> le résumé\n", "print(f\"\\nAperçu du résumé:\")\n", "print(summary_df.to_string(index=False))\n", "\n", "# C<PERSON>er un fichier de métadonnées pour l'export\n", "export_metadata = {\n", "    \"export_date\": pd.Timestamp.now().strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "    \"tokenizer_used\": tokenizer.name_or_path,\n", "    \"max_length\": 512,\n", "    \"splits_configuration\": \"original (train/test only)\",\n", "    \"files_exported\": {\n", "        \"train\": f\"train_processed.csv ({len(exported_dfs['Train'])} lignes)\",\n", "        \"test\": f\"test_processed.csv ({len(exported_dfs['Test'])} lignes)\",\n", "        \"summary\": f\"summary_statistics.csv ({len(summary_df)} lignes)\"\n", "    },\n", "    \"columns_description\": {\n", "        \"index\": \"Index de l'exemple dans le dataset\",\n", "        \"text\": \"Texte décodé (sans tokens spéciaux)\",\n", "        \"label\": \"Label numérique (0=Négatif, 1=Neutre, 2=Positif)\",\n", "        \"sentiment\": \"Nom du sentiment\",\n", "        \"token_length\": \"Longueur réelle en tokens (sans padding)\",\n", "        \"input_ids\": \"IDs des tokens (avec padding)\",\n", "        \"attention_mask\": \"Masque d'attention\"\n", "    }\n", "}\n", "\n", "metadata_path = os.path.join(csv_export_dir, \"export_metadata.json\")\n", "with open(metadata_path, \"w\", encoding='utf-8') as f:\n", "    json.dump(export_metadata, f, indent=2, ensure_ascii=False)\n", "\n", "print(f\"\\nFichiers CSV exportés dans: {csv_export_dir}/\")\n", "print(f\"Fichiers créés:\")\n", "for split_name in ['Train', 'Test']:\n", "    filename = f\"{split_name.lower()}_processed.csv\"\n", "    print(f\"  - {filename}\")\n", "print(f\"  - summary_statistics.csv\")\n", "print(f\"  - export_metadata.json\")\n", "\n", "print(f\"\\nEXPORT CSV TERMINÉ!\")\n", "print(f\"Les données des splits originaux sont maintenant disponibles en format CSV.\")"]}, {"cell_type": "code", "execution_count": 17, "id": "c88b0371", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>index</th>\n", "      <th>text</th>\n", "      <th>label</th>\n", "      <th>sentiment</th>\n", "      <th>token_length</th>\n", "      <th>input_ids</th>\n", "      <th>attention_mask</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>delta 9 engages kindred for cannabis wholesale...</td>\n", "      <td>2</td>\n", "      <td>Positif</td>\n", "      <td>116</td>\n", "      <td>[101, 7160, 1023, 24255, 2785, 5596, 2005, 179...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>nevgold intercepts 0. 53 g / t oxide au over 7...</td>\n", "      <td>1</td>\n", "      <td>Neutre</td>\n", "      <td>56</td>\n", "      <td>[101, 11265, 2615, 21270, 19115, 2015, 1014, 1...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>aura minerals completes feasibility study for ...</td>\n", "      <td>1</td>\n", "      <td>Neutre</td>\n", "      <td>186</td>\n", "      <td>[101, 15240, 13246, 28123, 24010, 2817, 2005, ...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>acadian timber corp. reports third quarter res...</td>\n", "      <td>1</td>\n", "      <td>Neutre</td>\n", "      <td>116</td>\n", "      <td>[101, 9353, 25205, 2078, 7227, 13058, 1012, 43...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>reminder - saputo inc. : fiscal 2023 second qu...</td>\n", "      <td>1</td>\n", "      <td>Neutre</td>\n", "      <td>94</td>\n", "      <td>[101, 14764, 1011, 20066, 16161, 4297, 1012, 1...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1503</th>\n", "      <td>1503</td>\n", "      <td>discovery intercepts 124 g / t ageq over 96m a...</td>\n", "      <td>2</td>\n", "      <td>Positif</td>\n", "      <td>158</td>\n", "      <td>[101, 5456, 19115, 2015, 13412, 1043, 1013, 10...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1504</th>\n", "      <td>1504</td>\n", "      <td>isracann biosciences provides bi - weekly defa...</td>\n", "      <td>0</td>\n", "      <td>Négatif</td>\n", "      <td>137</td>\n", "      <td>[101, 2003, 22648, 11639, 16012, 11020, 13684,...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1505</th>\n", "      <td>1505</td>\n", "      <td>inmed pharmaceuticals announces closing of $ 6...</td>\n", "      <td>1</td>\n", "      <td>Neutre</td>\n", "      <td>252</td>\n", "      <td>[101, 1999, 7583, 24797, 17472, 5494, 1997, 10...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1506</th>\n", "      <td>1506</td>\n", "      <td>agf announces estimated annual reinvested capi...</td>\n", "      <td>1</td>\n", "      <td>Neutre</td>\n", "      <td>106</td>\n", "      <td>[101, 12943, 2546, 17472, 4358, 3296, 27788, 6...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1507</th>\n", "      <td>1507</td>\n", "      <td>teledyne dalsa announces its new line of high ...</td>\n", "      <td>2</td>\n", "      <td>Positif</td>\n", "      <td>72</td>\n", "      <td>[101, 10093, 2098, 9654, 17488, 3736, 17472, 2...</td>\n", "      <td>[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1508 rows × 7 columns</p>\n", "</div>"], "text/plain": ["      index                                               text  label  \\\n", "0         0  delta 9 engages kindred for cannabis wholesale...      2   \n", "1         1  nevgold intercepts 0. 53 g / t oxide au over 7...      1   \n", "2         2  aura minerals completes feasibility study for ...      1   \n", "3         3  acadian timber corp. reports third quarter res...      1   \n", "4         4  reminder - saputo inc. : fiscal 2023 second qu...      1   \n", "...     ...                                                ...    ...   \n", "1503   1503  discovery intercepts 124 g / t ageq over 96m a...      2   \n", "1504   1504  isracann biosciences provides bi - weekly defa...      0   \n", "1505   1505  inmed pharmaceuticals announces closing of $ 6...      1   \n", "1506   1506  agf announces estimated annual reinvested capi...      1   \n", "1507   1507  teledyne dalsa announces its new line of high ...      2   \n", "\n", "     sentiment  token_length  \\\n", "0      Positif           116   \n", "1       Neutre            56   \n", "2       Neutre           186   \n", "3       Neutre           116   \n", "4       Neutre            94   \n", "...        ...           ...   \n", "1503   Positif           158   \n", "1504   Négatif           137   \n", "1505    Neutre           252   \n", "1506    Neutre           106   \n", "1507   Positif            72   \n", "\n", "                                              input_ids  \\\n", "0     [101, 7160, 1023, 24255, 2785, 5596, 2005, 179...   \n", "1     [101, 11265, 2615, 21270, 19115, 2015, 1014, 1...   \n", "2     [101, 15240, 13246, 28123, 24010, 2817, 2005, ...   \n", "3     [101, 9353, 25205, 2078, 7227, 13058, 1012, 43...   \n", "4     [101, 14764, 1011, 20066, 16161, 4297, 1012, 1...   \n", "...                                                 ...   \n", "1503  [101, 5456, 19115, 2015, 13412, 1043, 1013, 10...   \n", "1504  [101, 2003, 22648, 11639, 16012, 11020, 13684,...   \n", "1505  [101, 1999, 7583, 24797, 17472, 5494, 1997, 10...   \n", "1506  [101, 12943, 2546, 17472, 4358, 3296, 27788, 6...   \n", "1507  [101, 10093, 2098, 9654, 17488, 3736, 17472, 2...   \n", "\n", "                                         attention_mask  \n", "0     [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "1     [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "2     [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "3     [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "4     [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "...                                                 ...  \n", "1503  [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "1504  [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "1505  [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "1506  [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "1507  [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...  \n", "\n", "[1508 rows x 7 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": [" pd.read_csv(os.path.join(csv_export_dir, \"train_processed.csv\"))\n"]}, {"cell_type": "code", "execution_count": 18, "id": "678705c4", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA1IAAAIkCAYAAAAUKhpvAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjEsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvc2/+5QAAAAlwSFlzAAAPYQAAD2EBqD+naQAAWKZJREFUeJzt3QucjHX///HPsmuxrFNYaklSIVKUlA4OUelA7u6UHCLdqXRQKeVQOiiVSudbSTonRelE0gmVw61ESaUo1jEW5Tz/x/v7+F3zn5mdZS97mNmd1/PxGGOuuWbmmtk5XO/r+/1+vkmBQCBgAAAAAIA8K5X3VQEAAAAAQpACAAAAAJ8IUgAAAADgE0EKAAAAAHwiSAEAAACATwQpAAAAAPCJIAUAAAAAPhGkAAAAAMAnghQAAAAA+ESQAoAYSUpKsjvuuCPWm4EE0rt3bzv00ENjvRkl2vjx491n+7fffov1pgAoZAQpALnuCMybNy/Wm4ICcvrpp7u/6f5OBRns7r33Xps8ebIlqkWLFtm//vUvq1u3rpUtW9YOPvhgO+OMM+yxxx4r1MddtWqV+zsuXLjQiqO///7bbf+nn35a7D8DBWXr1q02fPhwO/rooy0tLc2qVatmzZo1s+uuu879vf1asmSJe56EPSB/kvN5ewBAMXD77bfb5ZdfHrw8d+5cGzNmjN12223WsGHD4PKmTZsWaJBSkOjcubMlmtmzZ1ubNm2sTp061q9fP8vIyLCVK1faV199ZY8++qgNGDCg0B5bO9Z33nmna3nSznaosWPH2t69ey3eg5S23ws/xe0z0KNHD+vWrZulpqZaQdi1a5edeuqp9uOPP1qvXr3ce0fBavHixfbKK69Yly5drHbt2r6DlF5jvb60UAIHjiAFAPm0e/dut3NapkwZi1dqCQmlFhLtRGp5Qe6sJpJt27a51oFo7rnnHqtUqZLbWa9cuXLYdWvXrrVYSUlJsUR1oJ+Bff2doyldurQ7FRS16v7vf/+zl19+2S655JKw67Zv3247d+4ssMcC4A9d+wAcMP24n3XWWZaenm4VKlSwdu3auSPu0boJzpo1ywYOHGjVq1d3OyU6irpu3bqwdRVG1N1ER1fLly/vjujryKmOmGpsh0fr6D7zOjbhgw8+sFNOOcU9bsWKFa1Tp07uaG4o7UhF25mKHFOi+9ZjPPjgg/bII49Y/fr13ZFnbWduduzYYTfccIN77nr88847z/7444+o6/7555/Wp08fq1mzprvfxo0b27hx43Ksp+5huk6vU5UqVaxFixbu6HR+7e+1+uSTT6xUqVI2bNiwsNvpsfW6PPXUU+6y/q8d0BdeeCHYZSr0b1iYz1NdwvR4r7/+umttUGuQno9ed7UKhfriiy/swgsvdC1H2o7MzEz3t/rnn3/C1tO26z3+yy+/2Nlnn+1em+7du+e6DVpP2x0ZoqRGjRo5lr300kvWvHlzK1eunFWtWtW1aERuq96f6tql95o+G3pN1F1w1KhRYc/9+OOPd/+/7LLLgq+9Phv7ez8/8cQTdthhh7n77dChg3v8QCBgd911lx1yyCFu284//3zbuHFjju3Py2fMew31t1crpf6vz8RNN91ke/bsCW6PlolaTIq6u5333aLXWKFF77nWrVu767777jv3HPQaKYTpfaX38IYNG/b7PaTX/JxzzrEvv/zSTjjhBHd73c+ECRP2u016L8nJJ5+c4zrdj75/Q6nlSi3Beh/pen1m3nnnnbDt03te9D7yXuOC7koJJAJapAAcEO0kacdJP+KDBg1yR7qfeeYZt7P32WefWcuWLcPWV3cU7ZSon792MBRCrrnmGrez6xk8eLDbKTz33HOtY8eO9u2337pzHXU9UC+++KLrDqP7uf/++123Ie3sa+dIQfBAu7U8//zzbruuuOIKtwOunZbcqDuRdpS1Y3bSSSe5MKIdzUhr1qyxE0880e3U6LXRDqV2UPv27WvZ2dl2/fXXB7tnXXvttW5nSWMktB3ayfv6669zHLEu6Neqbdu2dtVVV9nIkSPdzvBxxx1nq1evdn/f9u3b25VXXhm8Lz1v7TTqNRKFzqJ8nmoV0mPccsstrhVI7zlto8YOKRTIxIkT3fPs37+/G3fyzTffuPCmoKvrIlse9dro9VDwUODIjcZFzZkzx77//nsXfva3nUOHDrV///vf7jXTAQZtg7pz6XUPDWN//fWXnXnmmXbBBRe49d988033/Jo0aeIOaqiL2ogRI1zQ1euuz6jofbcvau1Qy4b+jgpK+hzq/vX31g62HuPnn39226XgExp6/XzGFJi0nr4f9Bp+/PHH9tBDD7n3hv4Gei/otvq/DrboeRZ0l9O8UNBo0KCB656qMCnTp0+3X3/91QVUhSh9B/73v/915zqAFO3gTii9fnov632u10uvoYKZArRC977eS6LQNWTIkH0+jrZFgUsB+9Zbb3XB9o033nCf1UmTJrnXVO8rfa4iuzWGdm8EkEcBAIjw/PPPa88hMHfu3FzX6dy5c6BMmTKBX375Jbhs1apVgYoVKwZOPfXUHPfVvn37wN69e4PLb7jhhkDp0qUDmzZtcpezsrICycnJ7n5D3XHHHe72vXr1Ci4bPny4W5bbdi9fvtxd3rJlS6By5cqBfv36ha2nx6pUqVLY8tNOO82dIulx69atG7ys+9ZjpKenB9auXRvYn4ULF7r1r7rqqrDll1xyiVuu5+Lp27dvoFatWoH169eHrdutWze3vX///be7fP755wcaN24cyI+JEye6x585c6bv12rbtm2Bww8/3G3D9u3bA506dXKvx++//x5227S0tLC/W1E9Tz0nPbeDDz44kJ2dHVz+xhtvuOWPPvpocJn3WKFGjhwZSEpKCns+eh667a233pqnbZg2bZp7f+vUqlWrwKBBgwIfffRRYOfOnWHr/fbbb26de+65J2z5okWL3OchdLnen9qGCRMmBJft2LEjkJGREejatWtwmT63Wk+fh7y+n6tXrx78LMrgwYPd8mOOOSawa9eu4PKLL77Yfe71d/f7vvFewxEjRoSte+yxxwaaN28evLxu3bocn43CEPkZCP1u0fOMFO298uqrr7r1P//881y/h0SveeR6+v5ITU0N3HjjjfvcTj3ukUce6W6v++ndu3fgueeeC6xZsybHuu3atQs0adIk+PcRfe+edNJJgQYNGuzzuQPwj659AHzTUeVp06a5o5zqnuKpVauWaylQ9xW1LITS0fHQI6k6Uq77+f33393lGTNmuCP+au0IlZ9B+TqCvGnTJrv44ott/fr1wZPGL+iI+MyZMw/4vrt27RrsgrQv77//vjvXEeBQXquLR0e9dcRYrXH6f+j26gj+5s2bbcGCBW5dtVCoxUTjbwqKn9dKLTHqHvTDDz+4o9vvvfeePfzww6573P4U5fPs2bOn62bmUWuA3qPe30S8lilRV0Rth1pvtG1qTYmklpK80LgbtUipO6FaVtXCo+enloLQblZvvfWW69Kq1p/Q10ItHmoRiXyPqjvcpZdeGryscXlq9VNLSX5bYDSmy+O1KOuxkpOTw5ar5Urd8w70M+a1WoZ+F+R3+wta5DZGvlfUOqrnqZZV8d6z+9KoUaNgC6Ho++PII4/c73PX46oV9uabb3aX9dlTq5bey/p+VNdhUUuiWrv1XtqyZUvwb6Guh3rvLVu2LPh3A1Aw6NoHwDd1PVL3He0ERFL3EO0YanxFaHeVyJ1sdfPzuiqJF6gOP/zwsPXUZc5b1y/tOIi6J0UTObbAj3r16uVpPT0vjSnyurV5Il87vabaIVVXIZ2i8YoUqJuVukRpB1qvl8azKMBGG0NRWK+VHkuhQuNqtJOmsSJ5UZTPU0EklIK87id07MqKFStcNziFG++96FGoC6VAobFCeaWxSgpKCh4KU2+//bYLnAp06l6oHWu97gptkduaW3EIPX5k1y59PtTlMT8iP59eqNKYsWjLvdfK7/tGY3YiD0Bo+yNf+7zSwZjIsZb6zshv4Zdon28FFY3beu2113IUDIl8r0QT7UBDXp+7XneFcZ30naIDT+oa+fjjj7vr7r77btd1UO8ldRPVKRptt8I8gIJBkAJQJHKrYuWNP/AjtzEC3oB1j1fmWWM4dIQ/UuiRdt1ntG2JvM9oR6cLgretagHQ+IlovHEiCqtLly61qVOn2ocffuhaeJ588kkXCLyy0Qf6+Hl5rURHwb3B6RoMr2C9rzFD8fI8I/+2ajnSDrJC21FHHeXGlOiovcauRJYJ11g4hWK/tFOvUKXTEUcc4cbYaPyVxgvqMfTe0xixaJ8RtUAV1ucoL/e7v8fz+74pyGp2ogM2kaFHrWD5rUQZ7fOtlh6VtVfLkMrK62+j568xa3kpKV9QfzuNmdKBC413Uo8AjW9TkPK2QWPYdHAjmsgDVQDyhyAFwDcdUdZOs3ZyI6lilHY2I49k7483oFpHVUN3jNQtJfKIrddCpZaN0IH4XquWx2sFUpU0FRnYF91ntC42kffpl56XdnAUNkJboSJfO6+in3bu97etoh3+iy66yJ3U4qFB+SpaoIIdOurvl5/XShQC1LVPR8UVQjSwXYPX9xd4i/J5eq0loTusen95QU0T5v7000+usqC6AXrUXa2wqIKaqECH97pru/SeV8gqCPsrelCQ/L5vCnr7Fd4i/17HHHOMFTR9B6kVSAE+tGJl5HusKOk7S6+/CpqI181arZj7+1sU5XsEKMkYIwXANx1ZVTerKVOmhHWTUjU2laZWtS6/3eZUOl1Hr73y2R51Xclt5+3zzz8PLvNKbYfSUVlthypvaVLLSKFdgnSfCoGhy9QdS2Xb80OV1CQyZKiCXORrqnFXanXxdoxy29bIcstq8VA3Me2QR3ueeeHntdJ4DQUojfO68cYb3RF6/Z1UrTEyBCnsxup5qsqZxop4VOFOAcb7m3gtBKEtAvq/JszNL7WKRGtp8MZneaFawVDboR30yPV1OfI1yAtvzqPI174w+Hnf5JXXspmX7VeYVmgIPR1oV+B9ifZeifY5Lgz6HtJYp2gHeVSm3XsvKcyqJU7VU72gntvfoijfI0BJRosUgFypPK+6VEVSKWp1JdGRYIUmFYhQCNIPuLp8hc5rk1eaT0j3q1LIGqCv7jLagVCXp4MOOijsCKpCnMYbaMC1duK1k6NtVWuHxrx4tIOnYNajRw9Xpltz83jrqECCxtp4QU1dZUaPHu12DHW/Gkvw9NNPu3FekYUz/FAXIA3EV5c0jaNQIQMd2VbLSKT77rvP7YBrkH6/fv1caFC3Mw1k11ghb/4ePX8didf263VTy5Ceh0qqhxZX8COvr5UG2atLnsb0qGVIFALeffdd12VNrTzeTprKOmu79bpqbjC1uui5FdXz1FgZvT+1XQr52ulV1yY9pqgrnwK0ukKpO59eAwW8Ax2vE0pFANTdUd2v9DhqTVO3MJX7VzlwbZPo8fVZUgubDkqogIue2/Lly92YKhVp0fb5oftUS63ev7ov/T30Wud1XJ8ffj5jfrrV6T2h10qtdPo7qoT8/srIFyY9TxVW0XebAqPGGangjv5OhU3fs2oB1veiiluoS6Faz/Wdp+/b0Dm2NGZR73mVw9f7XK1Ueu+r8IkKt+g71fte0vemytXre0ndVjXOLdocZwD24QAq/QEo4bzyvbmdVq5c6dZbsGBBoGPHjoEKFSoEypcvH2jTpk1g9uzZeSql7pWoDi2/u3v37sDQoUNdOedy5coF2rZtG/jhhx8C1apVC1x55ZVht58/f36gZcuWrhRznTp1AqNHj45adth7LG2nyjGXLVs2UL9+fVdCeN68eWHrvfTSS4HDDjvM3WezZs1cuercykU/8MADeX49//nnn8C1117rnodKgp977rnuNYxW4lklja+++upAZmZmICUlxb0WKmn83//+N7jOM88840rM6/5UPlnP5+abbw5s3rw5z9uUW/nj/b1WXtn6r7/+Oux2ul7luvv37x9c9uOPP7rt1N8ysoR9YT5P772l0tQq412jRg23DSrTHlmifcmSJa40v97DBx10kCvX/e233+YoH65t198urz744INAnz59AkcddZS7b72nVDJ+wIABUctWT5o0KdC6dWv3GDrpdnp9li5dGlb+PFo5+Mj3qEyZMiXQqFEj9zcJfS55fT97r6HeJ3n9PO/vM5bbaxhtOgN9j6gkul63wiqFvq/y5yrBHumPP/4IdOnSxZV71/O88MIL3ZQPkduXW/lzvf8i5TbtQqhff/01MGzYsMCJJ57o3sv6m6pcve7vk08+ybG+pqTo2bOn+0zps6VpAM4555zAm2++Gbbe2LFj3fedPs+UQgcOTJL+2VfQAoBYUtcTddXRUfvbb7891puDYkBFMNq0aeMKOqhCHgAAhYExUgDixj///JNjmTcGIb9VuAAAAAoSY6QAxA2NidBkk2effbYbB6CJfV999VU3ViY/cyQBAAAUNIIUgLihstQqWqEB3Srw4BWgULc+AACAeMIYKQAAAADwiTFSAAAAAOATQQoAAAAAfGKMlJnt3bvXVq1a5SYuDJ30EwAAAEBiCQQCtmXLFjeZfKlSubc7EaTMXIjKzMyM9WYAAAAAiBMrV660Qw45JNfrCVJmriXKe7HS09NjvTkAAAAAYkSVg9XI4mWE3BCkVLrw/7rzKUQRpAAAAAAk7WfID8UmAAAAAMAnghQAAAAA+ESQAgAAAACfCFIAAAAA4BNBCgAAAAB8IkgBAAAAgE8EKQAAAADwiSAFAAAAAD4RpAAAAADAJ4IUAAAAAPhEkAIAAAAAnwhSAAAAAOATQQoAAAAAfEr2ewOgpNm7d6/98ssvtnnzZqtUqZLVr1/fSpXiGAMAAAByR5BCQlu4cKG9/fbbtnHjxuCyqlWrWpcuXaxZs2Yx3TYAAADEL4IUEjpEjRs3zho3bmy9e/e22rVr26pVq2zatGlueZ8+fQhTAAAAiIr+S0jY7nxqiVKI6tevn9WrV89SU1PduS5r+eTJk916AAAAQCSCFBKSxkSpO1+HDh1yjIfSZS3fsGGDWw8AAACIRJBCQlJhCVF3vmhq1aoVth4AAAAQiiCFhKTqfKIxUdGsXr06bD0AAAAgFEEKCUklzlWdT4UlIsdB6bKWV6tWza0HAAAARCJIISFpHJRKnC9evNjGjh1ry5cvt+3bt7tzXdbyzp07M58UAAAAokoKBAIBS3DZ2dmuC5fGw6Snp8d6cxDjeaTUEqUQRelzAACAxJOdx2zAPFJIaApLTZs2ddX59GHRh0bd+WiJAgAAwL7EdG/xjjvusKSkpLDTUUcdFbxeXa2uvvpq10JQoUIF69q1q61ZsybsPlasWGGdOnWy8uXLW40aNezmm2+23bt3x+DZoLhSaGrQoIG1aNHCnROiAAAAsD8xb5HSxKcff/xx8HJy8v/fpBtuuMHee+89mzhxomspuOaaa+yCCy6wWbNmuev37NnjQlRGRobNnj3bVVrr2bOnpaSk2L333huT5wMAAACg5It5kFJwUhCKpG5Wzz33nL3yyivWtm1bt+z555+3hg0b2ldffWUnnniiq6y2ZMkSF8Rq1qzpumnddddddsstt7jWrjJlysTgGQEAAAAo6WLeh2nZsmVuUtTDDjvMunfv7rrqyfz5823Xrl3Wvn374Lrq9lenTh2bM2eOu6zzJk2auBDl6dixoxsgpqprudmxY4dbJ/QEAAAAAMUiSLVs2dLGjx9vH374oT311FOu9PQpp5xiW7ZssaysLNeiVLly5bDbKDTpOtF5aIjyrveuy83IkSNdV0HvlJmZWSjPDwAAAEDJFNOufWeddVbw/6qcpmBVt25de+ONN6xcuXKF9riDBw+2gQMHBi+rRYowBQAAAKDYdO0LpdanI444wn7++Wc3bmrnzp22adOmsHVUtc8bU6XzyCp+3uVo4648qampriZ86AkAAAAAimWQ2rp1q5vPp1atWta8eXNXfW/GjBnB65cuXerGULVq1cpd1vmiRYts7dq1wXWmT5/uglGjRo1i8hwAAAAAlHwx7dp300032bnnnuu6861atcqGDx9upUuXtosvvtiNXerbt6/rgle1alUXjgYMGODCkyr2SYcOHVxg6tGjh40aNcqNixoyZIibe0qtTgAAAABQ4oLUH3/84ULThg0brHr16ta6dWtX2lz/l4cffthNjqqJeFVpTxX5nnzyyeDtFbqmTp1q/fv3dwErLS3NevXqZSNGjIjhswIAAABQ0iUFAoGAJTgVm1ALmOauYrwUAAAAkLiy85gN4mqMFAAAAAAUBwQpAAAAAPCJIAUAAAAAPhGkAAAAAMAnghQAAAAA+ESQAgAAAACfCFIAAAAA4BNBCgAAAAB8IkgBAAAAgE8EKQAAAADwiSAFAAAAAD4RpAAAAADAJ4IUAAAAAPhEkAIAAAAAnwhSAAAAAOATQQoAAAAAfCJIAQAAAIBPBCkAAAAA8IkgBQAAAAA+EaQAAAAAwCeCFAAAAAD4RJACAAAAAJ8IUgAAAADgE0EKAAAAAHwiSAEAAACATwQpAAAAAPCJIAUAAAAAPhGkAAAAAMAnghQAAAAA+ESQAgAAAACfCFIAAAAA4BNBCgAAAAB8IkgBAAAAgE8EKQAAAADwiSAFAAAAAD4RpAAAAADAJ4IUAAAAAPhEkAIAAAAAnwhSAAAAAOATQQoAAAAAfCJIAQAAAIBPBCkAAAAA8IkgBQAAAAA+EaQAAAAAwCeCFAAAAAD4RJACAAAAAJ8IUgAAAADgE0EKAAAAAHwiSAEAAACATwQpAAAAAPCJIAUAAAAAPhGkAAAAAMAnghQAAAAA+ESQAgAAAACfCFIAAAAA4BNBCgAAAAB8IkgBAAAAgE8EKQAAAADwiSAFAAAAAD4RpAAAAADAJ4IUAAAAAPhEkAIAAAAAnwhSAAAAAOATQQoAAAAAfCJIAQAAAIBPBCkAAAAA8IkgBQAAAAA+EaQAAAAAwCeCFAAAAAD4RJACAAAAAJ8IUgAAAADgE0EKAAAAAHwiSAEAAABAcQ1S9913nyUlJdn1118fXLZ9+3a7+uqrrVq1alahQgXr2rWrrVmzJux2K1assE6dOln58uWtRo0advPNN9vu3btj8AwAAAAAJIq4CFJz5861Z555xpo2bRq2/IYbbrB3333XJk6caJ999pmtWrXKLrjgguD1e/bscSFq586dNnv2bHvhhRds/PjxNmzYsBg8CwAAAACJIuZBauvWrda9e3cbO3asValSJbh88+bN9txzz9no0aOtbdu21rx5c3v++eddYPrqq6/cOtOmTbMlS5bYSy+9ZM2aNbOzzjrL7rrrLnviiSdcuAIAIC/27t1ry5Yts3nz5rlzXQYAYF+SLcbUdU+tSu3bt7e77747uHz+/Pm2a9cut9xz1FFHWZ06dWzOnDl24oknuvMmTZpYzZo1g+t07NjR+vfvb4sXL7Zjjz22yJ8PAKB4Wbhwob399tu2cePG4LKqVataly5d3EE6AADiLki99tprtmDBAte1L1JWVpaVKVPGKleuHLZcoUnXeeuEhijveu+63OzYscOdPNnZ2fl+LgCA4hmixo0bZ40bN7bevXtb7dq1XTdy9XjQ8j59+hCmAADx1bVv5cqVdt1119nLL79sZcuWLdLHHjlypFWqVCl4yszMLNLHBwDEnrrvqSVKIapfv35Wr149S01Ndee6rOWTJ0+mmx8AIL6ClLrurV271o477jhLTk52JxWUGDNmjPu/WpY0zmnTpk1ht1PVvoyMDPd/nUdW8fMue+tEM3jwYDcGyzsp1AEAEssvv/ziuvN16NDBSpUK/znUZS3fsGGDWw8AgLgJUu3atbNFixa5bhXeqUWLFq7whPf/lJQUmzFjRvA2S5cudeXOW7Vq5S7rXPehQOaZPn26paenW6NGjXJ9bB1x1DqhJwBAYtGBNFF3vmhq1aoVth4AAHExRqpixYp29NFHhy1LS0tzc0Z5y/v27WsDBw50g34VdgYMGODCkwpNiI4WKjD16NHDRo0a5cZFDRkyxBWwUFgCACA36totGhOl7nyRVq9eHbYeAABxVf58Xx5++GE755xz3ES8p556quuu99ZbbwWvL126tE2dOtWdK2Bdeuml1rNnTxsxYkRMtxsAEP/q16/vDtSpsETkOChd1nId3NN6AABESgoEAgFLcKrapyOO6r5BNz8ASMyqferloO58aolSiNI0GlTtA4DEk53HbECQIkgBQEKLNo+UWqI6d+5MiAKABJSdx2wQ1137AAAoCpHHFCl5DgCI6wl5AQCIl659l112GRPyAgDyjBYpAEBCYkJeAEB+EKQAAAmJCXkBAPlBkAIAJCQm5AUA5AdBCgBgiT4hbzRMyAsA2BeCFAAgITEhLwAgPwhSAICEpHFQXbp0cRPvjh071pYvX27bt29357qs5ZpLKnL8FAAAwoS8TMgLAAmNCXkBAAeSDZhHCgCQ0BSWmjZt6qrz6UdTP57qzkdLFABgXwhSAICEp9DUoEGDWG8GAKAY4XAbAAAAAPhEixQAAAASnqp10sUXfhCkAAAAkNCiFZ3R9Aiq7EnRGeSGIAUAAICEDlHjxo2zxo0bW+/eva127dpuom7NJaflffr0IUwhKtorAQAAkLDd+dQSpRDVr18/q1evnqWmprpzXdbyyZMn55i0GxCCFAAAABKSxkSpO1+HDh1yjIfSZS3fsGGDWw+IRJACAABAQlJhCVF3vmhq1aoVth4QiiAFAACAhKTqfKIxUdGsXr06bD0gFEEKAAAACUklzlWdT4UlIsdB6bKWV6tWza0HRCJIAQAAICFpHJRKnC9evNjGjh1ry5cvt+3bt7tzXdbyzp07M58UokoKBAIBS3DZ2dmuyVb9X9PT02O9OQAAAIjxPFJqiVKIovR54snOYzZgHikAAAAkNIWlpk2buup82nnWTrS689EShX3h3QEAAAAAPtEiBQAAgIQWrWufilBo/BRd+5AbghQAAAASOkSNGzfOGjdubL1793ZzSqkcuir2aXmfPn0IU4iKrn0AAABISCpxrpYohah+/fpZvXr1LDU11Z3rspZPnjw5R2l0QAhSAICEp52kZcuW2bx589w5O01AYlBxCXXn69ChQ47CErqs5Rs2bHDrAZHo2gcASGiMjQASlyr0ibrzRVOrVq2w9YBQBCkAQMJibASQ2FTmXPS5V3e+SKtXrw5bDwhF1z4AQEJibAQAzRWlFmgdPIn8rOuylmtiXq0HRCJIAQASEmMjAOizrm68ixcvtrFjx9ry5ctt+/bt7lyXtbxz585MzIuo6NoHAEhIjI0AIOq+q268aqEePXp0cLlaoujei30hSAEAEhJjIwB4FJaaNm3qWqB18ESfe3XnoyUK+8K7AwCQkBgbASCUQlODBg2sRYsW7pwQhf3hHQIASEiMjQAA5EdSIBAIWILLzs52Tbhqyk1PT4/15gAAYjyPlFqiFKIYGwEAiSc7j9mAMVIAgITG2AgAwIEgSAEAEp43NgIAgLzicBsAAAAA+ESQAgAAAACf6NoHAACAhKdpDxgrCT8IUgAAAEho0ap3ap45TZFA9U7khiAFAACAhA5R48aNs8aNG1vv3r2tdu3atmrVKjcpt5b36dOHMIWoaK8EAABAwnbnU0uUQlS/fv2sXr16lpqa6s51WcsnT57s1gMiEaQAAACQkDQmSt35OnTokGM8lC5r+YYNG9x6QL6D1IIFC2zRokXBy1OmTHGzv9922222c+dOv3cHAAAAxIQKS4i680VTq1atsPWAfAWp//znP/bTTz+5///666/WrVs3K1++vE2cONEGDRrk9+4AAACAmFB1PtGYqGhWr14dth6QryClEOUNuFN4OvXUU+2VV16x8ePH26RJk/zeHQAAABATKnGu6nwqLBE5DkqXtbxatWpuPSDfQSoQCATfaB9//LGdffbZ7v+ZmZm2fv16v3cHAAAAxITGQanE+eLFi23s2LG2fPly2759uzvXZS3XEBbmk0I0SQElIx/atm3rQlP79u2tb9++tmTJEjv88MPts88+s169etlvv/1mxU12drZrslX/1/T09FhvDgAAAGI8j5RaohSiKH2eeLLzmA18zyP1yCOPWPfu3V0pyNtvv92FKHnzzTftpJNOyt9WAwAAAEVMYalp06auOp92nrUTre58tERhX3wHKb3JQqv2eR544AErXbq037sDAAAAgJIfpDwqdb527docA/Pq1KlTENsFAAAAxKxrn4pQaPwUXftQYEFKVfs0Nmr27NlhyzXUKikpyfbs2eP3LgEAAICYhahx48ZZ48aNrXfv3m5OKZVDV8U+Le/Tpw9hCgUTpC677DJLTk62qVOnuknKFJ4AAACA4kY9q9QSpRDVr1+/4JioevXqucuq3Ke6ABrawngp5DtIKbXPnz/fjjrqKL83BQAAAOKGikuoO59aomTZsmVhxSY6dOhgo0ePdus1aNAg1puL4h6kGjVqxHxRAAAAKPYUmkT7tuPHj88xRqpTp05h6wH5ClL333+/DRo0yO69915r0qSJpaSkhF3PPEwAAAAoDtTyJBMmTLCjjz46xxipF198MWw9IF9BShPxSrt27cKWU2wCAAAAxYnGQmnsU1pamiumpjoA3nJdHjZsmG3bts1dBvIdpGbOnOn3JgAAxP2AcybiBBLP8uXL3ed/y5Yt9txzz7kxUSqmtnr1atcipeXeeoyRQr6D1Gmnneb3JgAAxC3mjwESlzf2qVevXvbuu++6whKeatWqWc+ePV23P8ZIocAm5P3iiy/smWeesV9//dUmTpxoBx98sOtDqmbP1q1bH8hdAgBQ5Jg/Bkhs3tgnhaahQ4e6fdx169ZZ9erV7ZRTTrGVK1eGrQfkK0hNmjTJevToYd27d7cFCxbYjh073HIldRWgeP/99/3eJQAARY75YwCoG69aoNUwoLFQoS3Tn376qRs7pZCl9YBIvn8Z7r77bnv66afdD0xoxb6TTz7ZBSsAAIrT/DEaExEZlHRZyzds2ODWA1Ay6bN+7LHHupanXbt2Wbdu3dy+rs51WcvVKs3BFBRIi9TSpUvt1FNPzbFcTZ6bNm3ye3cAAMSEN+ZB3fmi0YDz0PUAlMyW6f/9739Wp04dV1jitddeC16nliotVxfg8847jzCF/AepjIwM+/nnn+3QQw8NW/7ll1/aYYcd5vfuAACICW/Mg8ZERSttrKpdoesBKLkt0xojWbdu3RzVO3///XdXgELLqdqHfAcp9Ru/7rrr3CBczRulH6A5c+bYTTfd5AbpAQBQnMZGqLCE5otReWNvB0rBSssZGwEkTsu0WpwiwxIt0yjQIHXrrbe6ZlBNyPv333+7bn6pqakuSA0YMMDv3QEAEBPaaVKJc80dM2jQIDcewqMxwLqsgEV3HqDkomUa+eH712H37t12++23u2bQ77//3r766itXJvKuu+6y9evX52tjAAAAgFi0TKuhIJQu0zKNAg1SqmISCASsTJky1qhRIzvhhBOsQoUKtmbNGjv99NP93h0AADEtf3700UfbfffdZxdccIGbN0bnuqzlKn8euXMFoOS1TC9evNhVpFYX3+3bt7tzXdbyzp070zKNgunat2LFCrv88stdV4jQZs+2bdu6uTgAAChOg8w1fcc999yTY/4YLVfPCwaZAyWbyptr8m0dWFFhCY9aopiUGwUapDThrsZFDRw40L3Z1Ke0TZs2dswxx4SVjAQAIJ55g8enTp3qDgSqapcGnOt3Td15tDx0PQAll8KSWqG/+OILN2SlevXqroU6Odn3rjISiO92Sr2x9AMzadIkF6bUnU8Tmb366qu+mz2feuopN2N8enq6O7Vq1co++OCD4PVqWr366qvdEQF1H+zatavrQhjZQtapUycrX7681ahRw26++WY3jgsAgH2pWLGiO9cAc1Wk1bmKJ4VeDl0PQMmluaI03v+tt95yYUrnuqzlQG4OqMNnZmamTZ8+3V5++WU3RkohqnTp0r7v55BDDnH90OfPn2/z5s1z3QPPP/981x9VbrjhBnv33Xdt4sSJ9tlnn7mjhOq77tmzZ48LUTt37rTZs2fbCy+8YOPHj7dhw4YdyNMCAABAglFY0rQ+apFWI8GDDz7oznVZywlTyE1SQJUj9qNKlSpuzqhIKn+uo3ehISq0j/mBUOWUBx54wP71r3+51q9XXnnF/V9+/PFHa9iwoZu36sQTT3StV+ecc44LWDVr1nTrPP3003bLLbe4ZlkVxMiL7OxsV9ZS3TfUMgYAKPl0AE8H4ERdejp06ODmjNG4X/W80Pgo6dWrl7Vo0SLGWwugMKiYzJ133ulCk1qiQ3tX6ToVnNB3gg7SU3AicWTnMRvkqePnI488YoVNrUtqedq2bZvr4qdWKs3h0b59++A6Rx11lNWpUycYpHTepEmTYIiSjh07Wv/+/V2rlrocRrNjxw53Cn2xAACJxZsX5txzz7VZs2blGGSuA3UaJ8X8MUDJLzqjMZKRQUmXdYBF3w0UncEBBykdjSssixYtcsFJ46E0DkoVU1RWXc2oalGqXLly2PoKTVlZWe7/Og8NUd713nW5GTlypDv6AABIXN78MSpzPHToUHeuo48KThofpeq0zB8DlGxeMRm1SEWjVurQ9YBQpQ609UjFJu6++253UvjRsgNx5JFHutD09ddfu5YkhbYlS5ZYYRo8eLD7QHinlStXFurjAQDie/4YhSZV51IXP53rMvPHACWf1+KsYSLRqFtf6HpAKN81HX/++Wc7++yz7c8//3QhyGvhUQGK9957z/eRO7U6HX744e7/zZs3t7lz59qjjz5qF110kSsisWnTprBWKVXty8jIcP/X+TfffBN2f15VP2+daDSuSycAQGJj/hggsXkt0xoXGW2MlJbTMo0CC1LXXnutezN99dVX7o0nGzZssEsvvdRdpzCVH3rTavySQlVKSorNmDHDlT2XpUuXunLn6gooOtckimvXrnWlz0XVBDUoTN0DAQDYH4UlTcWhMRBe1z79ztESBSROy7Sq86mwRGTRGbVM66AK3wc44Kp9odLS0lyIUpGHUN9++62bBX7r1q2+utidddZZroDEli1bXIW++++/3z766CM744wzXFc/TQCskuYKRwMGDHC3U6lzUXdC/QCqX+uoUaPcuKgePXrY5Zdfbvfee2+et4OqfQAAAIlLw0zUMh1afVotUereS8t04skuyKp9odQlTqEnkgJUXsuNe9SS1LNnT5f6tbE6IuiFKHn44YfdEQC1SKmVShX5nnzyyeDtVXZdFZUUuNQ6pZCnMVYjRozw+7QAAACQoGiZRpG0SCn4LFiwwA3E1WS8okIR6leq7nhqPSpuaJECAAAA4Ccb+I7ZY8aMcQldLUBly5Z1J3XpU8GIophvCgAAAABizXfXPlXQmzJliqve98MPP7hlDRs2DFbeAwAAAICSzneLlMYf/f333y44aTZ4nfT/f/75h7FJAIBiSRVjly1bZvPmzXPnugwgsfA9gEIfI6UCDyoO4ZUb96gEupYd6MS8scQYKQBIXNGqdWl6D5VEploXkBj4HkCRjJFS7kpKSsqxXOXPvXmlAAAoLjtPKp4UWY1Wl7Vc1wMo2fQ51zxSmk5n4MCB9uCDD7pzXdZyvgeQ7zFSVapUcQFKpyOOOCIsTKkVSuXPr7zyyrzeHQAAMaVuO6+//rr7v37XNMWGdpxWrVrlpuLQRJxvvPGGK4lMCWSg5H4PqCWqcePGrgK191mvV6+eu6xJeidPnsz3APIXpFSRT61Rmt35zjvvdM1dHs0fdeihh7pKfgAAFAcaA6GDgIcddpibyH358uW2aNEi9/umy4899pj9+uuvbr0jjzwy1psLoBBo3ih15+vdu3eOoKTLHTp0sNGjR7v1GjRoELPtRDEPUpro1kvoKneenOy74B8AAHFDAUkUku66664cYyNatmxJkAJKOI2BEbVGR1OrVq2w9YBQvtPQaaed5vcmAADErQ8++MCOPvpod0Ta69o3bdo0txxAyeb1sNLnXo0FkVRgLXQ9IBSdPQEACUmTy0v58uWtb9++bicqNTXVneuyloeuB6Dk0edbLdA6eBJZ7lyXtbxatWp8DyAq+ucBABKSNx5CcyNqQHn16tVt586dbtzvunXr3PLQ9QCUPPp8q8S5qvPpe0BjotSdTy1RClEqOqP6AHwPIBqCFAAgIYWWPF+yZEme1gNQ8mieKIUlVe9TYQmPWqK0nHmkUOBB6ueff3YVTE499VQrV65crvNLAQAQj/I65oGxEUDJp7CkEufat1VhCX3u1Z2PligUaJDasGGDXXTRRfbJJ5+44KRqRiodq/7kmmvqoYce8nuXAAAUubp167rz0qVL23333WcrV64M7kBlZmbarbfe6uZJ9NYDULIpNFHiHH74jtk33HCDK32+YsWK4EBcUbj68MMP/d4dAAAxMWvWLHeusPTCCy+43zZV79O5Lmt56HoAAOSrRUoD7zTj+yGHHBK2XAn+999/93t3AADEhApKyCWXXOIOBEaOjbj44ovt1VdfDa4HAEC+gtS2bdvCWqI8mshQZWMBACgOVKXPK3E8fPjwHGMj5syZE7YeAAD56tp3yimn2IQJE4KXNU5KP0KjRo2yNm3a+L07AABiQr9nGhPx3nvvud8x9axo0aKFO9dlLdf1Wg8AgHy3SCkwtWvXzubNm+fm2xg0aJCrsa8WKfqRAwCKC42F0gHAGTNm2LBhw6xTp07WuHFj95umEKWy5/q903oAAERKCqhuuU/q+vD444/bt99+a1u3brXjjjvOrr76ajeBWXGUnZ3tunLoeaWnp8d6cwAARWjy5Mk2c+ZM1wrlUUuUQlbnzp1jum0AgPjNBgcUpEoaghQAJLbdu3fbF1984QpLaEyUuvPREgUkFh1MYR4p+MkGefqV+O677yyvNJkZAADFiXaWVI22YsWK7seTnScgsSxcuNDefvttN1TFU7VqVevSpYubrBc44BYp/aCoqIRW1bnHu2noMm/ejeKEFikASFzsQAGJTd8B48aNc2MkO3ToYLVr17ZVq1a5KX80ZrJPnz58FySY7Dxmgzwdclu+fLn9+uuv7nzSpElWr149e/LJJ90bTyf9X82fug4AgOK2A6UdpwsvvNDNKaVzXdZyXQ+gZHfn04EUhah+/fq5fVxN56NzXdZyjaMMHUMJ+OraV7du3eD/9QMzZswYO/vss8O682VmZtrQoUMZmAsAKFY7UPr9+vPPP+37778PXlelShW3XDtQ+o2jqx9QMmlMlFqje/funeNzrstqodJk3VpPUyMAoXyPpF20aJFL6ZG0bMmSJX7vDgCAmO5A6aSjzu3bt7cyZcq4qT30e6YuPd567EABJZO6bolaoaPxKlJ76wH5ClINGza0kSNH2rPPPut+cEQ/Olqm6wAAKA42bdrkzg8++GA3HsILTl6LlJarpcpbD0DJo3Ewou+AaA0Fq1evDlsPyFeQevrpp+3cc8911Y28Cn2q6qeCE++++67fuwMAICY0D6IoLKWkpOS47q+//gpbD0B0OqC+Zs0aK47UKKCQNGXKFLvgggtyFFXTcl2v9VauXGnFVc2aNYMNIIhhkDrhhBNc4YmXX37ZfvzxR7fsoosucgN009LSCnDTAAAoPOXKlSvQ9YBEpRA1atQoK87Ude+BBx7I9foHH3zQirNBgwa5cZ8oWAc026AC0xVXXFHAmwIAQNH5+++/g//XkdrWrVvbQQcdZOvXr7dvvvnGdu3alWM9ANFbO7SjXpz99NNPNnPmzLCxUGqJatOmjR1xxBFWEv5GKHhM2w4ASEhbtmxx58nJyfbPP/+4najQal1avnv37uB6AKLTgYji3tqh7VdomjNnjr322mvWrVs3a9WqFRU7sU8EKQBAQvKOPCssVaxY0Y4//nirVq2abdiwwebOnRsMUFTrAhKDQlOdOnXc/3VOiML+EKQAAAmpcuXKwaPppUuXtk8++SSsap9XCt1bDwCAUAQpAEBC8gokKSwdfvjhdswxx7hxUargt27duuDciBRSAgAUWJDSnBpvvvmmm6Tw5ptvtqpVq9qCBQvcQDbNuwEAQLxLT08P/n9fE8qHrgcAwAEHKc0ZpdnfVcnkt99+s379+rkg9dZbb9mKFStswoQJfu8SAIAiF9plT3PHaM6YaJfp2gcAiMb3KLqBAwda7969bdmyZVa2bNng8rPPPts+//xzv3cHAEBM1KtXzw0mV1e+0BAluqzlul7rAQCQ7xYpVTJ65plncixXl76srCy/dwcAQEwsX77c9u7d6077qtqn9Ro0aBDrzQUAFPcglZqaatnZ2VEnMqtevXpBbRcAAIXqr7/+cueHHHKIm3Q3tGqfApWW//HHH8H1AADIV5A677zzbMSIEfbGG28E+5FrbNQtt9xiXbt29Xt3AADExLZt29x569at3cSbKqCkOaM0Brh+/frBiTm99QAAyNcYqYceesi2bt1qNWrUcDPBn3baaa5srLpF3HPPPX7vDgCAmKhQoYI7//bbb925uu+1aNEi2I3PW+6tBwBAvlqkdKRu+vTpNmvWLPcjo1B13HHHuUp+AAAUF141vh9++MHGjh1rHTp0sFq1atnq1att2rRpbnnoegAAHHCQ0kSF5cqVs4ULF9rJJ5/sTgAAFEfqvqfpO9Ti9Oeff9ro0aOD12l5nTp1XLc+rQcAQL6ClErB6odlz549fm4GAEDcUWnzLl262Lhx46xx48auZ4V+53TQUK1Rixcvtj59+rj1AADId9e+22+/3W677TZ78cUX3RE7AACKq2bNmrmw9Pbbb9v3338fVrVPy3U9AAAFEqQef/xx+/nnn6127dpWt25dS0tLC7t+wYIFfu8SAICYUVhq2rRpjqp9tEQBAAo0SHXu3NnvTQAAAAAgsYPU8OHDC2dLAACIARVQUte+jRs3Bpep67rGT9G1DwBQYEHKM2/evGBp2EaNGlnz5s0P9K4AAIhZiFKxiYYNG1qTJk1s9+7dlpycbOvWrXPLGScFACiwIPXHH3/YxRdf7OaR8ubW2LRpk5100kluBvhDDjnE710CAFDk9u7d61qi1Pr0448/2pIlS4LXaXyUlk+ePNmNn2K8FAAgku9fhssvvzxYGlbdIHTS//WDpOsAACgOVFxCv2EbNmxwhZO6detm99xzjzvXZS3XSesBAJDvFqnPPvvMZs+ebUceeWRwmf7/2GOP2SmnnOL37gAAiAmFJNGEvCNGjHBd+kSTzbds2dKGDh1qW7dudes1aNAgxlsLACj2LVKZmZmuRSqSJulVSXQAAIqDFStWuPMTTzwxGKI8uqzloesBAJCvFqkHHnjABgwYYE888YS1aNEiWHjiuuuuswcffNDv3aGY2rlzp61ZsybWm4F9qFmzppUpUybWmwHEvZUrV7oiE8uXLw/OI1WvXj0CFAAg/0GqSpUqlpSUFLy8bds21+3BO4LnVTlSdSPmmUoMClGjRo2K9WZgHwYNGuRakAFEV716dXe+dOlS93kJ7W2RkpISvOytBwCA7yD1yCOP5GU1JFhrh3Y8SoqsrCybMGGC9ezZ0zIyMqyk/I0A5E7jelW1LxAIuIJJobzLOojI+F8AwAEHqV69euVlNSQQdRkria0dClEl8XkByEklzVNTU2379u1Wvnx5O/74461atWquuMTcuXNty5Yt7npKnwMACnRC3rVr17pT5FE8zbcBAEC8U1lzhSiN912wYIF98sknwesUnrRcY4C1HlX7AAD5DlLz5893LVSaO0rdIUKpC4Sq9wEAEO9UWEI0b1T37t3tiy++sHXr1rkxUerOp/G/ClLeegAA5CtIqaDEEUccYc8995wbgxFahAIAkDiKe/XOHTt2uPOFCxe66TsOP/xwd5LVq1fbn3/+GVxPlf2KKyp4AkCcBKlff/3VJk2aFPyxAQAkppJSvfOll17a5/WvvfaaFWdU8ASAOAlS7dq1s2+//ZYgBQAJriRU7/zpp59s8uTJVr9+fdfb4oMPPrCzzjrLLdfYKE3poeXFGRU8ASBOgtSzzz7rxkh9//33dvTRR7u5NkKdd955Bbl9AIA4VRKqd2r7ValPZdAVokTnWta3b19r1qxZrDcRAFBSgtScOXNs1qxZwR+cUBSbAAAUNwpLqjir3zd141PxiVatWlH2HACwT75/JQYMGGCXXnqpG4ir0uehJ0IUAKA4UmiqU6eO+7/OCVEAgP3x/UuhiQpvuOEG+lwDAAAASFi+g9QFF1xgM2fOLJytAQAAAICSOEZK1YsGDx5sX375pTVp0iRHsYlrr722ILcPAAAAAEpG1b4KFSrYZ5995k6RxSYIUgAAAABKOt9Bavny5YWzJQAAAABQTOSrLFEgEHAnAAAAAEgkBxSkJkyY4MZHlStXzp00/8aLL75Y8FsHAAAAACWha9/o0aNt6NChds0119jJJ5/slqnwxJVXXmnr1693pdEBAAAAoCTz3SL12GOP2VNPPWX333+/nXfeee40atQoe/LJJ23MmDG+7mvkyJF2/PHHW8WKFa1GjRrWuXNnW7p0adg627dvt6uvvtqqVavmilx07drV1qxZE7bOihUrrFOnTla+fHl3PzfffLPt3r3b71MDAAAAgMIJUqtXr7aTTjopx3It03V+qOqfQtJXX31l06dPt127dlmHDh1s27ZtwXXUwvXuu+/axIkT3fqrVq1yc1l59uzZ40LUzp07bfbs2fbCCy/Y+PHjbdiwYX6fGgAAAAAUTpA6/PDD7Y033six/PXXX7cGDRr4uq8PP/zQevfubY0bN7ZjjjnGBSC1Ls2fP99dv3nzZnvuuedcd8K2bdta8+bN7fnnn3eBSeFLpk2bZkuWLLGXXnrJmjVrZmeddZbddddd9sQTT7hwBQAAAAAxHyN155132kUXXWSff/55cIzUrFmzbMaMGVEDlh8KTlK1alV3rkClVqr27dsH1znqqKOsTp06NmfOHDvxxBPduQpf1KxZM7hOx44drX///rZ48WI79thj87VNAAAAAJDvIKUxSl9//bU9/PDDNnnyZLesYcOG9s033+QrtOzdu9euv/56F86OPvpotywrK8vKlCljlStXDltXoUnXeeuEhijveu+6aHbs2OFOnuzs7APebgAAAACJx3eQEnWxU1e6gqSxUt9//72rAFjYVORCLWsAAAAAUOQT8hYUlVKfOnWqzZw50w455JDg8oyMDDfOadOmTWHrq2qfrvPWiazi51321ok0ePBg143QO61cubIQnhUAAAAAS/QgVapUKStduvQ+T8nJ/hq4AoGAC1Fvv/22ffLJJ1avXr0cLV8pKSlu/JVH5dFVkKJVq1buss4XLVpka9euDa6jCoDp6enWqFGjqI+bmprqrg89AQAAAEBe5Tn5KOzkRgUfNIeUxjn57c73yiuv2JQpU9xcUt6YpkqVKlm5cuXced++fW3gwIGuAIUCz4ABA1x4UqEJUbl0BaYePXq4+ax0H0OGDHH3rcAEAAAAADELUueff36OZWoduvXWW908T927d7cRI0b4enBN7Cunn3562HKVOFdZdFFRC7WGqciFCkSoIp8m//WoJUzdAlWlTwErLS3NevXq5XtbAAAAAKBQi01oUtzhw4e7yW8VbBYuXBistOe3a9/+lC1b1s0JpVNu6tata++//77vxwcAAACAQi82ocIMt9xyi5uUV3M0aeySWqMOJEQBAAAAQIlvkdL4o/vvv99Vwnv11VejdvUDAAAAgESQ5yClsVAqAKHWKHXp0ymat956qyC3DwAAAACKb5Dq2bOnJSUlFe7WAAAAAEBJClLjx48v3C0BAAAAgJJYbAIAAAAAQJACAAAAAN8IUgAAAADgE0EKAAAAAHwiSAEAAACATwQpAAAAAPCJIAUAAAAAPhGkAAAAAMAnghQAAAAA+ESQAgAAAACfCFIAAAAA4BNBCgAAAAB8IkgBAAAAgE8EKQAAAADwiSAFAAAAAD4RpAAAAADAJ4IUAAAAAPhEkAIAAAAAnwhSAAAAAOATQQoAAAAAfCJIAQAAAIBPBCkAAAAA8IkgBQAAAAA+EaQAAAAAwCeCFAAAAAD4lOz3BjhwGzdutG3btsV6MxBFVlZW2DniS1pamlWtWjXWmwEAABBEkCrCEHX3XXfbrt27Yr0p2IcJEybEehMQRUpyig0ZOoQwBQAA4gZBqoioJUoh6ryKteyg0mVivTlAsbF+z057Z8tq9xkiSAEAgHhBkCpiClEZKWVjvRkAAAAA8oFiEwAAAADgEy1SAAAARYwCVPGJ4lPxLy2OClARpAAAAIoQBajiH8Wn4ldKHBWgIkgBAAAUIQpQASWjABVBCgAAIAYoQAUUbxSbAAAAAACfCFIAAAAA4BNBCgAAAAB8IkgBAAAAgE8EKQAAAADwiSAFAAAAAD4RpAAAAADAJ4IUAAAAAPhEkAIAAAAAnwhSAAAAAOATQQoAAAAAfCJIAQAAAIBPBCkAAAAA8IkgBQAAAAA+EaQAAAAAwCeCFAAAAAD4lOz3Bsif9bt3xHoTgGKFzwwAAIhHBKki9s7WrFhvAgAAAIB8IkgVsfMqZNhByamx3gygWLVIcQACAADEG4JUEVOIykgpG+vNABBDGzdutG3btsV6MxAhKysr7BzxJS0tzapWrRrrzQCAIIIUABRxiLr7rrtt1+5dsd4U5GLChAmx3gREkZKcYkOGDiFMAYgbBCkAKEJqiVKIOq9iLTuodJlYbw5QLKzfs9Pe2bLafX4IUgDiBUEKAGJAIYpuvgAAFF/MIwUAAAAAPhGkAAAAAMAnghQAAAAA+ESQAgAAAACfCFIAAAAA4BNBCgAAAAB8ovw5AABADKzfvSPWmwAUK+vj7DNDkAIAAIiBd7ZmxXoTAOQDQQoAACAGzquQYQclp8Z6M4Bi1SL1ThwdgCBIAQAAxIBCVEZK2VhvBoADRLEJAAAAAPCJIAUAAAAAxSlIff7553buueda7dq1LSkpySZPnhx2fSAQsGHDhlmtWrWsXLly1r59e1u2bFnYOhs3brTu3btbenq6Va5c2fr27Wtbt24t4mcCAAAAIJHENEht27bNjjnmGHviiSeiXj9q1CgbM2aMPf300/b1119bWlqadezY0bZv3x5cRyFq8eLFNn36dJs6daoLZ1dccUURPgsAAAAAiSamxSbOOussd4pGrVGPPPKIDRkyxM4//3y3bMKECVazZk3XctWtWzf74Ycf7MMPP7S5c+daixYt3DqPPfaYnX322fbggw+6li4AAAAASJgxUsuXL7esrCzXnc9TqVIla9mypc2ZM8dd1rm683khSrR+qVKlXAtWbnbs2GHZ2dlhJwAAAAAo9kFKIUrUAhVKl73rdF6jRo2w65OTk61q1arBdaIZOXKkC2XeKTMzs1CeAwAAAICSKW6DVGEaPHiwbd68OXhauXJlrDcJAAAAQDESt0EqIyPDna9ZsyZsuS571+l87dq1Ydfv3r3bVfLz1okmNTXVVfkLPQEAAABAsSg2sS/16tVzYWjGjBnWrFkzt0xjmTT2qX///u5yq1atbNOmTTZ//nxr3ry5W/bJJ5/Y3r173ViqeLR+z85YbwJQrPCZAQAA8SimQUrzPf38889hBSYWLlzoxjjVqVPHrr/+erv77rutQYMGLlgNHTrUVeLr3LmzW79hw4Z25plnWr9+/VyJ9F27dtk111zjKvrFW8U+lW5PSU6xd7asjvWmAMWOPjv6DAEAAMSLmAapefPmWZs2bYKXBw4c6M579epl48ePt0GDBrm5pjQvlFqeWrdu7cqdly1bNnibl19+2YWndu3auWp9Xbt2dXNPxRuFwyFDh7jng/ij4iQqr9+zZ899dgtFbChE6TMEAAAQL2IapE4//XQ3X1RukpKSbMSIEe6UG+1cvfLKK1YcaFvZGYxvClFUcQQAAECxLTYBAAAAAPGKIAUAAAAAPhGkAAAAAKCklD8HgJJs/e4dsd4EoNjg8wIgHhGkACAG3tmaFetNAAAA+UCQAoAYOK9Chh2UnBrrzQCKTYsUBx8AxBuCFADEgEJURsr/nxMPAAAULxSbAAAAAACfCFIAAAAA4BNBCgAAAAB8IkgBAAAAgE8EKQAAAADwiSAFAAAAAD4RpAAAAADAJ+aRAgAAiIH1e3bGehOAYmV9nH1mCFIAAABFKC0tzVKSU+ydLatjvSlAsZOSnOI+Q/GAIAUAAFCEqlatakOGDrFt27bFelMQISsryyZMmGA9e/a0jIyMWG8OolCI0mcoHhCkAAAAiph2BONlZxA5KURlZmbGejMQ5yg2AQAAAAA+EaQAAAAAwCeCFAAAAAD4RJACAAAAAJ8IUgAAAADgE0EKAAAAAHwiSAEAAACATwQpAAAAAPCJIAUAAAAAPhGkAAAAAMAnghQAAAAA+ESQAgAAAACfkv3eAACQf+v37Iz1JgDFBp8XAPGIIAUARSgtLc1SklPsnS2rY70pQLGiz40+PwAQLwhSAFCEqlatakOGDrFt27bFelMQISsryyZMmGA9e/a0jIyMWG8OIihE6fMDAPGCIAUARUw7g+wQxi+FqMzMzFhvBgAgzlFsAgAAAAB8IkgBAAAAgE8EKQAAAADwiSAFAAAAAD4RpAAAAADAJ4IUAAAAAPhEkAIAAAAAnwhSAAAAAOATQQoAAAAAfCJIAQAAAIBPBCkAAAAA8IkgBQAAAAA+EaQAAAAAwCeCFAAAAAD4RJACAAAAAJ8IUgAAAADgE0EKAAAAAHwiSAEAAACATwQpAAAAAPCJIAUAAAAAPiX7vQEgO3futDVr1lhJkZWVFXZeEtSsWdPKlCkT680AAAAokQhSOCAKUaNGjbKSZsKECVZSDBo0yDIzM2O9GQAAACUSQQoH3NqhHXXE998IAAAAhYMghQOiLmO0dgAAACBRUWwCAAAAAHwiSAEAAACATwQpAAAAAPCJIAUAAAAAPhGkAAAAAMAnqvYBAADggO3cudPNL1kSZGVlhZ2XpClRVHEZBYsgBQCwRN95EnaggAOj74FRo0ZZSTJhwgQrSTT3J9PWFLykQCAQsASXnZ1tlSpVss2bN1t6enqsNwcAioWVK1eWuJ2nkogdKBS2knZQpSTigErhZAOCFEEKAA4IO0/FAztQAFA42YCufQCAA6Kdc1o6AACJiqp9AAAAAOATQQoAAAAAfCJIAQAAAIBPBCkAAAAA8IkgBQAAAAA+EaQAAAAAIFGD1BNPPGGHHnqolS1b1lq2bGnffPNNrDcJAAAAQAlVIoLU66+/bgMHDrThw4fbggUL7JhjjrGOHTva2rVrY71pAAAAAEqgEhGkRo8ebf369bPLLrvMGjVqZE8//bSVL1/exo0bF+tNAwAAAFACFfsgtXPnTps/f761b98+uKxUqVLu8pw5c6LeZseOHZadnR12AgAAAICECVLr16+3PXv2WM2aNcOW63JWVlbU24wcOdIqVaoUPGVmZhbR1gIAAAAoCYp9kDoQgwcPts2bNwdPK1eujPUmAQAAAChGkq2YO+igg6x06dK2Zs2asOW6nJGREfU2qamp7gQAAAAACdkiVaZMGWvevLnNmDEjuGzv3r3ucqtWrWK6bQAAAABKpmLfIiUqfd6rVy9r0aKFnXDCCfbII4/Ytm3bXBU/AAAAAChoJSJIXXTRRbZu3TobNmyYKzDRrFkz+/DDD3MUoAAAAACAgpAUCAQCluBU/lzV+1R4Ij09PdabAwAAACDOs0GJaJHKLy9LMp8UAAAAkNiy/y8T7K+9iSBlZlu2bHHnzCcFAAAAwMsIapnKDV37/q/K36pVq6xixYqWlJQU681BjI48KEhrTjG6dwKJie8BAHwPQBSPFKJq165tpUrlXuScFinVgC9Vyg455JBYbwbigL40+eIEEhvfAwD4HkClfbRElZh5pAAAAACgqBGkAAAAAMAnghRgZqmpqTZ8+HB3DiAx8T0AgO8B+EGxCQAAAADwiRYpAAAAAPCJIAUAAAAAPhGkAAAAAMAnghRKtE8//dSeeuqpWG8GAAAopvsRSUlJtmnTpn2ud+ihh9ojjzwSvJyVlWVnnHGGpaWlWeXKlYtgSxELBCmUWL/++qtdeumldvzxxxfZY/JFChSN3r17u52b++67L2z55MmT3fKC8ttvv7n7W7hwYYHdJ4DC+07QqUyZMnb44YfbiBEjbPfu3fm635NOOslWr14dnJx1/PjxUX/P586da1dccUXw8sMPP+xup++On376KV/bgPhFkEKJ3HHasWOHdevWzcaOHWstWrQo8G3hixSIvbJly9r9999vf/31V6w3xXbu3BnrTQAS3plnnul+c5ctW2Y33nij3XHHHfbAAw/k6z4VyjIyMvZ7gKZ69epWvnz54OVffvnFmjdvbg0aNLAaNWrkaxsQvwhSKJE7Tpr/4ZtvvrGzzjrLihJfpEDRad++vdvBGTlyZK7rfPnll3bKKadYuXLlLDMz06699lrbtm1b8HrtHOlgTCgdJNHBEqlXr547P/bYY926p59+evDATufOne2ee+6x2rVr25FHHumWr1y50v7973+7+6hataqdf/75rlULQOHTb7++E+rWrWv9+/d33xHvvPOO22fo2bOnValSxf1Ga99AYcvz+++/27nnnuuuVw+Sxo0b2/vvv5+ja5/+f9lll9nmzZuDrV8Ka5E9UvT/SZMm2YQJE9w6+r5AyUSQQsLtOOloVadOndz12kl65ZVXcnTJGz16tDVp0sR9oeo+rrrqKtu6dau7ji9SID6ULl3a7r33Xnvsscfsjz/+yHG9DmToCHXXrl3tu+++s9dff919P1xzzTV5fgwdkJGPP/7YfXe89dZbwetmzJhhS5cutenTp9vUqVNt165d1rFjR6tYsaJ98cUXNmvWLKtQoYLbBlqsgKKn33l99vT7O2/ePBeq5syZY5pC9eyzz3afWbn66qtdT5bPP//cFi1a5A7Y6rMbrZuffuPT09Pd94FON910U9TeKfrc66CK1nn00UeL5Pmi6BGkkHA7TjoqtWrVKheIFHT++9//2tq1a8Pup1SpUjZmzBhbvHixvfDCC/bJJ5/YoEGD3HV8kQLxo0uXLtasWTMbPnx4jut0wKV79+52/fXXu1ZhfXb1udbBje3bt+e5lVmqVavmDuKolcmjAy3PPvusO3qtk75v9u7d65bpQEzDhg3t+eeftxUrVrjvGwBFQ0FJBz8++ugjq1OnjgtQ+lzqIOsxxxxjL7/8sv3555/B1mh9Rk8++WT3uT3ssMPsnHPOsVNPPTVqNz+NldLBUX0f6BQtcOl7Q61jCnJaxxtfhZInOdYbAORnx+m5557LdcdJtPOkHafTTjvNVe9TFxt9uSrkeGOn9OWq9UJ5t/dalu6++2678sor7cknn8zxRZqbyC9SAIVDR4/btm2b44DGt99+6w6oaKcpdAdLYWf58uUu6OSHdrr0fRD6eD///LNrkQql0KaDPAAKl1qGFWzU0qTP+SWXXGIXXHCBW96yZcvgejowou64P/zwg7usnivqCjht2jTX80UHY5s2bRrDZ4LigiCFhNpxUsGH5ORkO+6444LXq7KP+kWHUthSKPvxxx8tOzvbVf3RztDff/8dNgYKQOzpyLG61A0ePDisC6264/7nP/9xO0mRdJRadEBE3xGhvO4++6MWqVB6PI2JDP3+iWzZAlB42rRp4w6a6gCHxi7q916tUftz+eWXu++Q9957z4Up/f4/9NBDNmDAgCLZbhRfdO1Dsd9xCuXtOKlKnndSuNKg0vr16+fpvtVqpWZ9HY1S17/58+fbE0884a5jnAMQn1TN891333XjHzw6YLJkyRJ3sCTy5LUkKeCo661H3xU6YOLx1tuzZ89+t0GPp9ursEzk49G1Byh8Orihz5sOlChEiVqedTD066+/Dq63YcMGN76xUaNGwWUaD62eJxoHqYp/qvobjb4T8vJ9gMRAkEJC7TipKV9fqP/73/+Ct1FXnNAqgApOasHS0agTTzzRjjjiCDemKhRfpEB8UTc7detVV17PLbfcYrNnz3ZjJHVQRSFnypQpYWMm1bL9+OOPu+8EDUbXjlRKSkrweoUidc/98MMPbc2aNa7ITG70+AcddJCr1KdiE2oF19gotYhFG9MJoPCp674+k/369XNjpnVwVXNMHnzwwW65151f46n0mV2wYIHNnDkz166/6u6vg7YqNrN+/fqwAy9IPAQpJNSO01FHHeX6P2uuJ1Xj0s6T/q8dJW+OCIUude1RQQtN6vviiy/a008/HfbYfJEC8UeTb+ogiEetyp999pnr0qtB5iphPmzYMNflx6MDJjoSres1nkLdhUO77+qotr5jnnnmGXc7b8crGt1OVb90NFzjMrQj1rdvX9ctWMVpAMSGir6o2616m7Rq1cp151V5c++giQ6MqnKfPrMqEqUDqBoTHY2K1uiAy0UXXeRatEeNGlXEzwbxJCkQ2TkciFMa+6B5HELnfFE3PLUyqcud91ZWIYnbb789WOJUXfr0hXfbbbe569WNRzs3qsTnlVLX0SjthKlboDeRribx0+OpG6ECm6r9qeXKm4hXA1MnTpzougio8IVKoCtg6b68YhWaZyZ0ThoAAACUDAQpJDx1udERaRWYaNeuXaw3BwAAAMUAQQoJRy1R6panroFqndL8UJpPQt1/QsdGAAAAALmh/DkSjsY/qZufxj9pvhf1d1a5YkIUAAAA8ooWKQAAAADwiap9AAAAAOATQQoAAAAAfCJIAQAAAIBPBCkAAAAA8IkgBQBIWJ9++qklJSW5ybcBAPCDIAUAiLl169ZZ//79rU6dOpaammoZGRnWsWNHmzVrVoE9xumnn27XX3992DJNf6D55CpVqmSx1rt3b+vcuXOsNwMAkEfMIwUAiLmuXbvazp077YUXXrDDDjvM1qxZYzNmzLANGzYU6uOWKVPGhTYAAPyiRQoAEFPqVvfFF1/Y/fffb23atLG6devaCSecYIMHD7bzzjsvuM7ll19u1atXt/T0dGvbtq19++23wfu44447rFmzZvbiiy/aoYce6lqYunXrZlu2bAm29nz22Wf26KOPuq58Ov322285uvaNHz/eKleubFOnTrUjjzzSypcvb//617/s77//diFP912lShW79tprbc+ePcHH37Fjh91000128MEHW1pamrVs2dLdt8e7348++sgaNmxoFSpUsDPPPNO1hnnbr/ufMmVKcPtCbw8AiD8EKQBATClU6DR58mQXSKK58MILbe3atfbBBx/Y/Pnz7bjjjrN27drZxo0bg+v88ssv7j4UgnRScLrvvvvcdQpQrVq1sn79+rnwolNmZmbUx1JoGjNmjL322mv24YcfukDTpUsXe//9991JYe2ZZ56xN998M3iba665xubMmeNu891337ntVVBatmxZ2P0++OCD7vaff/65rVixwoUv0fm///3vYLjSSd0OAQDxiyAFAIip5ORk12KjFhm12px88sl22223uUAiX375pX3zzTc2ceJEa9GihTVo0MAFEq0bGmb27t3r7ufoo4+2U045xXr06OG6B4paqNSNTy1M6sqnU+nSpaNuz65du+ypp56yY4891k499VTXIqVteO6556xRo0Z2zjnnuJazmTNnuvUViJ5//nm3fXrc+vXru2DUunVrtzz0fp9++mn3HBQEFb687VOQLFeuXHB8mE7aXgBA/GKMFAAgLsZIderUyXXx++qrr1zL06hRo+zZZ5+1bdu22datW61atWpht/nnn39cK5RH3e4qVqwYvFyrVi3XiuWXwpbCkKdmzZruvhV2Qpd5971o0SLXze+II44Iux+1roVuc+T9Huj2AQDiA0EKABAXypYta2eccYY7DR061I2JGj58uF111VUudEQbM6RWKU9KSkrYdRpnpFYqv6Ldz77uWyFPrVvqchjZyhUavqLdRyAQ8L19AID4QJACAMQldaPTmCd1g8vKynJdANUydKDUVS60QERBURdA3a9al9S1L962DwBQOBgjBQCIKZU4VxW+l156yY2LWr58uRtvpK59559/vrVv394VitAcS9OmTXPV9mbPnm233367zZs3L8+PoxD29ddfu9uvX7/+gFqrolGXvu7du1vPnj3trbfectuvMV0jR4609957z9f26fkvXbrUbZ/GVAEA4hdBCgAQU+r+pnLhDz/8sCvuoGIR6tqnCnuPP/646wKnanm67rLLLnPBRaXNf//9dzdWKa9UAEJd79TSpTLqKhJRUFRUQkHqxhtvdGXTFfrmzp3rJhjOKz1f3VbFKLR9BTkZMQCg4CUF6KANAAAAAL7QIgUAAAAAPhGkAAAAAMAnghQAAAAA+ESQAgAAAACfCFIAAAAA4BNBCgAAAAB8IkgBAAAAgE8EKQAAAADwiSAFAAAAAD4RpAAAAADAJ4IUAAAAAPhEkAIAAAAA8+f/AfFTxFyED/4xAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Statistiques par sentiment:\n", "Négatif:\n", "  Moyenne: 133.3 tokens\n", "  Médiane: 126.0 tokens\n", "  Min: 27 tokens\n", "  Max: 512 tokens\n", "Neutre:\n", "  Moyenne: 94.9 tokens\n", "  Médiane: 87.0 tokens\n", "  Min: 15 tokens\n", "  Max: 398 tokens\n", "Positif:\n", "  Moyenne: 117.0 tokens\n", "  Médiane: 111.0 tokens\n", "  Min: 17 tokens\n", "  Max: 371 tokens\n"]}], "source": ["# First, load the CSV data\n", "df_train = pd.read_csv(os.path.join(csv_export_dir, \"train_processed.csv\"))\n", "\n", "# Create the boxplot using the correct column name 'token_length'\n", "plt.figure(figsize=(10, 6))\n", "sns.boxplot(x='label', y='token_length', data=df_train)\n", "plt.title('Longueur des Textes par Sentiment - Train Set')\n", "plt.xlabel('Sentiment')\n", "plt.ylabel('Nombre de tokens')\n", "plt.xticks([0, 1, 2], ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>ti<PERSON>'])\n", "plt.show()\n", "\n", "# Optional: Add some statistics\n", "print(\"\\nStatistiques par sentiment:\")\n", "for label in [0, 1, 2]:\n", "    sentiment_name = ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>ti<PERSON>'][label]\n", "    subset = df_train[df_train['label'] == label]\n", "    print(f\"{sentiment_name}:\")\n", "    print(f\"  Moyenne: {subset['token_length'].mean():.1f} tokens\")\n", "    print(f\"  Médiane: {subset['token_length'].median():.1f} tokens\")\n", "    print(f\"  Min: {subset['token_length'].min()} tokens\")\n", "    print(f\"  Max: {subset['token_length'].max()} tokens\")"]}, {"cell_type": "code", "execution_count": 19, "id": "a26e7b39", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["delta 9 engages kindred for cannabis wholesale brokerage services - - delta 9 cannabis inc. ( tsx : dn ) ( otcqx : dltnf ) ( \" delta 9 \" or the \" company \" ), is pleased to announce that, through its wholly owned subsidiary delta 9 bio - tech inc., it has entered into a brokerage agreement ( the “ agreement ) with kindred partners inc. ( “ kindred ” ), to provide exclusive brokerage and sales services for delta 9 ’ s portfolio of branded cannabis products in canada.\n"]}], "source": ["dd = pd.read_csv(os.path.join(csv_export_dir, \"train_processed.csv\"))\n", "print(dd['text'][0])"]}, {"cell_type": "markdown", "id": "a2241943", "metadata": {}, "source": ["## **Résumé du Prétraitement - Splits Originaux**\n", "\n", "Ce notebook a implémenté avec succès le prétraitement en utilisant **uniquement les splits originaux** fournis par Hugging Face :\n", "\n", "### **✅ Étapes Complétées :**\n", "\n", "1. **Chargement du Dataset** : Utilisation des splits originaux train/test sans modification\n", "2. **Vérification des Caractéristiques** : Ana<PERSON>se complète des deux splits\n", "3. **Analyse de Distribution** : Comparaison des distributions de labels entre train et test\n", "4. **Nettoyage des Données** : Suppression des balises HTML, caractères spéciaux, gestion des valeurs nulles\n", "5. **Tokenisation** : Utilisation du tokenizer FinBERT avec analyse des longueurs optimales\n", "6. **Formatage PyTorch** : Configuration pour l'entraînement avec tensors PyTorch\n", "7. **Sauvegarde** : Datasets et métadonnées sauvegardés dans un dossier séparé\n", "\n", "### **📊 Configuration Finale :**\n", "- **Splits** : Train/Test originaux (pas de validation séparée)\n", "- **Tokenizer** : FinBERT (ProsusAI/finbert)\n", "- **<PERSON><PERSON><PERSON> maximale** : 512 tokens\n", "- **Format** : PyTorch tensors\n", "- **Colonnes** : input_ids, attention_mask, labels\n", "\n", "### **🎯 Avantages de cette approche :**\n", "- **Simplicité** : Utilise directement la structure originale du dataset\n", "- **Plus de données d'entraînement** : Toutes les données train sont utilisées pour l'entraînement\n", "- **Évaluation standardisée** : Le split test reste intact pour une évaluation comparable\n", "- **Reproductibilité** : Suit la configuration standard du dataset\n", "\n", "**🚀 Les datasets sont maintenant prêts pour l'entraînement du modèle FinBERT avec les splits originaux !**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}